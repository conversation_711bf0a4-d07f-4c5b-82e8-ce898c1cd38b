from django.utils.datastructures import MultiValueDictKeyError
from django.conf import settings
from django.db.models import Q
import requests

from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from cards.models import *
from cards.tasks import receive_trans_created, send_default_pin_task, process_refund_trans_task

from cards.serializers import (
    CreatePhysicalVirtualCardSerializer,
    ViewCardListDetailSerializer, 
    ListCardsIDSerializer, 
    UserTransIDSerializer,
    CardSettingSerializer,
    CardIDSerializer,
    NewPhysicalCardMappingViewSerializer,
    ViewRequestCardSerializer,
    FormatCardDataSerializer
)

from accounts.models import WalletSystem, SendMoneyDumpData, OtherServiceDetail, Escrow, AccountSystem
from accounts.helpers.helper_func import notify_admin_whatsapp_on_new_cards_requests
from main.helper.logging_utils import log_info, log_debug

from main.helper.send_emails import send_email
from main.models import User, DeliveryAddressData, ConstantTable
from main.serializers import User, ResetPINSerializer
from main.helper.utils import get_ip_address
from main.permissions import HasKYCLevelTwo, CanSendMoney, HasTransactionPin, CheckWalletAvailable, CustomIsAuthenticated, AdminLockPermission, CheckDynamicAuthentication



from send_money.helpers.helper_functions import calculate_transaction_limit
import json


# Create your views here.
                
class CardPricingAPIView(APIView):
    permission_classes = [CheckDynamicAuthentication]


    def get(self, request):

        request_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        physical_card_issuance_fee = ConstantTable.get_constant_table_instance().physical_card_initialize_amount
        virtual_card_issuance_fee = ConstantTable.get_constant_table_instance().virtual_card_initialize_amount
        delivery_fee = ConstantTable.get_constant_table_instance().card_delivery_fee
        
        response = {
            "physical_card_issuance_fee": physical_card_issuance_fee,
            "virtual_card_issuance_fee": virtual_card_issuance_fee,
            "delivery_fee": delivery_fee
        }
        return Response(response, status=status.HTTP_200_OK)


class CreatePhysicalVirtualCardView(APIView):
    permission_classes = [CheckDynamicAuthentication, HasKYCLevelTwo, CanSendMoney, HasTransactionPin, CheckWalletAvailable]
    serializer_class = CreatePhysicalVirtualCardSerializer

    def post(self, request):

        # request_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        request_user = request.user

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        from_wallet_type = serializer.validated_data["from_wallet_type"]
        get_card_type = serializer.validated_data.get("card_type")
        get_card_brand = serializer.validated_data.get("card_brand")
        delivery_address_id = serializer.validated_data.get("delivery_address_id")
        transaction_pin = serializer.validated_data["transaction_pin"]
        unique_reference = serializer.validated_data.get("unique_reference")


        if unique_reference and RequestCardData.objects.filter(unique_reference=unique_reference).exists():
            response = {
                "error": "898",
                "message": "Request Data Already Exists"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
            
            

        if get_card_brand:
            brand_choice_result = [item for item in self.serializer_class.CARD_BRAND_CHOICES if item[0] == get_card_brand]
            card_brand_num = get_card_brand
            card_brand_choice = brand_choice_result[0][1]
        else:
            card_brand_num = 1
            card_brand_choice = "Verve"


        if get_card_type == "PHYSICAL":
            if card_brand_num != 1:
                response = {
                    "error": "898",
                    "message": "We only give out Verve Cards for now."
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            

            if delivery_address_id is None:
                response = {
                    "error": "899",
                    "message": "you must provide address for a physical card"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                if delivery_address_id is None:
                    response = {
                        "status": "error",
                        "message": "No delivery_address_id attached"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                

                check_address = DeliveryAddressData.objects.filter(user=request_user, id=delivery_address_id).last()
                if not check_address:
                    response = {
                        "error": "896",
                        "message": "Invalid Address"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                

            get_other_cards_exist = CustomerCardDetail.objects.filter(customer__user=request_user, card_brand__iexact=card_brand_choice, card_type__iexact=get_card_type, is_canceled=False).exists()
            if get_other_cards_exist and not OtherServiceUsersGateway.objects.filter(user=request_user, is_active=True).exists():
                response = {
                    "error": "896",
                    "message": "Cannot Request for another card whille previous card is active"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
                
                   
            transfer_type="PHYSICAL_CARD_CREATE"
            initialization_amount = ConstantTable.get_constant_table_instance().physical_card_initialize_amount
            
            
        else:
            delivery_address_id = None 
            transfer_type="VIRTUAL_CARD_CREATE"
            initialization_amount = ConstantTable.get_constant_table_instance().virtual_card_initialize_amount
           
        dump_data = serializer.validated_data
        del dump_data["transaction_pin"]

        payload_saved = SendMoneyDumpData.objects.create(
            user=request_user,
            transfer_type=transfer_type,
            dump=dump_data
        )

        log_info(str(get_card_type))
        # Check Transaction Pin

        chcek_pin = User.check_sender_transaction_pin(
            user=request_user, pincode=transaction_pin
        )
        if chcek_pin == False:
            retries = User.count_down_transaction_pin_retries(request_user)

            response = {
                "error": "error",
                "message": "Incorrect Pin",
                "retry_count": retries["retry_count"],
                "remaining_retries": retries["remaining_retries"],
            }

            return Response(response, status=status.HTTP_401_UNAUTHORIZED)

        else:
            User.reset_transaction_pin_retries(request_user)

            wallet_instance = WalletSystem.get_wallet(user=request_user, from_wallet_type=from_wallet_type)

            service_user_check = OtherServiceDetail.objects.filter(service_name="CARDS").last()
            if not service_user_check:
                response = {
                    "error": "890",
                    "message": f"Cannot Process {get_card_type.lower()} card Request At The Time"
                }
                return Response(response, status=status.HTTP_401_UNAUTHORIZED)


            cards_user = service_user_check.user

            receiver_wallet_instance = WalletSystem.get_wallet(user=cards_user, from_wallet_type="COLLECTION")
            if not receiver_wallet_instance:
                response = {
                    "error": "565",
                    "message": f"An Error Occured and Card request cannot be processed at the time"
                }

                return Response(response, status=status.HTTP_400_BAD_REQUEST)


            # Main Check Balance Before Transfer
                
            total_amount_charged = initialization_amount

            get_user_wallet_balance = WalletSystem.check_wallet_balance(
                amount = total_amount_charged,
                wallet_instance = wallet_instance
            )


            if get_user_wallet_balance["status"] == False:
                response = {
                    "error": True,
                    "success": False,
                    "message": f"You do not have sufficient balance to make this transaction of {total_amount_charged}. Your balance is {wallet_instance.available_balance}",
                }

                return Response(response, status=status.HTTP_403_FORBIDDEN)

            else: 

                # Debit Sender with amount due
                deduct_balance = WalletSystem.deduct_balance(
                    user=request_user,
                    wallet=wallet_instance,
                    amount=total_amount_charged,
                    trans_type=transfer_type
                )

                payload_saved.money_removed = True
                payload_saved.save()     


                # GET IP ADDRESS
                ip_addr = get_ip_address(request)

                pay_for_card = SudoCardTransaction.initialize_card_payment(
                    request_user = request_user,
                    cards_user = cards_user,
                    total_amount_charged = total_amount_charged,
                    ip_addr = ip_addr,
                    deduct_balance = deduct_balance,
                    wallet_instance = wallet_instance,
                    receiver_wallet_instance = receiver_wallet_instance,
                    payload_saved = payload_saved,
                    transfer_type = transfer_type,
                    unique_reference = unique_reference
                )
                

                escrow_instance = pay_for_card["escrow_instance"]
                sender_transaction = pay_for_card["sender_transaction"]



                create_user_card = SudoHelper.create_old_new_card(
                    request_user = request_user,
                    card_type = get_card_type,
                    ip_addr = ip_addr,
                    delivery_address_id = delivery_address_id,
                    card_brand = card_brand_choice
                )
                
            
                if create_user_card is not None:

                    release_to_cards = WalletSystem.release_fund_wallet_pay_buddy_for_receiver(
                        sender_transaction = sender_transaction,
                        sender_user_instance = request_user,
                        buddy_user_instance = cards_user,
                        sender_wallet = wallet_instance,
                        wallet = receiver_wallet_instance,
                        escrow_instance = escrow_instance,
                        customer_reference=None
                    )

                    if not get_card_type == "VIRTUAL":
                        RequestCardData.objects.create(
                            customer = create_user_card,
                            card_type = get_card_type,
                            card_brand = card_brand_choice,
                            delivery_address_id = delivery_address_id,
                            unique_reference = unique_reference,
                            date_requested = datetime.now()
                        )

                    if settings.ENVIRONMENT == "development":

                        import random

                        # generate 16 digit card pan
                        first_fake_card_pan = ''.join(str(random.randint(0, 9)) for _ in range(16))

                        fake_cards_data = dict(
                            fake_card_pan = first_fake_card_pan,
                            fake_card_first_six = first_fake_card_pan[:6],
                            fake_card_last_four = first_fake_card_pan[-4:],
                            fake_card_expiry_month = "07",
                            fake_card_expiry_year = "29",
                            unique_reference = unique_reference,
                        )

                        PhysicalCard.objects.create(
                            pan_number = fake_cards_data["fake_card_pan"],
                            card_first_six = fake_cards_data["fake_card_first_six"],
                            card_last_four = fake_cards_data["fake_card_last_four"],
                            card_expiry_month = fake_cards_data["fake_card_expiry_month"],
                            card_expiry_year = fake_cards_data["fake_card_expiry_year"]
                        )


                    else:

                        fake_cards_data = None

                        # card_id = create_user_card.get("data", {}).get('_id')
                        # if card_id:

                        #     send_default_pin_task.apply_async(
                        #         queue="resolvecardpur",
                        #         kwargs={
                        #             "card_id": card_id
                        #         }
                        #     )


                        for email in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                            email_text = f"User with email: {request_user.email} and names: {request_user.bvn_full_name} has requested for a {get_card_type} card.\n\nPlease confirm"
                            send_email(email=email, passcode=email_text)
                            
                        for num in ["2347039516293", "2348077469471", "2348167631246", "2348031346306", "2348129259458", "2348090643057"]:
                            notify_admin_whatsapp_on_new_cards_requests(phone_number=num, message=email_text)

                        
                    response = {
                        "status": "success",
                        "message": "card created successfully",
                        "fake_cards_data": fake_cards_data
                    }
                    return Response(response, status=status.HTTP_200_OK)

                else:

                    # Refund Money
                    reversal_transaction = Transaction.reverse_bills_airpin_transactions(transaction=sender_transaction, provider_status="FAILED", payload_reversal_reason=create_user_card)


                  
                    if settings.ENVIRONMENT == "development":

                        response = {
                            "status": "success",
                            "message": "card created successfully"
                        }
                        return Response(response, status=status.HTTP_200_OK)


                    response = {
                        "error": "565",
                        "message": "Could not create card at the moment, please again later"
                    }

                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

    

class NewPhysicalCardMappingView(APIView):
    permission_classes = [CheckDynamicAuthentication, HasKYCLevelTwo, CanSendMoney, HasTransactionPin, CheckWalletAvailable]
    serializer_class = NewPhysicalCardMappingViewSerializer

    def post(self, request):
        request_user = request.user

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        card_pan = serializer.validated_data["card_pan"]
        card_expiry_month = serializer.validated_data["card_expiry_month"]
        card_expiry_year = serializer.validated_data["card_expiry_year"][-2:]
        unique_reference = serializer.validated_data.get("unique_reference")


        log_info(str(card_pan))
        log_info(str(card_expiry_month))
        log_info(str(card_expiry_year))
        log_info(str(unique_reference))

        log_info(str(type(card_pan)))
        log_info(str(type(card_expiry_month)))
        log_info(str(type(card_expiry_year)))
        log_info(str(type(unique_reference)))

        card_first_six = card_pan[:6]
        card_last_four = card_pan[-4:]

        if unique_reference == "AJO_PLAN":
            try:
                get_card_id = CustomerCardDetail.objects.get(card_masked_pan=f"{card_first_six}******{card_last_four}", card_expiry_month=card_expiry_month, card_expiry_year=card_expiry_year)

                response = {
                    "status": "success",
                    "message": "card id found",
                    "card_id": get_card_id.card_id
                }

                return Response(response, status=status.HTTP_200_OK)
                
            
            except:

                response = {
                    "status": "error",
                    "message": "no card id found",
                    "card_id": None
                }

                return Response(response, status=status.HTTP_404_NOT_FOUND)
            

        other_service_det = OtherServiceDetail.objects.filter(user=self.request.user, service_name__in=["LOTTO_PLAY", "SAVINGS"]).first()
        if other_service_det and not unique_reference:

            response = {
                "status": "error",
                "message": "must pass unique reference",
                "card_id": None
            }

            return Response(response, status=status.HTTP_404_NOT_FOUND)

        ip_addr = get_ip_address(request)

        response = PhysicalCard.map_physical_card_start(
            request_user, card_pan, card_first_six, card_last_four, card_expiry_month, card_expiry_year, ip_addr, unique_reference
        )

        if response.get("status") == "00":
            return Response(response, status=status.HTTP_200_OK)
        else:
            return Response(response, status=status.HTTP_400_BAD_REQUEST)



        # get_entered_pan_hash = SudoHelper.encrypt_pan_num(pan_number=card_pan)

        # filter_query = Q(card_first_six=card_first_six) & Q(card_last_four=card_last_four) & Q(pan_number=get_entered_pan_hash) & Q(card_expiry_month=card_expiry_month) & Q(card_expiry_year=card_expiry_year)

        # get_request_object = RequestCardData.objects.filter(customer__user=request_user, assigned=False, card_type="PHYSICAL").last()

        # physical_card = PhysicalCard.objects.exclude(card_status="ASSIGNED").filter(customer__user=request_user, card_status="NOT_ASSIGNED").filter(filter_query).last()


        # if settings.ENVIRONMENT == "development":
        #     response = {
        #         "status": "00",
        #         "message": "Card Successfully Registered"
        #     }

        #     return Response(response, status=status.HTTP_200_OK)
        
        # if not get_request_object:
        #     response = {
        #         "error": "56",
        #         "message": "No Request Found"
        #     }
        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)
            
        # if physical_card and get_request_object:
        #     # GET IP ADDRESS
        #     ip_addr = get_ip_address(request)

        #     map_user_card = SudoHelper.map_customer_to_card(
        #         customer=get_request_object.customer,
        #         funding_source_id=SUDO_FUNDING_SOURCE_ID,
        #         card_type="PHYSICAL",
        #         ip_address=ip_addr,
        #         delivery_address_id=get_request_object.delivery_address_id,
        #         card_brand=get_request_object.card_brand,
        #         card_pan_number=card_pan
        #     )


        #     if map_user_card is not None:

        #         get_request_object.assigned = True
        #         get_request_object.assigned_card = physical_card
        #         get_request_object.save()

        #         physical_card.customer = get_request_object.customer
        #         physical_card.card_status = "ASSIGNED"
        #         physical_card.date_assigned = datetime.now()
        #         physical_card.save()


        #         card_id = map_user_card.get("data", {}).get('_id')
        #         if card_id:

        #             send_default_pin_task.apply_async(
        #                 queue="resolvecardpur",
        #                 kwargs={
        #                     "card_id": card_id
        #                 }
        #             )

        #         response = {
        #             "status": "00",
        #             "message": "Card Successfully Registered"
        #         }

        #         return Response(response, status=status.HTTP_200_OK)

        #     else:
        #         response = {
        #             "error": "565",
        #             "message": "Could not assign card at the moment, please again later"
        #         }

        #         return Response(response, status=status.HTTP_400_BAD_REQUEST)
        # else:
        #     response = {
        #         "error": "565",
        #         "message": "Card Does not Exist"
        #     }

        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)




######################################################################################################################
# CARDS

class ViewCardListDetailsAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = ViewCardListDetailSerializer

    def get(self, request):
        request_user = request.user

        card_type = request.query_params.get('card_type')
        if card_type is None:
            response = {
                "error": "652",
                "message": "No card type attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        
        if card_type not in ["virtual", "physical"]:
            response = {
                "error": "653",
                "message": "invalid card type"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        cards_qs = CustomerCardDetail.objects.filter(customer__user=request_user, card_type__iexact=card_type.lower(), is_canceled=False)

        if cards_qs:
            serializer = self.serializer_class(cards_qs, many=True)

        response = {
            "status": "success",
            "message": f"{card_type.lower()} cards retreived successfully",
            "data": serializer.data if cards_qs else []
        }
        return Response(response, status=status.HTTP_200_OK)



class ViewRequestCardListDetailsAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = ViewRequestCardSerializer

    def get(self, request):
        user = request.user
        try:
            get_requests = RequestCardData.objects.filter(customer__user=user, card_id__isnull=True)
            serializer = self.serializer_class(get_requests, many=True)

            response = {
                "status": "success",
                "message": "Request card data retreived successfully",
                "data": serializer.data
            }
            return Response(response, status=status.HTTP_200_OK)

        except RequestCardData.DoesNotExist:
            response = {
                "error": "653",
                "message": "No Request Card Data",
                "data": []
            }
            return Response(response, status=status.HTTP_200_OK)
    
        except Exception as err:
            response = {
                "error": "654",
                "message": f"Error Occured - {err}"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)




class GetCardTokenAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = CardIDSerializer

    def get(self, request):

        card_id = request.query_params.get('card_id')
        if card_id is None:
            response = {
                "error": "652",
                "message": "No card id attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        
        # if card_type not in ["virtual", "physical"]:
        #     response = {
        #         "error": "653",
        #         "message": "invalid card type"
        #     }
        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)


        reveal_card = SudoHelper.get_card_token(card_id=card_id)
        
        
        response = {
            "status": "success",
            "data": reveal_card
        }
        return Response(response, status=status.HTTP_200_OK)


class RefundFailedCardCreation(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = UserTransIDSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():

            trans_id = serializer.validated_data["trans_id"]

            service_user_check = OtherServiceDetail.objects.filter(service_name="CARDS").last()
            if not service_user_check:
                response = {
                    "error": "890",
                    "message": f"Cards user does not exist"
                }
                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            cards_user = service_user_check.user
            
            get_trans = Transaction.objects.exclude(status__in=["FAILED", "SUCCESSFUL"]).filter(id=trans_id).last()
            if not get_trans:
                response = {
                    "error": "470",
                    "message": "Transaction does not exist or is already successful"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            
            else:
            
                user = get_trans.user

                # Check if cards trans exists
                get_cards_trans = Transaction.objects.filter(escrow_id=get_trans.escrow_id, user=cards_user).last()
                if get_cards_trans:
                    response = {
                        "error": "470",
                        "message": "Transaction already exists for cards user"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                
                else:
                    if DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=get_trans.transaction_id, user=user, entry="CREDIT").exists():
                        response = {
                            "error": "471",
                            "message": "There is an existing credit for this transaction"
                        }
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)
                    
                    else:
                        # Refund Money
                        reversal_transaction = Transaction.reverse_bills_airpin_transactions(transaction=get_trans, provider_status="FAILED")

                        # # Refund Money
                        # refund_request_user = WalletSystem.fund_balance(
                        #     user = user,
                        #     wallet = ,
                        #     amount = get_trans.amount,
                        #     trans_type = f"{get_trans.transaction_type}-FAILED_{get_trans.transaction_type}",
                        #     transaction_instance_id = get_trans.transaction_id
                        # )

                        # get_trans.balance_after = refund_request_user["wallet_instance"].available_balance
                        # get_trans.provider_status = "FAILED"
                        # get_trans.status = "FAILED"
                        # get_trans.save()


                        response = {
                            "status": "success",
                            "message": "Repayment processed successfully successfully",
                            # "data": {
                            #     "balance_before": refund_request_user["balance_before"],
                            #     "balance_after": refund_request_user["balance_after"]
                            # }
                        }
                        return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

###############################################################################################################3

# CARD Settings APIs

class CardSettingsAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = CardSettingSerializer
    
    def put(self, request):
        request_user = request.user

        card_id = request.query_params.get('card_id')
        if card_id is None:
            response = {
                "status": "error",
                "message": "No card id attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            get_int_id = int(card_id)
        except:
            response = {
                "error": "545",
                "message": "must supply integer for card_id"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():

            freeze = serializer.validated_data.get("freeze")
            cancel = serializer.validated_data.get("cancel")
            new_limit = float(serializer.data["new_limit"])
            

            if new_limit % 10000 != 0:

                response = {
                    "error": "545",
                    "message": "New limit must be mulitples of 10000"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        

            get_card = CustomerCardDetail.objects.filter(customer__user=request_user, id=get_int_id, is_canceled=False).last()
            if get_card:
                added_trans_limit = request_user.added_trans_limit

                if request_user.kyc_level == 1:
                    daily_limit = ConstantTable.get_constant_table_instance().kyc_one_daily_transfer_limit

                elif request_user.kyc_level == 2:
                    daily_limit = ConstantTable.get_constant_table_instance().kyc_two_daily_transfer_limit + added_trans_limit

                elif request_user.kyc_level == 3:
                    daily_limit = ConstantTable.get_constant_table_instance().kyc_three_daily_transfer_limit + added_trans_limit

                else:
                    daily_limit = ConstantTable.get_constant_table_instance().kyc_one_daily_transfer_limit


                if new_limit > daily_limit:
                    response = {
                        "error": "546",
                        "message": f"Cannot Upgrade to {new_limit} on KYC {request_user.kyc_level}"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                


                freeze_status = "canceled" if cancel is True else "inactive" if freeze is True else "active"


                update_card_settings = SudoHelper.update_card_settings(card_id=get_card.card_id, new_limit=new_limit, freeze_status=freeze_status)
                log_debug("---------------------------------------------")
                log_debug("---------------------------------------------")
                log_info("update_card_settings: ", update_card_settings)
                log_debug("---------------------------------------------")
                log_debug("---------------------------------------------")

                # update_card_settings:  {'status': False, 'data': '{\'statusCode\': 400, \'message\': "Can\'t update canceled card."}'}

                if update_card_settings["status"] == True:

                    get_card.daily_limit = new_limit
                    get_card.is_frozen = freeze
                    get_card.is_canceled = cancel
                    get_card.save()

                    response = {
                        "status": "success",
                        "message": "Card Settings Successfully Updated"
                    }
                    return Response(response, status=status.HTTP_200_OK)

                else:
                    response = {
                        "error": "545",
                        "message": "an error occured, please try again"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

        
            else:

                response = {
                    "error": "545",
                    "message": "card_id does not exist"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)
            
        return Response(serializer.errors, status=status.HTTP_404_NOT_FOUND)
        

###############################################################################################################3

# CARD PINS APIs

class SendDefaultCardPinView(APIView):
    permission_classes = [CustomIsAuthenticated]
    
    def put(self, request):
        request_user = request.user

        card_id = request.query_params.get('card_id')
        if card_id is None:
            response = {
                "status": "error",
                "message": "No card id attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            get_int_id = int(card_id)
        except:
            response = {
                "error": "545",
                "message": "must supply integer for card_id"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        

        get_card = CustomerCardDetail.objects.filter(customer__user=request_user, id=get_int_id).last()
        if get_card:
            if get_card.pin_changed == True:
                response = {
                    "error": "544",
                    "message": "pin already changed"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)


            send_out_default_pin = SudoHelper.send_default_card_pin(card_id=get_card.card_id)
            log_debug("---------------------------------------------")
            log_debug("---------------------------------------------")
            log_info("send_out_default_pin: ", send_out_default_pin)
            log_debug("---------------------------------------------")
            log_debug("---------------------------------------------")

            if send_out_default_pin["status"] == True:

                response = {
                    "status": "success",
                    "message": "Default Card Pin has been sent to your mobile phone"
                }
                return Response(response, status=status.HTTP_200_OK)

            else:
                response = {
                    "error": "545",
                    "message": "an error occured, please try again"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        
        else:

            response = {
                "error": "545",
                "message": "card_id does not exist"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)
    

class SendManyDefaultCardPinView(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = ListCardsIDSerializer
    
    def post(self, request):
        request_user = request.user

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            card_ids = serializer.validated_data["card_ids"]

            good_results = []
            error_results = []
            
            for data in card_ids:
                
                get_card = CustomerCardDetail.objects.filter(id=data).last()
                if get_card:
                    if get_card.pin_changed == True:
                        response = {
                            "error": "544",
                            "message": "pin already changed"
                        }
                        error_results.append(response)

                    send_out_default_pin = SudoHelper.send_default_card_pin(card_id=get_card.card_id)
                    log_debug("---------------------------------------------")
                    log_debug("---------------------------------------------")
                    log_info("send_out_default_pin: ", send_out_default_pin)
                    log_debug("---------------------------------------------")
                    log_debug("---------------------------------------------")

                    if send_out_default_pin["status"] == True:

                        response = {
                            "user_email": get_card.customer.user.email,
                            "status": "success",
                            "message": "Default Card Pin has been sent to your mobile phone"
                        }

                        good_results.append(response)

                    else:
                        response = {
                            "error": "545",
                            "message": "an error occured, please try again"
                        }

                        error_results.append(response)

            results = {
                "good_results": good_results,
                "error_results": error_results
            }

            return Response(results, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)



class ChangeCardPinView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = ResetPINSerializer

    def post(self, request):
        request_user = request.user

        card_id = request.query_params.get('card_id')
        if card_id is None:
            response = {
                "status": "error",
                "message": "No card id attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            get_int_id = int(card_id)
        except:
            response = {
                "error": "545",
                "message": "must supply integer for card_id"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            old_pin = serializer.validated_data["old_pin"]
            new_pin = serializer.validated_data["new_pin"]
            new_pin_retry = serializer.validated_data["new_pin_retry"]

            get_card = CustomerCardDetail.objects.filter(customer__user=request_user, id=get_int_id).last()
            if get_card:

                # if get_card.pin_changed == True:
                #     response = {
                #         "error": "544",
                #         "message": "pin already changed"
                #     }
                #     return Response(response, status=status.HTTP_400_BAD_REQUEST)
                
            
                if not old_pin.isnumeric():
                        response = {
                            "error_code": "14",
                            "message": "You must supply an integer for old pin"
                        }
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                if not new_pin.isnumeric():
                    response = {
                        "error_code": "14",
                        "message": "You must supply an integer for new pin"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                

                if new_pin != new_pin_retry:
                    response = {
                        "error_code": "14",
                        "message": "New Entered Pins do not match"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)


                if len(old_pin) != 4 or len(new_pin) != 4 or len(new_pin_retry) != 4:

                    response = {
                        "error": "995",
                        "message": "New and Old Transaction Pins must be four digits",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)


                if old_pin == new_pin:
                    response = {
                        "error": "958",
                        "message": "cannot change transaction pin to old pin",
                    }
                    return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)


                if new_pin == new_pin_retry:

            
                    send_out_default_pin = SudoHelper.change_card_pin(card_id=get_card.card_id, old_pin=old_pin, new_pin=new_pin)
                    log_debug("---------------------------------------------")
                    log_debug("---------------------------------------------")
                    log_info("send_out_default_pin: ", send_out_default_pin)
                    log_debug("---------------------------------------------")
                    log_debug("---------------------------------------------")

                    if send_out_default_pin["status"] == True:
                        get_card.pin_changed = True
                        get_card.save()

                        response = {
                            "status": "success",
                            "message": "Default Card Pin has been been changed"
                        }
                        return Response(response, status=status.HTTP_200_OK)

                    else:
                        response = {
                            "error": "544",
                            "message": "an error occured, please try again"
                        }
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    response = {
                        "error": "652",
                        "message": "Pins Entered Do Not Match",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
        
            else:

                response = {
                    "error": "545",
                    "message": "card_id does not exist"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    

class FormatPayloadView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = FormatCardDataSerializer

    def post(self, request):
        request_user = request.user

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        raw_id: RawSudoCardCallback = serializer.validated_data.get('raw_id')


        request_payload = raw_id.payload

        try:
            request_payload = eval(request_payload)
        except:
            request_payload = json.loads(request_payload)

        if raw_id.request_type == "authorization.request":

            try:
                metadata = eval(raw_id.returned_response)
            except:
                metadata = json.loads(raw_id.returned_response)

            metadata = metadata["data"]["metadata"]

            environment = request_payload["environment"]
            business = request_payload["business"]
            main_createdAt = request_payload["createdAt"]
            main_id = request_payload["_id"]
            old_data = request_payload["data"]

            mini_id = old_data["_id"]
            authorization = old_data["object"]["_id"]
            customer = old_data["object"]["customer"]["_id"]
            account = old_data["object"]["account"]["_id"]
            card = old_data["object"]["card"]["_id"]
            amount = old_data["object"]["pendingRequest"]["amount"]
            fee = old_data["object"]["fee"]
            vat = old_data["object"]["vat"]
            currency = old_data["object"]["currency"]
            merchantAmount = old_data["object"]["merchantAmount"]
            merchantCurrency = old_data["object"]["merchantCurrency"]
            merchant = old_data["object"]["merchant"]
            terminal = old_data["object"]["terminal"]
            transactionMetadata = old_data["object"]["transactionMetadata"]
            isDeleted = old_data["object"]["isDeleted"]
            createdAt = old_data["object"]["createdAt"]
            updatedAt = old_data["object"]["updatedAt"]
            feeDetails = old_data["object"]["feeDetails"]
            __v = old_data["object"]["__v"]

            trans_created = {
                "environment": environment,
                "business": business,
                "data":{
                    "object":{
                        "business": business,
                        "customer": customer,
                        "account": account,
                        "card": card,
                        "authorization": authorization,
                        "amount": -amount,
                        "fee": -fee,
                        "vat": vat,
                        "currency": currency,
                        "metadata": metadata,
                        "type":"capture",
                        "balanceTransactions":[
                            
                        ],
                        "merchantAmount": -merchantAmount,
                        "merchantCurrency": merchantCurrency,
                        "merchant": merchant,
                        "terminal": terminal,
                        "transactionMetadata": transactionMetadata,
                        "isDeleted": isDeleted,
                        "createdAt": createdAt,
                        "updatedAt": updatedAt,
                        "_id": f"{authorization}_MANUAL",
                        "feeDetails": feeDetails,
                        "__v": __v
                    },
                    "_id": mini_id,
                    "bypass": True
                },
                "type": "transaction.created",
                "pendingWebhook": True,
                "webhookArchived": False,
                "createdAt": main_createdAt,
                "_id": main_id,
                "__v":0
            }


            formatted_string = f"changes: {{\n- _id: \"{authorization}_MANUAL\"\n+ _id: \"651ca0dfb2fd754fcd32a1aa\"\n- amount: {-amount}\n+ amount: {amount}\n- fee: {-fee}\n+ fee: {fee}\n- type: \"capture\"\n+ type: \"refund\"\n- merchantAmount: {-amount}\n+ merchantAmount: {amount}\n- createdAt: \"{createdAt}\"\n+ createdAt: \"{createdAt}\"\n- updatedAt: \"{updatedAt}\"\n+ updatedAt: \"{updatedAt}\"\n}}"

            trans_refund = {
                "environment": environment,
                "business": business,
                "data":{
                    "object":{
                        "_id": f"{authorization}_MANUAL",
                        "business": business,
                        "customer": customer,
                        "account": account,
                        "card": card,
                        "authorization": authorization,
                        "amount": -amount,
                        "fee": -fee,
                        "vat": vat,
                        "currency": currency,
                        "metadata": metadata,
                        "type":"capture",
                        "balanceTransactions":[
                            
                        ],
                        "merchantAmount": -merchantAmount,
                        "merchantCurrency": merchantCurrency,
                        "merchant": merchant,
                        "terminal": terminal,
                        "transactionMetadata": transactionMetadata,
                        "isDeleted": isDeleted,
                        "createdAt": createdAt,
                        "updatedAt": updatedAt,
                        "_id": f"{authorization}_MANUAL",
                        "feeDetails": feeDetails,
                        "__v": __v
                    },
                    "changes": formatted_string,
                    "_id": mini_id
                },
                "type": "transaction.refund",
                "pendingWebhook": False,
                "webhookArchived": False,
                "createdAt": main_createdAt,
                "_id": main_id,
                "__v":0
            }
            
            response = {
                "trans_created": trans_created,
                "trans_refund": trans_refund
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        
        else:

            response = {
                "error": "545",
                "message": "raw_id.request_type must be authorization.request"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)


    
#  Card Authorization
class SudoGatewayView(APIView):
    def post(self, request):
        
        auth_header = request.headers.get('Authorization', '')

        request_payload = request.data


        # Get IP ADDRESS
        address = request.META.get('HTTP_X_FORWARDED_FOR')
        if address:
            ip_addr = address.split(',')[-1].strip()
        else:
            ip_addr = request.META.get('REMOTE_ADDR')


        # print(ip_addr)
        # # IP ADDRESS
        # print(ConstantTable.get_constant_table_instance().sudo_card_ips)
        # if ip_addr in ConstantTable.get_constant_table_instance().sudo_card_ips:
        #     ip_correct = True
        # else:
        #     ip_correct = False

        # print(ip_correct)


        # AUTH HEADER
        if auth_header == settings.SUDO_AUTH_HEADER:
            header_correct = True
        else:
            header_correct = False


        raw_sudo_card = RawSudoCardCallback.objects.create(
            payload=json.dumps(request_payload),
            ip_addr = ip_addr,
            auth_header = header_correct
        )

        
        environment = request_payload.get("environment")
        request_type = request_payload.get("type")

        
        if request_type == "card.balance":
            card_id = request_payload.get("data", {"object": {"_id": "null"}}).get("object").get("_id")

        elif request_type == "authorization.request":
            if ConstantTable.get_constant_table_instance().sudo_card_regulator == False or environment != "production":
                response = {
                    "statusCode": 400,
                    "responseCode": "99",
                    "data": {
                        "message": f"Withdrawal Unavailable. Environment {environment}"
                    }
                }

                raw_sudo_card.returned_response=json.dumps(response)
                raw_sudo_card.save()

                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            
            if not header_correct:
                response = {
                    "statusCode": 400,
                    "responseCode": "99",
                    "data": {
                        "message": "Wrong Auth Header or Wrong IP"
                    }
                }

                raw_sudo_card.returned_response=json.dumps(response)
                raw_sudo_card.save()

                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            
            
            card_id = request_payload.get("data", {"object": {"card": {"_id": "null"}}}).get("object").get("card").get("_id")
        
        elif request_type == "transaction.created":
            card_id = request_payload.get("data", {"object": {"card": "null"}}).get("object").get("card")

        elif request_type == "transaction.refund":
            card_id = request_payload.get("data", {"object": {"card": "null"}}).get("object").get("card")

        try:
            customer = CustomerCardDetail.objects.get(card_id=card_id, card_status="active", is_frozen=False)
        except CustomerCardDetail.DoesNotExist:
            payload = json.dumps(request_payload)
            url = "https://www.cards.libertypayng.com/cards/authorizations/gateway/"

            card_response = requests.post(url, data=payload)
            log_info(f"CARD SERVICE RESPONSE :: {card_response}")

            if card_response.status_code == 200:
                response = card_response.json()

                raw_sudo_card.returned_response=json.dumps(response)
                raw_sudo_card.save()
                return Response(response, status=status.HTTP_200_OK)
            
            elif card_response.status_code == 400:
                response = card_response.json()

                raw_sudo_card.returned_response=json.dumps(response)
                raw_sudo_card.save()
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            
            else:
                response = card_response.json()

                raw_sudo_card.returned_response=json.dumps(response)
                raw_sudo_card.save()
                return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


            # response = {
            #     "statusCode": resp.get(""),
            #     "responseCode": "99",
            #     "data": {
            #         "message": "Card Does Not Exist or Card is not Active"
            #     }
            # }

            # raw_sudo_card.returned_response=json.dumps(response)
            # raw_sudo_card.save()
            # return Response(response, status=status.HTTP_400_BAD_REQUEST)



        if not customer.pin_changed:
            customer.pin_changed = True
            customer.save()

        user = customer.customer.user

        if user.is_fraud:
            response = {
                "statusCode": 400,
                "responseCode": "99",
                "data": {
                    "message": "User is Fraud"
                }
            }
            raw_sudo_card.returned_response=json.dumps(response)
            raw_sudo_card.save()
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        

        if user.is_suspended or user.lotto_suspended:
            response = {
                "statusCode": 400,
                "responseCode": "99",
                "data": {
                    "message": "User is suspended or lotto suspended"
                }
            }
            raw_sudo_card.returned_response=json.dumps(response)
            raw_sudo_card.save()
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        


        wallet_instance = user.wallets.filter(wallet_type="COLLECTION").first()

        if not wallet_instance:
            response = {
                "statusCode": 400,
                "responseCode": "99",
                "data": {
                    "message": "Wallet Does Not Exists"
                }
            }
            raw_sudo_card.returned_response=json.dumps(response)
            raw_sudo_card.save()
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        
        user_balance = wallet_instance.available_balance
        raw_sudo_card.request_type=request_type
        
        try:
            get_external_gateway = OtherServiceUsersGateway.objects.get(user=user, is_active=True)
        except OtherServiceUsersGateway.DoesNotExist:
            get_external_gateway = None
        
        if request_type == "card.balance":
            gateway_request = OtherServiceUsersGateway.send_gateway_data(
                payload=request_payload,
                service_user=get_external_gateway,
                card_id=card_id
            )
            log_info(str(gateway_request))
            user_balance = gateway_request.get("response", {}).get("data", {}).get("balance") if get_external_gateway else user_balance
            
            response = {
                "statusCode": 200,
                "responseCode": "00",
                "data": {
                    "balance": user_balance,
                }
            }            
            raw_sudo_card.returned_response=json.dumps(response)
            raw_sudo_card.other_service_response=json.dumps(gateway_request)
            raw_sudo_card.save()
            return Response(response, status=status.HTTP_200_OK)
        
        elif request_type == "authorization.request":

            unique_reference = request_payload.get("data").get("object").get("_id")
            trans_channel = request_payload.get("data").get("object").get("transactionMetadata").get("channel")
            trans_meta_type = request_payload.get("data").get("object").get("transactionMetadata").get("type")
            transaction_amount = request_payload.get("data").get("object").get("pendingRequest").get("amount")
            provider_fee = request_payload.get("data").get("object").get("fee")
            liberty_commission = 10
            total_fee = provider_fee + liberty_commission
            total_amount = transaction_amount + liberty_commission
            amount_without_comm = total_amount - total_fee
            transfer_type = "CARD_PURCHASE"
            provider_type = "SUDO"
            narration = f"{trans_channel}_{trans_meta_type}"


            try:
                get_former_trans = Transaction.objects.filter(unique_reference=unique_reference, transaction_type="CARD_PURCHASE")
                if get_former_trans:
                    response = {
                        "statusCode": 400,
                        "responseCode": "99",
                        "data": {
                            "message": "Transaction Already Exists"
                        }
                    }
                    raw_sudo_card.returned_response=json.dumps(response)
                    raw_sudo_card.save()
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

            except:
                pass


            
            calculate_daily_limit = calculate_transaction_limit(user=user, amount_to_be_removed=total_amount)
            if calculate_daily_limit is not None:
                error_message = calculate_daily_limit["message"]
                response = {
                    "statusCode": 400,
                    "responseCode": "99",
                    "data": {
                        "message": f"{error_message}"
                    }
                }
                raw_sudo_card.returned_response=json.dumps(response)
                raw_sudo_card.save()
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            
            else:

                # Part where you hanlde external call to get authorization from savings
                
                deduce_user_balance = WalletSystem.check_wallet_balance(
                    amount=total_amount,
                    wallet_instance=wallet_instance
                )     

                if deduce_user_balance["status"] == True:
                
                    # Check user's level of KYC and get transaction limit

                    check_kyc = user.check_kyc.kyc_level

                    transfer_limit = ConstantTable.calculate_transfer_limit(kyc_level=check_kyc, user=user)
                    if total_amount > transfer_limit:
                        response = {
                            "statusCode": 400,
                            "responseCode": "99",
                            "data": {
                                "message": f"You cannot Withdraw more than ₦{transfer_limit} with KYC Level {check_kyc}",
                            }
                        }
                        raw_sudo_card.returned_response=json.dumps(response)
                        raw_sudo_card.save()
                        return Response(response, status=status.HTTP_403_FORBIDDEN)
                    
                    gateway_request = None
                    
                    if get_external_gateway:
                        gateway_request = OtherServiceUsersGateway.send_gateway_data(
                            payload=request_payload,
                            service_user=get_external_gateway,
                            card_id=card_id
                        )

                        if not gateway_request.get("response", {}).get("statusCode") == 200:
                            response = {
                                "statusCode": 400,
                                "responseCode": "99",
                                "data": {
                                    "message": f"Other Service User Rejected Withdrawal"
                                }
                            }
                            raw_sudo_card.returned_response=json.dumps(response)
                            raw_sudo_card.other_service_response=json.dumps(gateway_request)
                            raw_sudo_card.save()
                            return Response(response, status=status.HTTP_403_FORBIDDEN)

                    # Debit Sender with amount due
                    deduct_balance = WalletSystem.deduct_balance(
                        user=user,
                        wallet=wallet_instance,
                        amount=total_amount,
                        trans_type=transfer_type
                    )

                    user_balance_before = deduct_balance.get("balance_before")

                    user_balance_after = WalletSystem.get_balance_after(
                        user = user,
                        balance_before=user_balance_before,
                        total_amount=total_amount,
                        is_credit=False,
                        transaction_type=transfer_type
                    )
                    
                    debit_credit_record_id = deduct_balance.get("debit_credit_record_id")

                    liberty_reference = Transaction.create_liberty_reference(suffix="CARDPUR")
                    from_provider_type = AccountSystem.get_provider_type(user=user)


                    escrow_instance = Escrow.objects.create(
                        user=user,
                        from_wallet_id=wallet_instance.wallet_id,
                        from_wallet_type=wallet_instance.wallet_type,
                        transfer_type=transfer_type,
                        amount=amount_without_comm,
                        account_provider=from_provider_type,
                        ip_addr=ip_addr,
                        balance_before=user_balance_before,
                        balance_after=user_balance_after,
                        liberty_commission=total_fee,
                        total_amount_charged=total_amount,
                        narration=narration,
                        debit_credit_record_id=debit_credit_record_id,
                        pos_charge=0,
                        pos_charge_type="BANK"
                    )

                    create_transaction = Transaction.objects.create(
                        user=user,
                        wallet_id=wallet_instance.wallet_id,
                        wallet_type=wallet_instance.wallet_type,
                        transaction_type=transfer_type,
                        transaction_sub_type=trans_channel,
                        amount=total_amount,
                        liberty_commission=total_fee,
                        provider_fee=provider_fee,
                        ip_addr=ip_addr,
                        balance_before=user_balance_before,
                        balance_after=user_balance_after,
                        liberty_reference=liberty_reference,
                        escrow_id=escrow_instance.escrow_id,
                        unique_reference=unique_reference,
                        total_amount_charged = total_amount,
                        narration=narration,
                        status="IN_PROGRESS",
                        transaction_leg="EXTERNAL",
                        terminal_id = user.terminal_id,
                        payload = {
                            "maskedPan": json.dumps(request_payload.get("data").get("object").get("card").get("maskedPan")),
                            "data1": json.dumps(request_payload.get("data").get("object").get("merchant")),
                            "data2": json.dumps(request_payload.get("data").get("object").get("terminal")),
                        },
                        debit_credit_record_id = debit_credit_record_id,
                    )

                    SudoCardTransaction.create_transaction(request_payload)

                    response = {
                        "statusCode": 200,
                        "responseCode": "00",
                        "data": {
                            "metadata": {
                                "name": user.bvn_full_name,
                                "user_email": user.email,
                                "transaction_id": f"{create_transaction.transaction_id}",
                                "liberty_reference": f"{liberty_reference}",
                                "record_id": debit_credit_record_id,
                            }
                        }
                    }
                    raw_sudo_card.returned_response=json.dumps(response)
                    raw_sudo_card.save()

                    return Response(response, status=status.HTTP_200_OK)
                
                else:
                    response = {
                        "statusCode": 400,
                        "responseCode": "51",
                    }
                    raw_sudo_card.returned_response=json.dumps(response)
                    raw_sudo_card.save()
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)                    

        elif request_type == "transaction.created":

            # response = SudoCardTransaction.finish_transaction(request_payload=request_payload)
            # raw_sudo_card.returned_response=json.dumps(response)
            # raw_sudo_card.request_type=request_type
            # raw_sudo_card.save()
            
            
            receive_trans_created.apply_async(
                queue="resolvecardpur",
                kwargs={
                    "request_payload": request_payload,
                    "raw_data_id": raw_sudo_card.id,
                    "request_type": request_type
                }
            )

            response = {
                "statusCode": 200,
                "responseCode": "00",
                "data": {
                    "metadata": "Now in Queue"
                }
            }

            raw_sudo_card.returned_response=json.dumps(response)
            raw_sudo_card.save()
            return Response(response, status=status.HTTP_200_OK)
        

        elif request_type == "transaction.refund":

            if not header_correct:
                response = {
                    "statusCode": 400,
                    "responseCode": "99",
                    "data": {
                        "message": "Wrong Auth Header or Wrong IP"
                    }
                }

                raw_sudo_card.returned_response=json.dumps(response)
                raw_sudo_card.request_type=request_type
                raw_sudo_card.save()

                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            
            else:
                process_refund_trans_task.apply_async(
                    queue="resolvecardpur",
                    kwargs={
                        "request_payload": request_payload,
                        "raw_data_id": raw_sudo_card.id
                    }
                )

                response = {
                    "statusCode": 200,
                    "responseCode": "00",
                    "data": {
                        "metadata": "Now in Queue"
                    }
                }

                # response = SudoCardTransaction.process_transaction_refund(request_payload=request_payload)

                raw_sudo_card.returned_response=json.dumps(response)
                raw_sudo_card.save()
                return Response(response, status=status.HTTP_200_OK)
        
        else:
            response = {
                "statusCode": 400,
                "responseCode": "99",
                "data": {
                    "message": "Bad Data"
                }
            }
            raw_sudo_card.returned_response=json.dumps(response)
            raw_sudo_card.save()
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


