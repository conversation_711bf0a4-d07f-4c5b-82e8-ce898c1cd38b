from django.urls import path, include
from accounts import views

# URLCOnf

call_backs = [
    path("callback/dynamic_callback/", views.DynamicTransferCallBack.as_view()),
    path("callback/woven_send/", views.WovenFundingCallBack.as_view()),
    path("callback/vfd_send/", views.VFDFundingCallBack.as_view()),
    path("user/wema/account_lookup/", views.WEMAAcountLookupAPIView.as_view()),
    path("user/wema/callback/", views.WemaCallBackAPIView.as_view()),
    path("user/wema-core/callback/", views.WemaCoreCallBackAPIView.as_view()),
    path("coralpay/ussd/withdrawal_notification/", views.WithdrawalByUSSDTransactionNotificationAPIView.as_view()),
]

transactions = [
    path("user/get_transactions_categories/", views.GetTransactionHistoryAPIView.as_view()),
    path("user/transactions/", views.TransactionHistoryAPIView.as_view()),
    path("user/transactions_notifications/", views.InAppTransactionNotificationsHistoryAPIView.as_view()),
    path("transaction_enquiry/", views.TransactionEnquiryAPIView.as_view()),
    path("manual_resolve_trans/", views.ManualResolveSendBankTransfer.as_view()),
    path("get_user_balance/", views.GetBalanceOnUserAccount.as_view()),
    path("other_comms_history/", views.OtherCommissionsRecordAPIView.as_view()),
    path("bulk_reset_escrow_id_leg/", views.BulkResetPendingTransEscrow.as_view()),
    path("generate_promocode_winners/", views.GeneratePromoCodeWinnersAPIView.as_view()),
    path("get_account_transactions/", views.GetVFDAccountTransactions.as_view()),
    path("setup_108_trans/", views.SetUpTransactionsFor108ResolveAPIView.as_view()),
    path("resolve_108_trans/", views.ManualSendMoneyAdminAPIView.as_view()),
    path("change_trans_verf_ref/", views.ChangeTransReferenceAPIView.as_view()),
    path("admin_transfer/", views.AdminTransferAPIView.as_view()),
    path("get_all_transactions/", views.AdminGetAllTransactionsAPIView.as_view()),
    path("get_all_transverfs/", views.AdminGetAllTransferVerfAPIView.as_view()),
    path("get_all_commissions/", views.AdminGetAllCommissionsAPIView.as_view()),
    path("user/get_bulk_transactions/", views.GetAllBulkTransferView.as_view()),
    path("update_bvn_nin/", views.UpdateVFDAccountNINBVN.as_view()),
    path("get_account_with_bvn/", views.GetAccountWithBVN.as_view()),
    path("vfd_repush_session_id/", views.RetriggerInflowSessionID.as_view()),
    path("update_account_name_vfd/", views.AccountNameUpdateVFD.as_view()),
    path('transactions/', views.FreshdeskTransactionDetail.as_view(), name='get_freshdesk_transaction_details'),
    path('transaction-disupte/create', views.TransactionDisputeAPIView.as_view(), name='create-transaction-dispute'),
    path('transaction-disupte/list', views.TransactionDisputeListAPIView.as_view(), name='list-transaction-dispute'),
    path("send-disputes-to-n8n/", views.SendDisputesToN8NView.as_view(), name="send_disputes_to_n8n"),
]

billsandpayment = [
    path("billers_list/", views.BillersListAPIView.as_view()),
    path("bills_payments_amount_payable/", views.BillAndPaymentPayableAmountAPIView.as_view()),
    path("bills_payments/", views.BillAndPaymentAPIView.as_view()),
    path("print_airtime_pin/", views.AirtimePinAPIView.as_view()),
    path("airtime_pin_history/", views.AirtimePinHistoryAPIView.as_view()),
    path("commissions_record_history/", views.CommissionsHistoryAPIView.as_view()),
    path("call_back_airtime_from_coral/", views.CallBackAirtimePinAPIView.as_view()),
    path("check-bills-payment-status/", views.FreshdeskCheckBillsPaymentStatusAPIView.as_view(), name="check-bills-payment-status"),
]

beneficiaries = [
    path("user/beneficiaries/buddy", views.BuddyBeneficiariesAPIView.as_view()),
    path("user/beneficiaries/buddy/remove", views.BuddyBeneficiariesAPIView.as_view()),
    path(
        "user/beneficiaries/bank_transfer",
        views.BankTransferBeneficiariesAPIView.as_view(),
    ),
    path(
        "user/beneficiaries/bank_transfer/remove",
        views.BankTransferBeneficiariesAPIView.as_view(),
    ),
]

ledger = [
    path("ledger/summary", views.FetchLedgerSummary.as_view()),
    path("ledger/record_funding/", views.RecordFunding.as_view()),
]

requestPOS = [
    path("request_pos/create/", views.RequestPosAPIView.as_view()),
    path("request_pos/history/", views.POSRequestHistoryAPIView.as_view()),
    path("request_pos/pay/", views.RequestPosMakePaymentAPIView.as_view()),
    path("request_pos/history/details/", views.RequestDetailsAPIView.as_view()),
]

others = [
    path("bank_list/", views.BankListAPIView.as_view()),
    path("user/manually_create_wallets_accounts/", views.ManaulCreateWalletAPIView.as_view()),
    path("paystack/initiate/", views.InitializePaystackTransactionAPIView.as_view()),
    path("paystack/callback/", views.PaystackCallBackAPIView.as_view()),
    path("transaction_verf/call_back/", views.TranasactionVerificationCallBackAPIView.as_view()),
    path("retrigger_payload/", views.RetriggerVFDInflowsAPIView.as_view()),
    path("transfer_commissions_out/", views.TransferBillsCommissionsOutAPIView.as_view()),
    path("transfer_other_commissions_out/", views.TransferOtherCommissionsOutAPIView.as_view()),
    path("manual_second_leg_reversal/", views.ManualReversalsExternalLegAPIView.as_view()),
    path("get_provider_transaction_history/<str:liberty_reference>/", views.GetProviderTrnasactionAccount.as_view()),
    # path("manual_resolve_internal_trans/", views.ManaualResolveInternalReversal.as_view()),
    path("merchant_sweep/", views.MerchantSweep.as_view()),
    path("get_service_transaction/", views.RetrieveTransactionUniquIDAPIView.as_view()),
    path("check_balance_levels/", views.GetBalancesAPIView.as_view()),
    path("recreate_trans_verf/", views.RecreateTransactionVerfObject.as_view()),
    path("get_trans_notifications/", views.NotifyAppTransAPIView.as_view()),
    # path("reverse_many_fund_trans/", views.ReverseManyFundTransAPIView.as_view()),

    # Deprecated
    # path("fetch_trans_escrow_id/", views.FetchTransWithEscrowID.as_view()),
    path("resend_other_account_data/", views.ResendOtherAccountDataAPIView.as_view()),
    path("cyberpay_webhook/", views.QRCyberPayWebhookAPIView.as_view()),
    path("get_credit_cards/", views.GetCreditCardsAPIView.as_view()),
    path("get_debit_cred/", views.ViewDebitCreditRecordAPIView.as_view()),
    path("get_vfd_all_bank_list/", views.GetAllBankListAPIView.as_view()),
    path("get_auto_sweep/", views.AutoSweepAPIView.as_view()),
    path("get_lotto_rem_trans/", views.LottoRemitanceTransactionHistoryAPIView.as_view()),
    path("get_card_operations/", views.LibertyCardOperationsTransactionHistoryAPIView.as_view()),
    path("get_transfer_success_rate/", views.BankBeneficiarySuccessRateAPIView.as_view()),
    path("create_temp_liberty_ref/", views.CreateLibertyReference.as_view()),
    path("handleoob_trans/", views.HandleOOBTransfers.as_view()),
    path("set_pass_temp/", views.SetCoralPasswordAPIView.as_view()),
    path("parallex_notification/", views.ParallexDumpDataAPIView.as_view()),
    path("admin_manual_create_wallet/", views.AdminCreateAccountAPIView.as_view()),
    path("fetch_transaction/", views.FetchTransactionVersion2.as_view()),
    path("get_all_vfd_accounts/", views.GetAllVFDAccountData.as_view()),
    path("resend_service_acc_callbacks/", views.RetriggerCallbackForOtherServiceAccounts.as_view()),
    path("give_commision_from_me/", views.OtherCommissionsRecordUpdateAPIView.as_view()),
]

rewards = [
    path("daily_reward/", views.DailyRewardAPIView.as_view()),
    path("daily_reward_history/", views.DailyRewardHistoryAPIView.as_view()),
    path("single_daily_reward_history/", views.SingleDailyRewardHistoryAPIView.as_view()),
    path("weekly_reward/", views.WeeklyRewardAPIView.as_view()),
    path("weekly_reward_history/", views.WeeklyRewardHistoryAPIView.as_view()),
    path("weekly_reward_filter/", views.WeekHistoryFilterAPIView.as_view()),
    path("monthly_reward/", views.MonthlyRewardAPIView.as_view()),
    path("monthly_reward_history/", views.MonthlyRewardHistoryAPIView.as_view()),
    path("monthly_reward_filter/", views.MonthlyRewardFilterHistoryAPIView.as_view()),
    path("weekly_daily_data_history/", views.WeeklyRewardsDailyDataHistoryAPIView.as_view()),
    path("weekly_data_history/", views.WeeklyRewardsDataHistoryAPIView.as_view()),
    path("leader_board_filter_by_date_type/", views.LeaderBoardFilterbyDateTypeAPIView.as_view()),
    path("leader_board_filter_by_today/", views.LeaderBoardFilterbyTodayAPIView.as_view()),
    path("leader_board_filter_by_last_seven_days/", views.LeaderBoardFilterByLastSevenDaysAPIView.as_view()),
    path("leader_board_filter_by_last_month/", views.LeaderBoardFilterByLastMonthAPIView.as_view()),
    # path("test/", views.DailyLeaderBoardAPIView.as_view()),
]

fund_by_ussd = [
    path("start_ussd_funding/", views.BeginFundByUSSDTransAPIView.as_view()),
    path("verify_ussd_funding/", views.VerifyFundByUSSDTransAPIView.as_view()),
    path("ussd_funding_callback/", views.RedBillerCallbackView.as_view()),
]

create_accounts = [
    path("create_corporate_account/", views.CreateOtherAccountCorporate.as_view()),
    path("create_personal_account/", views.CreateOtherAccountPersonal.as_view()),
    path("get_other_accounts/", views.GetOtherAccountsAPIView.as_view()),
    path("create_ajo_user_accounts/", views.CreateAjoUserAccountsAPIView.as_view()),
]

withdrawal_by_ussd = [
    path("start_ussd_withdrawal/", views.BeginWithdrawalByUSSDTransAPIView.as_view()),
    path("verify_ussd_withdrawal/", views.WithdrawalByUSSDStatusQueryAPIView.as_view()),
    path("coral_ussd_login/", views.CoralPayUSSDLoginAPIView.as_view()),

]

report = [
    path("commission-report/", views.GenerateWeeklyTransactionReport.as_view()),
    path("merchant-dashboard-report/", views.MerchantDashboardStatsAPIView.as_view()),
]


urlpatterns = [
    *call_backs,
    *transactions,
    *beneficiaries,
    *billsandpayment,
    *ledger,
    *others,
    *rewards,
    *requestPOS,
    *fund_by_ussd,
    *create_accounts,
    *withdrawal_by_ussd,
    *report
]
