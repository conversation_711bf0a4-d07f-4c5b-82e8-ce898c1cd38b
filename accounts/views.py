import io
import ast
import base64
import requests
import fitz  # PyMuPDF
import math
import calendar
import uuid
import socket

from openai import OpenAI
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from django.http import Http404
from PIL import Image


from django.db import transaction
from openpyxl import Workbook
from calendar import monthrange
from datetime import datetime, timedelta

from django.db.models import Q, Sum, Count, FloatField
from django.core.exceptions import ValidationError
from django.http import Http404, HttpResponse
from django.utils.datastructures import MultiValueDictKeyError
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from django.utils.timezone import localtime

from rest_framework.views import APIView
from rest_framework.filters import SearchFilter
from rest_framework import status
from rest_framework import generics
from drf_spectacular.utils import extend_schema, OpenApiParameter

import PyPDF2
from io import BytesIO

from accounts.helpers.helper_func import CyberPayClass, convert_any_date_format, notify_admin_group, deduct_commission_from_paybox_merchant, \
    create_electronic_levy_transaction, get_week_start_and_end_datetime, get_month_start_and_end_datetime, pay_terminal_commission, \
    settle_terminal_commission
from accounts.helpers.coral_pay_manager import CoralWithdrawByUSSDClass, ServicesVASApp
from accounts.helpers.transfer_resolving_helper import fix_receipt_url
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.woven_manager import Woven
from accounts.helpers.v2_transaction_verf_script import push_out_new_legs_from_verf
from accounts.helpers.general_account_manager import dynamic_call_back_for_funding, print_airtime_pin_request, \
    send_paystack_data_to_marketing, vfd_call_back_for_funding, send_paystack_data_to_savings, initialize_paystack
from accounts.helpers.rewards_rank import monthly_rank, today_rank, last_seven_days_rank, get_leader_board_by_date_type
from accounts.filters import CommissionsDateFilter, TransactionDateFilter, EscrowDateFilter, TransVerfDateFilter, \
    OtherCommissionsRecordDateFilter
from accounts.model_choices import VfdAccountUpdateType
from accounts.tasks import notify_admin_on_bills_airtime_low_balance_task, send_callback_out_for_other_account_fund, \
    send_callback_out_for_send_to_lotto, set_up_108_task, resolve_108_task
from accounts.authentication import CustomTokenAuthentication
from accounts.models import (
    BillsPaymentDumpData,
    GeneralDataDump,
    PayStackTransaction,
    DeletedReference,
    AccountSystem,
    RawPayStack,
    WEMACallBack,
    AccountInflowPayload,
    TransactionReward,
    DailyFundingRecord,
    StartFundByUSSDTran,
    QRCyberWebhookData,
    QRCode,
    WithdrawalByUSSDNotificationData,
    ParallexDumpData,
    PromoCodeDecider,
    PromoCodeData,
    InitializedPayStackTrans,
    OutOfBookTransfer,
    TerminalPurchaseCommission, AgencyTeam, FidelityCallBack, SendCommissionScheduler, TransactionLimitLog
)
from accounts.serializers import *
from horizon_pay.models import StartCashOutTran
from liberty_pay.exceptions import raise_serializer_error_msg
from main.helper.utils import get_ip_address
from main.models import User, ConstantTable, CallbackSystem, UserWhitelist, OtherServiceDetail, CorporateAccount, SuperAgentProfile, UserOtherAccount
from main.helper.helper_function import send_sms_to_user_on_successful_account_creation
from main.views import CustomPagination, CustomPaginationV2
from main.permissions import (
    CustomIsAuthenticated, CanSendMoney, OTPVerified, HasTransactionPin, HasKYC, VFD_Webhook_Whitelist, CheckAccountAvailable,
    WhitelistPermission, CheckWalletAvailable, AdminLockPermission, LottoUserPermission, HasKYCLevelTwo, CheckDynamicAuthentication,
    OtherServiceOtherPermissions, SendMoneyRegulatorPermission, BasicAuthWithUsernameAsUsernameFieldAuthentication, CheckIPAddresses,
    TechSupportPermission
)
from admin_dashboard.helpers.helpers import Paginator
from admin_dashboard.models import Stock, SalesRep

from send_money.helpers.helper_functions import detect_duplicate_transactions, calculate_transaction_limit
from cards.models import CustomerCardDetail
from liberty_pay.settings import cloud_messaging
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical

#################################################################################
# FUND ACCOUNT CALL BACK


class ManaulCreateWalletAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC]

    def get(self, request):
        admin_password = request.query_params.get("admin_password")
        user_by_admin = request.query_params.get("user_by_admin")
        account_create_provider = request.query_params.get("provider")

        # %2B is format for plus sign

        if admin_password and not user_by_admin or user_by_admin and not admin_password:
            user_instance = request.user

        if admin_password and user_by_admin:
            check_user_as_admin = User.objects.filter(email=user_by_admin).last()

            if admin_password != f"{settings.EMEKA_ADMIN_PASSWORD}" or not check_user_as_admin:
                user_instance = request.user

            else:
                user_instance = check_user_as_admin
        else:

            user_instance = request.user

        ###################################################################################################################
        # user_instance = request.user

        if user_instance.email == settings.FLOAT_USER_EMAIL:
            response = {
                "status": "error",
                "message": "cannot create account for superuser. sorry",
                "user_by_admin": user_by_admin,
                "user_instance": f"{user_instance.email}"
            }

            return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

        else:
            if user_instance.check_kyc.bvn_rel.bvn_first_name is None \
                    or user_instance.check_kyc.bvn_rel.bvn_last_name is None \
                    or user_instance.check_kyc.bvn_rel.bvn_number is None \
                    or user_instance.check_kyc.bvn_rel.bvn_birthdate is None:

                response = {
                    "status": "error",
                    "message": "BVN KYC Details Incomplete",
                    "user_by_admin": user_by_admin,
                    "user_instance": f"{user_instance.email}"
                }

                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            else:

                expected_wallets = ["SPEND", "COLLECTION", "COMMISSIONS", "SAVINGS"]

                # wallet_system = WalletSystem.objects.filter(user=user_instance)
                # if wallet_system:
                # Get all Wallet Instances

                get_all_wallets = WalletSystem.get_uncreated_wallets(user=user_instance)

                for wallet in expected_wallets:
                    if wallet not in get_all_wallets:

                        if wallet == "SPEND":
                            spend_wallet = WalletSystem.create_spend_wallet(user=user_instance)

                        if wallet == "COLLECTION":
                            collection_wallet = WalletSystem.create_collection_wallet(user=user_instance)

                        if wallet == "SAVINGS":
                            savings_wallet = WalletSystem.create_savings_wallet(user=user_instance)

                        if wallet == "COMMISSIONS":
                            commissions_wallet = WalletSystem.create_commissions_wallet(user=user_instance)

                    else:
                        pass

                # Get all account instances

                # get_spend_wallet = WalletSystem.objects.filter(user=user_instance, wallet_type="SPEND").last()
                get_collection_wallet = WalletSystem.objects.filter(user=user_instance, wallet_type="COLLECTION").last()

                if user_instance.type_of_user in ["LIBERTY_RETAIL", "AJO_AGENT"]:
                    get_super_account = UserOtherAccount.objects.filter(other_account=user_instance).last()
                    if get_super_account:
                        super_user = get_super_account.owner
                        new_user = user_instance

                        # corporate_id get_super_account.owner.corporate_user.last().corporate_id

                        corporate_id = CorporateAccount.objects.filter(
                            Q(user=get_super_account.owner) | Q(other_users=get_super_account.owner)).last()

                        if not corporate_id:
                            response = {
                                "status": "error",
                                "message": "No Corporate ID found",
                            }
                            return Response(response, status=status.HTTP_400_BAD_REQUEST)

                        corporate_id = corporate_id.corporate_id
                        location = new_user.retail_system.location if getattr(new_user, 'retail_system', None) else None

                        get_all_corp_accounts = AccountSystem.fetch_accounts(user=user_instance).values_list("account_type", flat=True)

                        if user_instance.type_of_user == "LIBERTY_RETAIL" and not location:
                            response = {
                                "status": "error",
                                "message": "No Location Found",
                                "user_by_admin": user_by_admin,
                                "user_instance": f"{user_instance.email}"
                            }

                            return Response(response, status=status.HTTP_400_BAD_REQUEST)

                        if "COLLECTION" not in get_all_corp_accounts:
                            if new_user.type_of_user == "AJO_AGENT":
                                company_name = OtherServiceAccountSystem.get_company_name(company_base="LIBERTYPAY", email=new_user.email)
                            else:
                                company_name = None

                            create_corporate = OtherServiceAccountSystem.arrange_corporate_detail(user=super_user, new_user=new_user,
                                                                                                  corporate_id=corporate_id, suffix=None,
                                                                                                  get_location=location, company_name=company_name)

                        if user_instance.type_of_user == "LIBERTY_RETAIL" and "OTHERS" not in get_all_corp_accounts:
                            create_corporate_two = OtherServiceAccountSystem.arrange_corporate_detail(user=super_user, new_user=new_user,
                                                                                                      corporate_id=corporate_id, suffix="COLLECTION",
                                                                                                      get_location=location)

                    else:
                        pass


                else:
                    try:
                        AccountSystem.create_accounts(user=user_instance, wallet=get_collection_wallet, provider=account_create_provider)

                    except Exception as e:

                        response = {
                            "status": "error",
                            "message": f"{e}",
                        }

                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                from_provider_type = AccountSystem.get_provider_type(user=user_instance)

                get_account_for_sms = AccountSystem.objects.filter(
                    user=user_instance, account_type="COLLECTION",
                    account_provider=from_provider_type
                ).last()

                if get_account_for_sms:
                    send_sms_to_user_on_successful_account_creation(
                        phone_number=user_instance.phone_number,
                        bvn_full_name=user_instance.bvn_full_name,
                        account_number=get_account_for_sms.account_number,
                        bank_name=get_account_for_sms.bank_name
                    )
                else:
                    pass

                response = {
                    "message": f"Successfully created VFD Accounts for {user_instance}",
                    "user_by_admin": user_by_admin,
                    "user_instance": f"{user_instance.email}"
                }

            return Response(response, status=status.HTTP_200_OK)


#######################################################################################################
# WOVEN
class WovenFundingCallBack(APIView):

    def post(self, request):

        resp = request.data

        nuban = resp.get("nuban", None)
        transaction_type = resp.get("transaction_type", None)
        source_nuban = resp.get("source_nuban", None)
        source_account_name = resp.get("source_account_name", None)
        source_bank_code = resp.get("source_bank_code", None)
        amount = resp.get("amount", None)
        provider_fee = resp.get("fee", None)
        unique_reference = resp.get("unique_reference", None)
        provider_status = resp.get("status", None)
        timestamp = resp.get("transaction_time", None)
        narration = resp.get("narration", None)

        AccountInflowPayload.objects.create(
            account_provider="WOVEN",
            payload=resp
        )

        if provider_status == "ACTIVE":
            transfer_status = "SUCCESSFUL"
        elif provider_status == "FAILED":
            transfer_status = "FAILED"
        else:
            transfer_status = "PENDING"

        if transaction_type == "FUNDING" and amount is not None and amount <= 0:
            account_ref = nuban
            woven_acct_inst = AccountSystem.objects.filter(
                account_number=account_ref
            ).first()

            if woven_acct_inst:
                wallet_instance = woven_acct_inst.wallet
                user_instance = wallet_instance.user

                woven_helper = Woven()

                # Verify that transaction_exists

                verify_transaction = woven_helper.woven_verify_funding_transaction(
                    transaction_reference=unique_reference,
                    user_nuban=nuban,
                    amount=amount
                )

                if not verify_transaction:
                    pass
                else:
                    data = dict(
                        amount=amount,
                        unique_reference=unique_reference,
                        user_nuban=nuban,
                        source_nuban=source_nuban,
                        source_account_name=source_account_name,
                        narration=narration,
                        source_bank_code=source_bank_code,
                        account_provider="WOVEN",
                        provider_fee=provider_fee,
                        transfer_status=transfer_status,
                        provider_status=provider_status,
                        timestamp=timestamp,
                        funding_payload=resp
                    )

                    send_to_dynamic_call_back = dynamic_call_back_for_funding(data=data)

            else:
                return Response({"No User Account Found"}, status=status.HTTP_400_BAD_REQUEST)

        else:
            pass

        return Response({"RECIEVED"})


#######################################################################################################
# VFD
class VFDFundingCallBack(APIView):
    permission_classes = [VFD_Webhook_Whitelist, WhitelistPermission]

    def post(self, request):

        resp = request.data

        # {
        #     "reference": "walletname-*****************",
        #     "amount": "100",
        #     "account_number": "***********",
        #     "originator_account_number": "**********",
        #     "originator_account_name": "ADEYEMI KINGSLEY ADAMU",a
        #     "originator_bank": "000014",
        #     "originator_narration": "FRM OBAFEMI NNAMDI BUHARI",
        #     "timestamp": "2022-06-06 09:53:38"
        # }

        unique_reference = resp.get("reference", None)
        amount = resp.get("amount", None)
        nuban = resp.get("account_number", None)
        source_nuban = resp.get("originator_account_number", None)
        source_account_name = resp.get("originator_account_name", None)
        source_bank_code = resp.get("originator_bank", None)
        narration = resp.get("originator_narration", None)
        timestamp = resp.get("timestamp", None)
        session_id = resp.get("session_id", None)

        transaction_type = "FUNDING"
        provider_fee = resp.get("fee", None)
        provider_status = "ACTIVE"

        # Check if session id and if it exists
        if session_id:
            existing_object = AccountInflowPayload.objects.filter(session_id=session_id).first()

            if existing_object is not None:
                return Response({
                    "status": False,
                    "message": "Session ID Exists"
                }, status=status.HTTP_400_BAD_REQUEST)

        if narration.startswith("REVERSAL") or "REVERSAL" in narration:
            account_inflow = AccountInflowPayload.objects.create(
                account_provider="VFD",
                payload=resp,
                account_num=nuban,
                rejected_inflow=True,
                unique_reference=unique_reference,
                amount=float(amount.replace(',', ''))
            )
            new_response = {
                "status": False,
                "message": "This is for Reversal"
            }
            return Response(new_response, status=status.HTTP_400_BAD_REQUEST)

        else:
            altered_nuban = nuban
            if AccountInflowPayload.is_to_be_skipped(account_number=nuban):
                altered_nuban = f"target-{nuban}"
                log_info("NUBAN IS A TARGET ACCOUNT")

            account_inflow = AccountInflowPayload.objects.create(
                account_provider="VFD",
                payload=resp,
                account_num=altered_nuban,
                unique_reference=unique_reference,
                amount=float(amount.replace(',', ''))
            )

            if AccountInflowPayload.is_to_be_skipped(account_number=nuban):
                new_response = {
                    "status": True,
                    "message": "This is for Target Account"
                }
                log_info("NUBAN IS A TARGET ACCOUNT, SKIPPING FUNDING")
                return Response(new_response, status=status.HTTP_200_OK)

            log_info("NUBAN IS NOT A TARGET ACCOUNT, PROCEEDING WITH FUNDING")
            # Verify that transaction_exists

            if provider_status == "ACTIVE":
                transfer_status = "SUCCESSFUL"
            elif provider_status == "FAILED":
                transfer_status = "FAILED"
            else:
                transfer_status = "PENDING"

            if transaction_type == "FUNDING" and amount is not None and float(amount) > 0:
                account_ref = nuban

                vfd_acct_inst_qs = AccountSystem.objects.filter(account_number=account_ref) \
                    .filter(Q(account_type="COLLECTION") | Q(account_type="OTHERS") | Q(account_type="COMMISSIONS") | Q(account_type="FLOAT")
                            )

                if len(vfd_acct_inst_qs) == 1:
                    vfd_acct_inst = vfd_acct_inst_qs.first()

                    wallet_instance = vfd_acct_inst.wallet
                    user_instance = wallet_instance.user

                    if settings.ENVIRONMENT == "development":

                        verify_transaction = {
                            "status": "00",
                            "message": "Successful Transaction Retrieval",
                            "data": {
                                "TxnId": unique_reference,
                                "amount": amount,
                                "accountNo": nuban,
                                "transactionStatus": "00",
                                "transactionDate": timestamp,
                                "toBank": "999999",
                                "fromBank": source_bank_code,
                                "sessionId": session_id,
                                "bankTransactionId": ""
                            }
                        }
                    else:

                        verify_transaction = VFDBank.vfd_transaction_verification_handler(reference=unique_reference)

                        {
                            "reference": "Liberty-*****************",
                            "amount": "500.0",
                            "account_number": "**********",
                            "originator_narration": "FRM SHITTU OLAWALE ABDULMUJEEB|Test Zenith",
                            "originator_account_number": "**********",
                            "originator_account_name": "SHITTU OLAWALE ABDULMUJEEB",
                            "originator_bank": "000015",
                            "timestamp": "2022-08-30T18:43:25.508",
                            "session_id": "000015220830194244002969964639"
                        }

                        log_info("TRANSACTION VERIFICATION HERE..!!!!!!!!!")
                        log_info("TRANSACTION VERIFICATION HERE..!!!!!!!!!")
                        log_info("TRANSACTION VERIFICATION HERE..!!!!!!!!!")
                        log_info(str(verify_transaction))
                    float_account = AccountSystem.get_float_account(from_wallet_type="FLOAT", from_provider_type="VFD")
                    commisisions_account = AccountSystem.get_float_account(from_wallet_type="COMMISSIONS", from_provider_type="VFD")

                    # and verify_transaction.get("data").get("originator_account_number") != float_account.account_number \

                    # if verify_transaction.get("status") == "00" \
                    #     and verify_transaction.get("data").get("transactionStatus") == "00" \
                    #         and verify_transaction.get("data").get("accountNo") == vfd_acct_inst.account_number \
                    #             and source_nuban not in [float_account.account_number, commisisions_account.account_number] \
                    #                 and nuban not in [float_account.account_number, commisisions_account.account_number]:

                    if verify_transaction.get("status") == "00" \
                            and verify_transaction.get("data").get("transactionStatus") == "00" \
                            and verify_transaction.get("data").get("accountNo") == vfd_acct_inst.account_number \
                            and nuban not in [float_account.account_number, commisisions_account.account_number] \
                            and (source_nuban not in [commisisions_account.account_number] or resp.get("bypass_source_nuban", None) == "success"):

                        is_greater_or_equal = True

                        if source_nuban == float_account.account_number:
                            date_part = datetime.fromisoformat(timestamp).date()
                            comparison_date = datetime(2024, 5, 31).date()  # This is the date VFD transfer moved to pool
                            is_greater_or_equal = date_part >= comparison_date

                        if not is_greater_or_equal:
                            new_response = {
                                "status": True,
                                "float_inflow": True,
                                "message": "This is for Float Account number"
                            }
                            return Response(new_response, status=status.HTTP_200_OK)

                        data = dict(
                            amount=amount,
                            unique_reference=unique_reference,
                            user_nuban=nuban,
                            source_nuban=source_nuban,
                            source_account_name=source_account_name,
                            narration=narration,
                            source_bank_code=source_bank_code,
                            account_provider="VFD",
                            provider_fee=provider_fee,
                            transfer_status=transfer_status,
                            provider_status=provider_status,
                            timestamp=timestamp,
                            funding_payload=resp,
                            float_inflow=False,
                            session_id=session_id
                        )

                        send_to_dynamic_call_back = dynamic_call_back_for_funding(data=data)

                        new_response = {
                            "status": True,
                            "inflow": True,
                            "message": "Data Received and Processed"
                        }
                        return Response(new_response, status=status.HTTP_200_OK)

                    else:

                        if nuban == commisisions_account.account_number:

                            # Get Account Balance
                            if settings.ENVIRONMENT == "development":
                                comm_balance_func = 10000
                            else:
                                comm_balance_func = VFDBank.get_vfd_float_balance(account_number=commisisions_account.account_number)

                            comm_balance = comm_balance_func if comm_balance_func is not None else 0.00

                            clean_narration = narration.replace('From Liberty/', '')

                            get_escrow_id = clean_narration.split("|")

                            if len(get_escrow_id) == 2:
                                escrow_id = get_escrow_id[0]
                                get_transaction = Transaction.objects.filter(escrow_id=escrow_id)
                                if get_transaction:
                                    if get_transaction.first().transaction_type == "SEND_BANK_TRANSFER":
                                        main_trans = get_transaction.filter(transaction_leg="EXTERNAL").last()
                                    else:
                                        main_trans = get_transaction.first()
                                else:
                                    main_trans = None

                                if main_trans is not None:

                                    OtherAppTransNotify.arrange_send_data_for_other_trnas_app_notify(
                                        user=main_trans.user,
                                        amount=amount,
                                        charge=0.00,
                                        balance=comm_balance,
                                        entry="CREDIT",
                                        to_account=commisisions_account.account_number,
                                        desc=f"Commission From - {main_trans.user_full_name}",
                                        trans_id=main_trans.transaction_id,
                                        trans_type=main_trans.transaction_type
                                    )

                                else:

                                    OtherAppTransNotify.arrange_send_data_for_other_trnas_app_notify(
                                        user=vfd_acct_inst.user,
                                        amount=amount,
                                        charge=0.00,
                                        balance=comm_balance,
                                        entry="CREDIT",
                                        to_account=commisisions_account.account_number,
                                        desc=f"Commissions for - {narration}",
                                        trans_id=narration,
                                        trans_type="NO_TYPE"
                                    )


                            else:
                                OtherAppTransNotify.arrange_send_data_for_other_trnas_app_notify(
                                    user=vfd_acct_inst.user,
                                    amount=amount,
                                    charge=0.00,
                                    balance=comm_balance,
                                    entry="CREDIT",
                                    to_account=commisisions_account.account_number,
                                    desc=f"Inflow to commissions from - {source_account_name}",
                                    trans_id=narration,
                                    trans_type="NO_TYPE"
                                )

                            new_response = {
                                "status": True,
                                "message": "This is for commissions Account number"
                            }
                            return Response(new_response, status=status.HTTP_200_OK)

                        elif nuban == float_account.account_number:
                            if not AccountSystem.objects.filter(account_number=source_nuban).exists() \
                                    and VFDBank.vfd_account_enquiry(account_number=source_nuban).get("status") != "00":

                                data = dict(
                                    amount=amount,
                                    unique_reference=unique_reference,
                                    user_nuban=nuban,
                                    source_nuban=source_nuban,
                                    source_account_name=source_account_name,
                                    narration=narration,
                                    source_bank_code=source_bank_code,
                                    account_provider="VFD",
                                    provider_fee=provider_fee,
                                    transfer_status=transfer_status,
                                    provider_status=provider_status,
                                    timestamp=timestamp,
                                    funding_payload=resp,
                                    float_inflow=True
                                )

                                send_to_dynamic_call_back = dynamic_call_back_for_funding(data=data)

                                new_response = {
                                    "status": True,
                                    "inflow": True,
                                    "main_float_inflow": True,
                                    "message": "Data Received and Processed"
                                }
                                return Response(new_response, status=status.HTTP_200_OK)



                            else:
                                new_response = {
                                    "status": True,
                                    "float_inflow": True,
                                    "message": "This is for Float Account number"
                                }
                                return Response(new_response, status=status.HTTP_200_OK)

                        elif nuban == float_account.account_number or source_nuban in [float_account.account_number,
                                                                                       commisisions_account.account_number]:
                            new_response = {
                                "status": True,
                                "message": "This is for Float Account number"
                            }
                            return Response(new_response, status=status.HTTP_200_OK)

                        else:
                            new_response = {
                                "status": False,
                                "message": "This is for an unknown Account Number"
                            }
                            return Response(new_response, status=status.HTTP_404_NOT_FOUND)

                else:
                    new_response = {
                        "status": False,
                        "message": "There are more than one User with this Account number"
                    }
                    return Response(new_response, status=status.HTTP_404_NOT_FOUND)

            else:
                new_response = {
                    "status": False,
                    "message": "Amount is None or amount not greater than zero"
                }
                return Response(new_response, status=status.HTTP_400_BAD_REQUEST)

        return Response({"RECIEVED"}, status=status.HTTP_200_OK)


class DynamicTransferCallBack(APIView):
    # permission_classes = [BlockOnMaxSameSourceFunding]
    serializer = BankFundingSerializer

    def post(self, request):
        resp = request.data

        serializer = self.serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        amount = float(serializer.validated_data["amount"])
        unique_reference = serializer.validated_data["unique_reference"]
        user_nuban = serializer.validated_data["user_nuban"]
        source_nuban = serializer.validated_data["source_nuban"]
        source_account_name = serializer.validated_data["source_account_name"]
        narration = serializer.validated_data["narration"]
        source_bank_code = serializer.validated_data["source_bank_code"]
        account_provider = serializer.validated_data["account_provider"]
        provider_fee = serializer.validated_data["provider_fee"]
        transfer_status = serializer.validated_data["transfer_status"]
        provider_status = serializer.validated_data["provider_status"]
        timestamp = serializer.validated_data["timestamp"]
        funding_payload = serializer.validated_data["funding_payload"]
        session_id = serializer.validated_data["session_id"]
        float_inflow = serializer.validated_data.get("float_inflow")

        # LOGIC

        if account_provider == "WOVEN":
            liberty_reference = f"LP-INW-WOV-{str(uuid.uuid4())}"
            send_money_to_float_reference = f"LGLP-WOV-{str(uuid.uuid4())}"

        elif account_provider == "VFD":
            liberty_reference = Transaction.create_liberty_reference(suffix="LP-INW-VFD")
            send_money_to_float_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDF")

        else:
            liberty_reference = f"LP-INW-ANY-{str(uuid.uuid4())}"
            send_money_to_float_reference = f"LGLP-ANY-{str(uuid.uuid4())}"

        get_user_wallet = AccountSystem.objects.filter(account_number=user_nuban).filter(account_type__in=["COLLECTION", "OTHERS"]).first()

        bank_float_balance_before = 0.00
        bank_float_balance_after = 0.00

        if float_inflow:
            get_user_wallet = AccountSystem.objects.filter(account_number=user_nuban).filter(account_type__in=["FLOAT"]).first()
            bank_float_balance_before = VFDBank.get_vfd_float_balance()
            bank_float_balance_after = (bank_float_balance_before + amount) if bank_float_balance_before is not None else 0.00

        if get_user_wallet:

            with transaction.atomic():
                if get_user_wallet.account_type == "OTHERS":
                    wallet_instance = get_user_wallet.user.wallets.select_for_update().filter(wallet_type="COLLECTION").last()
                else:
                    wallet_inst = get_user_wallet.wallet
                    wallet_instance = WalletSystem.objects.select_for_update().filter(id=wallet_inst.id).last()

                user_instance = wallet_instance.user
                sms_charge = WalletSystem.get_sms_charge(user_instance, "FUND_BANK_TRANSFER", amount)

                get_other_account_det = OtherServiceAccountSystem.objects.filter(user=user_instance, account_number=user_nuban, is_active=True).first()
                if get_other_account_det:
                    is_other_account = True
                    is_other_account_number = user_nuban
                    is_other_account_owner = get_other_account_det.requested_by
                    callback_payload = {
                        "user_id": get_other_account_det.ajo_collector.id if get_other_account_det.ajo_collector else wallet_instance.user.id,
                        "reference": liberty_reference,
                        "amount": amount,
                        "agent_phone": wallet_instance.user.phone_number,
                        "account_number": user_nuban,
                        "response": funding_payload
                    }

                else:
                    is_other_account = False
                    is_other_account_number = None
                    is_other_account_owner = None
                    callback_payload = None

                # if user_instance.type_of_user in ["AGENT", "LOTTO_AGENT"]:
                if (get_other_account_det and get_other_account_det.requested_by.email in [
                    "<EMAIL>"] and get_user_wallet.account_type == "OTHERS") or SuperAgentProfile.objects.select_related('agent').filter(
                        agent=user_instance).exists():
                    first_liberty_commission = ConstantTable.calculate_fund_bank_transfer_fees(user_instance, amount, True)
                else:
                    first_liberty_commission = ConstantTable.calculate_fund_bank_transfer_fees(user_instance, amount)

                if amount < first_liberty_commission:
                    liberty_commission = amount
                else:
                    liberty_commission = first_liberty_commission

                charges_on_inflow = 0
                if get_user_wallet.user.is_paybox_merchant:
                    charges_on_inflow = deduct_commission_from_paybox_merchant(amount)

                liberty_commission += charges_on_inflow

                new_amount = amount - liberty_commission

                float_account = AccountSystem.get_float_account(
                    from_wallet_type="FLOAT", from_provider_type=account_provider
                )

                account_instance = get_user_wallet

                """
                Suggesting check for session_id also here. This will eliminate dupe sessionID on inflows
                """
                check_for_transaction = Transaction.objects.filter(unique_reference=unique_reference)
                if Transaction.objects.filter(session_id=session_id).exists():
                    check_for_transaction = Transaction.objects.filter(session_id=session_id)

                if check_for_transaction:
                    transaction_instance = check_for_transaction.last()
                    escrow_id = transaction_instance.escrow_id
                    escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).last()

                    escrow_instance.liberty_commission = liberty_commission
                    escrow_instance.save()

                else:
                    user_balance_before = wallet_instance.available_balance

                    user_balance_after = WalletSystem.get_balance_after(
                        user=user_instance,
                        balance_before=user_balance_before,
                        total_amount=new_amount,
                        is_credit=True
                    )

                    # bank_float_account = AccountSystem.get_dynamic_float_account(from_wallet_type="FLOAT", from_provider_type=account_provider)
                    # if bank_float_account is not None:
                    #     bank_float_balance_before = bank_float_account.wallet.available_balance
                    # else:
                    #     bank_float_balance_before = 0.00

                    escrow_qs_inst = AccountSystem.move_to_escrow_bank_transfer(
                        user=user_instance,
                        from_wallet_id=wallet_instance.wallet_id,
                        from_wallet_type=wallet_instance.wallet_type,
                        amount=amount,
                        balance_before=user_balance_before if float_inflow == False else bank_float_balance_before,
                        balance_after=user_balance_after if float_inflow == False else bank_float_balance_after,
                        from_provider_type=account_provider,
                        user_account_number=account_instance.account_number,
                        user_account_name=account_instance.account_name,
                        user_bank_name=account_instance.bank_name,
                        user_bank_code=account_instance.bank_code,
                        user_account_provider=account_instance.account_provider,
                        total_amount_charged=amount,
                        to_account_name=float_account.account_name,
                        to_nuban=float_account.account_number,
                        to_bank_name=float_account.bank_name,
                        to_bank_code=float_account.bank_code,
                        narration="QLP_FUND_HOUSE",
                        liberty_commission=liberty_commission,
                        is_beneficiary=False,
                        is_recurring=False,
                        transaction_mode="SEND_MONEY_ONLINE",
                        transfer_type="SEND_BACK_TO_FLOAT_TRANSFER" if float_inflow == False else "FLOAT_INFLOW",
                        liberty_reference=liberty_reference,
                        fund_liberty_reference=send_money_to_float_reference
                    )

                    escrow_id = escrow_qs_inst.escrow_id

                    transaction_instance = Transaction.objects.create(
                        user=user_instance,
                        amount=amount,
                        total_amount_received=new_amount,
                        liberty_commission=liberty_commission,
                        bank_float_balance_before=bank_float_balance_before,
                        wallet_id=wallet_instance.wallet_id,
                        account_id=get_user_wallet.account_id,
                        account_provider=account_provider,
                        wallet_type=wallet_instance.wallet_type,
                        transaction_type="FUND_BANK_TRANSFER" if float_inflow == False else "FLOAT_INFLOW",
                        narration=narration,
                        status="PENDING" if float_inflow == False else "SUCCESSFUL",
                        balance_before=user_balance_before if float_inflow == False else bank_float_balance_before,
                        balance_after=user_balance_after if float_inflow == False else bank_float_balance_after,
                        beneficiary_account_name=get_user_wallet.account_name,
                        beneficiary_nuban=user_nuban,
                        source_account_name=source_account_name,
                        source_nuban=source_nuban,
                        source_bank_code=source_bank_code,
                        escrow_id=escrow_id,
                        liberty_reference=liberty_reference,
                        unique_reference=unique_reference,
                        provider_fee=provider_fee,
                        provider_status=provider_status,
                        payload=funding_payload,
                        is_other_account=is_other_account,
                        is_other_account_number=is_other_account_number,
                        is_other_account_owner=is_other_account_owner,
                        callback_payload=json.dumps(callback_payload),
                        session_id=session_id,
                        sms_charge=sms_charge
                    )

                if transaction_instance.status != "SUCCESSFUL" and transfer_status == "SUCCESSFUL":
                    transaction_instance.escrow_id = escrow_id
                    transaction_instance.status = "SUCCESSFUL"
                    transaction_instance.provider_status = provider_status

                    if not DebitCreditRecordOnAccount.objects.filter(user=user_instance, entry="CREDIT", unique_reference=unique_reference).exists():
                        # check_debit_credit
                        # pass
                        # response = {
                        #     "status": "error",
                        #     "message": "Credit Exists"
                        # }
                        # return Response(response, status=status.HTTP_400_BAD_REQUEST)

                        # fund wallet
                        fund_wallet = WalletSystem.fund_balance(
                            user=user_instance,
                            wallet=wallet_instance,
                            amount=new_amount - sms_charge,
                            trans_type="FUND_BANK_TRANSFER",
                            transaction_instance_id=transaction_instance.transaction_id,
                            unique_reference=unique_reference
                        )

                        balance_before = fund_wallet['balance_before']
                        balance_after = fund_wallet['balance_after']
                        transaction_instance.balance_after = balance_after
                        transaction_instance.balance_before = balance_before
                        transaction_instance.save()

                        create_electronic_levy_transaction(transaction_instance)
                        inflow_check = TransactionLimitLog.check_inflow_blocking(user=user_instance, amount=amount)

                        if settings.ENVIRONMENT == "development":
                            pass
                        else:
                            # Send Commission to Liberty
                            if liberty_commission > 0:
                                # Create SendCommission Scheduler
                                SendCommissionScheduler.objects.create(
                                    user=user_instance, wallet_id=wallet_instance.wallet_id, wallet_type=wallet_instance.wallet_type,
                                    amount=float(liberty_commission), provider=account_provider,
                                    transaction_commission_id=str(transaction_instance.transaction_id), transfer_leg="FUND_BANK_TRANSFER"
                                )
                                # WalletSystem.pay_commission_to_liberty(
                                #     user_id=user_instance.id,
                                #     wallet_id=wallet_instance.wallet_id,
                                #     wallet_type=wallet_instance.wallet_type,
                                #     liberty_commission=liberty_commission,
                                #     from_provider_type=account_provider,
                                #     transaction_commission_id=transaction_instance.transaction_id,
                                #     transfer_leg="FUND_BANK_TRANSFER",
                                # )

                            ##########################################################################################
                            # SEND OUT APP NOTIFICATION
                            receiver_not_token = user_instance.firebase_key
                            receiver_not_title = "Payment Received"
                            receiver_not_body = f"You have recieved a CREDIT of N{amount} from {source_account_name}"
                            receiver_not_data = {"amount_sent": f"{amount}", "available_balance": f"{balance_after}"}

                            send_out_notification = cloud_messaging.send_broadcast(
                                token=receiver_not_token,
                                title=receiver_not_title,
                                body=receiver_not_body,
                                data=receiver_not_data
                            )

                            # If the user was blocked due to large inflow, send a notification
                            if inflow_check["status"] == "blocked":
                                blocked_title = "Account Restricted"
                                blocked_body = "Your account has been restricted from making transfers due to receiving a large amount. Please contact support."
                                blocked_data = {"status": "blocked", "reason": "large_inflow"}

                                cloud_messaging.send_broadcast(
                                    token=receiver_not_token, title=blocked_title, body=blocked_body, data=blocked_data
                                )
                                InAppTransactionNotification.create_in_app_transaction_notification(
                                    user=user_instance, title=blocked_title, message_body=blocked_body
                                )

                            if is_other_account == True and new_amount > 0 and wallet_instance.available_balance >= new_amount:
                                pass
                            else:
                                # DEBIT ALERT MANAGER
                                manage_alert = WalletSystem.transaction_alert_notfication_manager(
                                    user=user_instance,
                                    amount=amount,
                                    cr_dr="CR",
                                    narration=narration,
                                    from_wallet_type=wallet_instance.wallet_type,
                                    liberty_commission=liberty_commission if new_amount != amount else None,
                                    transaction_instance_id=transaction_instance.transaction_id
                                )

                            InAppTransactionNotification.create_in_app_transaction_notification(
                                user=user_instance,
                                title=receiver_not_title,
                                message_body=receiver_not_body
                            )

                            if user_nuban == float_account.account_number:
                                pass
                            else:

                                # check KYC AND DECATIVATE SEND MONEY

                                check_kyc = user_instance.check_kyc.kyc_level
                                if check_kyc == 1:
                                    balance_limit = (
                                        ConstantTable.get_constant_table_instance().kyc_one_receivable_balance_limit
                                    )

                                elif check_kyc == 2:
                                    balance_limit = (
                                        ConstantTable.get_constant_table_instance().kyc_two_receivable_balance_limit
                                    )

                                elif check_kyc == 3:
                                    balance_limit = (
                                        ConstantTable.get_constant_table_instance().kyc_three_receivable_balance_limit
                                    )

                                else:
                                    balance_limit = None

                                if amount is not None and amount > balance_limit:
                                    user_instance.send_money_status = False
                                    user_instance.save()

                                get_whitelist_users = UserWhitelist.objects.filter(Q(list_type="INFLOW_COUNT") & Q(content__contains=[user_instance.email]))
                                if not get_whitelist_users or not source_nuban == "**********":

                                    today_date = datetime.today()
                                    today_funding = Transaction.objects.filter(transaction_type="FUND_BANK_TRANSFER", user=user_instance,
                                                                               date_created__date=today_date, source_nuban=source_nuban)
                                    no_of_times = 10
                                    if today_funding.count() >= no_of_times:
                                        details = f"ALERTTTTTTTTTTT!!\n\nALERTTTTTTTTTTT!!\nUser with email: {user_instance.email} has recieved inflow from this nuban: {source_nuban} up to or more than {no_of_times} times today and therefore has been suspended."

                                        # user_instance.is_suspended = True
                                        # user_instance.suspension_reason = details
                                        # user_instance.save()

                                        User.suspend_user(
                                            user=user_instance,
                                            reason=details
                                        )

                                        notify_admin_group(user=user_instance, details=details)

                                # SEND MONEY OUT TO FLOAT

                                # transfer_charge = ConstantTable.get_provider_fee(from_provider_type=account_provider)

                                # account_instance = AccountSystem.objects.filter(account_number=user_nuban).last()

                                # escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).last()

                                # liberty_commission=escrow_instance.liberty_commission
                                # total_amount_sent_out=escrow_instance.amount + escrow_instance.liberty_commission

                    transaction_instance.save()

                else:
                    pass
                    # # DEBIT ALERT MANAGER
                    # manage_alert = WalletSystem.transaction_alert_notfication_manager(
                    #     user = user_instance,
                    #     amount = amount,
                    #     cr_dr = "CR",
                    #     narration = narration,
                    #     from_wallet_type = wallet_instance.wallet_type,
                    #     liberty_commission=liberty_commission if new_amount != amount else None,
                    #     transaction_instance_id = transaction_instance.transaction_id
                    # )

            if settings.ENVIRONMENT == "development":
                pass
            else:
                # Send Commission to Liberty
                if transaction_instance.status == "SUCCESSFUL" and  \
                        DebitCreditRecordOnAccount.objects.filter(user=user_instance, entry="CREDIT",
                                                                                         unique_reference=unique_reference).exists():
                    if is_other_account == True and new_amount > 0 and wallet_instance.available_balance >= new_amount:

                        other_service_det = OtherServiceDetail.objects.filter(user=is_other_account_owner,
                                                                              service_name__in=["LOTTO_PLAY", "SAVINGS"]).first()

                        if other_service_det and get_other_account_det.requested_by == other_service_det.user:

                            # send_callback_out = send_callback_out_for_other_account_fund.apply_async(
                            #     queue="processbulksheet",
                            #     kwargs={
                            #         "instance_id": transaction_instance.id,
                            #         "account_number": user_nuban,
                            #         "trans_owner_user_id": get_other_account_det.requested_by.id,
                            #     }
                            # )

                            if get_other_account_det.requested_by == user_instance:
                                receiver_transaction = transaction_instance
                            else:
                                get_other_account_owner = get_other_account_det.requested_by
                                get_other_account_owner_wallet = WalletSystem.get_wallet(user=get_other_account_owner,
                                                                                         from_wallet_type="COLLECTION")

                                # Debit Agent with amount due
                                deduct_balance_from_user = WalletSystem.deduct_balance(
                                    user=user_instance,
                                    wallet=wallet_instance,
                                    amount=new_amount,
                                    trans_type="SEND_COLLECTION_ACCOUNT"
                                )

                                # Send Money To Account Owner

                                user_instance_balance_before = deduct_balance_from_user["balance_before"]
                                user_instance_balance_after = deduct_balance_from_user["balance_after"]

                                # user_instance_balance_after = WalletSystem.get_balance_after(
                                #     user=user_instance,
                                #     balance_before=user_instance_balance_before,
                                #     total_amount=new_amount,
                                #     is_credit=False
                                # )

                                tagging_reference = Transaction.create_liberty_reference(suffix="FNDOTHSNDCOLL")

                                other_account_escrow_instance = WalletSystem.move_to_escrow_send_pay_buddy(
                                    user=user_instance,
                                    balance_before=user_instance_balance_before,
                                    balance_after=user_instance_balance_after,
                                    from_wallet_id=wallet_instance.wallet_id,
                                    to_wallet_id=get_other_account_owner_wallet.wallet_id,
                                    from_wallet_type=wallet_instance.wallet_type,
                                    to_wallet_type=get_other_account_owner_wallet.wallet_type,
                                    transfer_type="SEND_COLLECTION_ACCOUNT",
                                    amount=new_amount,
                                    to_nuban=get_other_account_owner.phone_number,
                                    to_account_name=get_other_account_owner.bvn_full_name if get_other_account_owner.bvn_first_name else get_other_account_owner.full_name,
                                    liberty_commission=0.00,
                                    total_amount_charged=new_amount,
                                    narration=narration,
                                    is_beneficiary=False,
                                    is_recurring=False,
                                    debit_credit_record_id=deduct_balance_from_user.get("debit_credit_record_id"),
                                    customer_reference=tagging_reference,
                                    lotto_agent_user_id=user_instance.id,
                                    lotto_agent_user_phone=user_instance.phone_number
                                )

                                send_money_to_buddy = WalletSystem.fund_wallet_pay_buddy(
                                    sender_user_instance=user_instance,
                                    buddy_user_instance=get_other_account_owner,
                                    sender_wallet=wallet_instance,
                                    receiver_wallet=get_other_account_owner_wallet,
                                    amount=new_amount,
                                    escrow_id=other_account_escrow_instance.escrow_id,
                                    customer_reference=escrow_id,
                                    other_acc_number=user_nuban,
                                )

                                receiver_transaction = send_money_to_buddy.get("receiver_transaction")

                            if isinstance(receiver_transaction, Transaction):

                                if receiver_transaction == "FUND_BANK_TRANSFER" and get_other_account_det.ajo_collector:
                                    pass
                                else:

                                    if settings.ENVIRONMENT == "development":

                                        send_callback_out = send_callback_out_for_other_account_fund(
                                            instance_id=receiver_transaction.id,
                                            account_number=user_nuban,
                                            trans_owner_user_id=get_other_account_det.requested_by.id,
                                        )
                                    else:

                                        send_callback_out = send_callback_out_for_other_account_fund.apply_async(
                                            queue="processbulksheet",
                                            kwargs={
                                                "instance_id": receiver_transaction.id,
                                                "account_number": user_nuban,
                                                "trans_owner_user_id": get_other_account_det.requested_by.id,
                                            }
                                        )

                    else:

                        AccountSystem.handle_other_account_fund_and_debit(entry="CREDIT", amount=new_amount,
                                                                          account_inst=get_user_wallet)

            response = {"message": "new transaction created"}
            return Response(response, status=status.HTTP_200_OK)

        else:
            response = {"message": "bad data"}
            return Response(response, status=status.HTTP_200_OK)


#         # NOTES
#         # Convert timestamp on each provider callback to python datetime
#         # whitelist ip and add authentication where needed

#         # check for successful transaction here before resolving, else, pass


class TranasactionVerificationCallBackAPIView(APIView):
    permission_classes = [WhitelistPermission]

    def post(self, request):
        data = request.data

        data["raw_data"] = json.dumps(data)

        result = push_out_new_legs_from_verf(data)

        response = {
            "status": True,
            "message": "response received"
        }

        return Response(response, status=status.HTTP_200_OK)


class InitializePaystackTransactionAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        user = request.user
        amount = request.query_params.get("amount")

        try:
            amount = int(amount)
            if not isinstance(amount, int):
                raise Exception("amount not integer")
        except:
            return Response({"status": "error", "message": "invalid amount"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            get_reference = initialize_paystack(email=user.email, amount=amount)
            data = get_reference.get("data", {})

            if get_reference.get("status") is True:
                authorization_url = data.get("authorization_url")

                InitializedPayStackTrans.objects.create(
                    user=user,
                    amount=amount,
                    authorization_url=authorization_url,
                    reference=data.get("reference"),
                    access_code=data.get("access_code"),
                )

                response = {
                    "status": "success",
                    "message": "authorization url generated",
                    "data": {
                        "authorization_url": authorization_url
                    }
                }
                return Response(response, status=status.HTTP_201_CREATED)

            return Response({"status": "error", "message": "bad request"}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as err:
            return Response({"status": "error", "message": f"{err}"}, status=status.HTTP_400_BAD_REQUEST)


class PaystackCallBackAPIView(APIView):
    def post(self, request):

        data = request.data

        reference = data["data"].get("reference")
        raw_data = RawPayStack.objects.filter(reference=reference).first()
        if not raw_data:
            raw_data = RawPayStack.objects.create(
                reference=reference,
                payload=json.dumps(data)
            )

        if "RPOS-PYSTCK" in data["data"].get("reference") or data["data"].get("metadata") == "Marketing_Backend":

            raw_data.data_for = "MARKTING_BACKEND"
            raw_data.save()

            send_paystack_data_to_marketing(data)

            return Response(
                {"status": "true", "message": "response received"},
                status=status.HTTP_200_OK,
            )

        elif "SAVINGS-CR" in data["data"].get("reference") or data["data"].get("metadata") == "LIBERTYSAVINGS":

            raw_data.data_for = "LIBERTYSAVINGS"
            raw_data.save()

            post_data = send_paystack_data_to_savings(data)

            return Response(
                {"status": "true", "message": "response received"},
                status=status.HTTP_200_OK,
            )

        else:
            raw_data.data_for = "AGENCY_BANKING_BACKEND"
            raw_data.save()

            data["raw_data"] = json.dumps(data)
            email = data.get("data").get("customer").get("email")
            user = User.objects.filter(email=email).last()
            if user:
                PayStackTransaction.create_transaction(user=user, data=data)
            else:
                pass

        return Response(
            {"status": True, "message": "Payment Received"},
            status=status.HTTP_200_OK,
        )


class WemaCoreCallBackAPIView(APIView):
    permission_classes = [WhitelistPermission]

    def post(self, request):

        data = request.data
        reference = data.get("transaction_reference")
        session_id = data.get("session_id", 0)
        payer_account_number = data.get("originator_account_number", None)
        source_bank_code = data.get("payer_bank_code", None)
        narration = data.get("narration", None)
        timestamp = data.get("paid_at", None)
        nuban = data.get("recipient_account_number", None)
        unique_reference = data.get("reference", 0)
        transaction_reference = data.get("transaction_reference", 0)
        provider = data.get("provider", "WEMA_BANK")

        transaction_type = "FUNDING"
        provider_fee = data.get("fee", None)
        provider_status = data["settlement_status"]

        # Check if session id and if it exists
        if session_id:
            existing_object = AccountInflowPayload.objects.filter(session_id=session_id).first()

            if existing_object is not None:
                return Response({
                    "status": False,
                    "message": "Session ID Exists"
                }, status=status.HTTP_400_BAD_REQUEST)

        raw_data = AccountInflowPayload.objects.filter(transaction_reference=reference).first()
        if provider == "WEMA_BANK":
            provider_name = "WEMA"
            WEMACallBack.objects.create(payload=data)
            acct_inst_qs = AccountSystem.objects.filter(account_number=nuban, bank_name="Wema Bank PLC") \
                .filter(Q(account_type="COLLECTION")
                        )
        elif provider == "FIDELITY":
            provider_name = "FIDELITY"
            FidelityCallBack.objects.create(payload=data)
            acct_inst_qs = AccountSystem.objects.filter(account_number=nuban, bank_name="Fidelity Bank Plc").filter(Q(account_type="COLLECTION"))
        else:
            return Response({
                "status": False,
                "message": "Invalid Provider"
            }, status=status.HTTP_400_BAD_REQUEST)

        if not raw_data:
            raw_data = AccountInflowPayload.objects.create(
                account_provider=provider_name,
                account_num=data.get("recipient_account_number"),
                unique_reference=data.get("reference", 0),
                transaction_reference=transaction_reference,
                amount=data.get("amount", 0),
                session_id=data.get("session_id", 0),
                rejected_inflow=False,
                payload=json.dumps(data)
            )

        else:
            return Response({
                "status": False,
                "message": "Reference already Exists"
            }, status=status.HTTP_400_BAD_REQUEST)

        if len(acct_inst_qs) == 1:
            log_info("I GOT HERE IN TO THE IF STATEMENT")
            log_info("I GOT HERE IN TO THE IF STATEMENT")
            log_info(nuban, "ACCOUNT NUMBER")
            log_info("I GOT HERE IN TO THE IF STATEMENT")
            log_info("I GOT HERE IN TO THE IF STATEMENT")
            log_info("I GOT HERE IN TO THE IF STATEMENT")
            acct_inst = acct_inst_qs.first()
            log_info(f"{acct_inst, acct_inst.id}", "ACCOUNT SYSTEM ID")
            wallet_instance = acct_inst.wallet
            log_info(f"{wallet_instance, wallet_instance.id}", "WALLET SYSTEM ID")
            user_instance = wallet_instance.user
            log_info(f"{user_instance, user_instance.id}", "USER ID")

            payload = dict(
                amount=float(data["amount"]),
                unique_reference=unique_reference,
                payment_reference=transaction_reference,
                transaction_reference=transaction_reference,
                user_nuban=nuban,
                source_nuban=payer_account_number,
                source_account_name=data["payer_account_name"],
                narration=narration,
                source_bank_code=source_bank_code,
                account_provider=provider_name,
                provider_fee=provider_fee,
                transfer_status=provider_status,
                provider_status=provider_status,
                timestamp=timestamp,
                funding_payload=data,
                float_inflow=False,
                session_id=session_id
            )

            AccountSystem.inflow_handler(user_instance.id, payload, provider_name)

        else:
            return Response({"status": False, "message": "Multiple account number detected"}, status=status.HTTP_400_BAD_REQUEST)

        return Response(
            {"status": True, "message": "Payment Received"},
            status=status.HTTP_200_OK,
        )

    ## SAMPLE CALL BACK DATA FROM CORE BANKING
    # {
    #     "one_time":false,
    #     "request_reference":"None",
    #     "company":"WhisperSMS",
    #     "sub_company":"None",
    #     "sub_company_email":"None",
    #     "sub_company_unique_id":"None",
    #     "recipient_account_name":"Liberty/Oluwasegun Matthew",
    #     "recipient_account_number":"**********",
    #     "amount":5450.0,
    #     "fee":0.0,
    #     "amount_payable":5450.0,
    #     "reference":"ee9cfa15-2f6d-4cbf-a130-a97ccc9fc353",
    #     "transaction_type":"CREDIT",
    #     "payer_account_name":"MATTHEW BAKARE OLADIPUPO",
    #     "payer_account_number":"***********",
    #     "payer_bank_code":"100004",
    #     "paid_at":"2024-07-09 17:09:08.654250+00:00",
    #     "narration":"***********/**********/Liberty/Oluwase",
    #     "session_id":"100004240709170812116573706830",
    #     "transaction_reference":"*********",
    #     "settlement_status":false,
    #     "currency":"NGN"
    #     }


# Transaction History

class GetTransactionHistoryAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        transaction_category = dict(Transaction.HISTORY_TRANS_TYPES)

        transaction_category_qs = []

        for key, value in transaction_category.items():
            transaction_category_qs.append(value)

        response = {
            "transaction_type": transaction_category_qs
        }
        return Response(response, status=status.HTTP_200_OK)


class TransactionHistoryAPIView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = TransactionHistorySerializer
    pagination_class = CustomPagination
    search_fields = ['narration']
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = TransactionDateFilter

    def get_queryset(self):
        # request_data = self.request.data

        admin_password = self.request.query_params.get("admin_password")
        user_by_admin = self.request.query_params.get("user_by_admin")

        if admin_password and not user_by_admin or user_by_admin and not admin_password:
            request_user = self.request.user

        if admin_password and user_by_admin:
            check_user_as_admin = User.objects.filter(email=user_by_admin).last()

            if admin_password != f"{settings.EMEKA_ADMIN_PASSWORD}" or not check_user_as_admin:
                request_user = self.request.user

            else:
                request_user = check_user_as_admin
        else:
            request_user = self.request.user

        cache_key = f"transaction_history_{request_user.id}"
        queryset = cache.get(cache_key)

        if not queryset:
            # Fetch the data as before
            queryset = Transaction.objects.filter(
                Q(user=request_user) & ~Q(narration="QLP_IN_HOUSE") \
                & ~Q(transaction_type="SEND_BACK_TO_FLOAT_TRANSFER") \
                & ~Q(transaction_type="SEND_LIBERTY_COMMISSION") \
                & ~Q(transaction_type="RE_FAILED_TRANSFER_IN") \
                & ~Q(transaction_type="BANK_OOB_IN") \
                & ~Q(transaction_type="BANK_OOB_OUT") \
                & ~Q(status="IGNORE_HISTORY")
            ).order_by("-date_created")

            # Store the data in the cache
            cache.set(cache_key, queryset)

        return queryset

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True)

        for transaction in page:
            transaction: Transaction
            if transaction.promo_code and transaction.status == "SUCCESSFUL" and len(transaction.promo_code) < 10:
                # Less than 10 is because the promo_code field was initially a merchant code field which is made up of 15 characters.
                if transaction.transaction_type == "SEND_BANK_TRANSFER":
                    transaction.beneficiary_account_name += f" | (Promo Code: {transaction.promo_code})"
                elif transaction.transaction_type in ["CARD_TRANSACTION_FUND", "CARD_TRANSACTION_FUND_TRANSFER"]:
                    if transaction.source_account_name:
                        transaction.source_account_name += f" | (Promo Code: {transaction.promo_code})"
                    else:
                        transaction.source_account_name = f"| (Promo Code: {transaction.promo_code})"

        response.data = {
            'count': paginator.page.paginator.count,
            'next': paginator.get_next_link(),
            'previous': paginator.get_previous_link(),
            'trans_types': [x[0] for x in Transaction.TRANSACTION_TYPE_CHOICES],
            'results': serializer.data
        }

        return response


class FreshdeskTransactionDetail(APIView):
    def post(self, request):
        # Extract API Key from headers
        api_key = request.headers.get("X-API-KEY")

        # Validate API Key
        if not api_key or api_key != settings.LIB_PAY_API_KEY:
            return Response(
                {"success": False, "error": "Invalid or missing API key."}, status=403
            )

        # Get transaction_id or receipt_url from request body
        transaction_id = request.data.get('transaction_id')
        receipt_url = request.data.get('receipt_url')
        
        if receipt_url and not transaction_id:
            # Extract transaction ID from PDF receipt
            try:
                extracted_transaction_id = self.extract_transaction_id_from_pdf(receipt_url)
                if not extracted_transaction_id:
                    return Response(
                        {"success": False, "error": "Could not extract transaction ID from receipt."}, 
                        status=400
                    )
                transaction_id = extracted_transaction_id
            except Exception as e:
                return Response(
                    {"success": False, "error": f"Error processing receipt: {str(e)}"}, 
                    status=500
                )
        elif not transaction_id:
            return Response(
                {"success": False, "error": "Either transaction_id or receipt_url is required."}, 
                status=400
            )

        # Retrieve transaction (existing logic)
        try:
            transaction = Transaction.objects.get(transaction_id=transaction_id)
        except Transaction.DoesNotExist:
            raise Http404("Transaction not found")

        # Fetch escrow object using escrow_id from transaction (existing logic)
        session_id = None
        if transaction.escrow_id:
            try:
                escrow = Escrow.objects.get(escrow_id=transaction.escrow_id)
                if escrow.external_payload:
                    try:
                        external_payload = ast.literal_eval(escrow.external_payload)
                    except (ValueError, SyntaxError):
                        raise ValueError("Invalid JSON format in external_payload")
                    session_id = external_payload.get("data", {}).get("sessionId")
            except Escrow.DoesNotExist:
                pass

        data = {
            "transaction_id": transaction.transaction_id,
            "amount": transaction.amount,
            "status": transaction.status,
            "transaction_type": transaction.transaction_type,
            "transaction_sub_type": transaction.transaction_sub_type,
            "beneficiary_account_name": transaction.beneficiary_account_name,
            "beneficiary_nuban": transaction.beneficiary_nuban,
            "source_account_name": transaction.source_account_name,
            "source_nuban": transaction.source_nuban,
            "date_created": transaction.date_created,
            "session_id": session_id,
        }

        return Response(
            {"success": True, "data": data, "message": "Transaction details retrieved successfully."},
            status=200,
        )

    def extract_transaction_id_from_pdf(self, pdf_url):
        """
        Download PDF/image from URL and use GPT Vision to extract transaction ID
        """
        print(f"[DEBUG] Starting extraction for URL: {pdf_url}")
        
        # Step 1: Clean and decode the URL
        import urllib.parse
        
        # Handle double-encoded URLs from Freshchat
        if '%25' in pdf_url:
            print(f"[DEBUG] URL is double-encoded, decoding...")
            pdf_url = urllib.parse.unquote(pdf_url)
            print(f"[DEBUG] Decoded URL: {pdf_url}")
        
        # Step 2: Download file from Freshchat S3 URL
        try:
            print(f"[DEBUG] Starting file download...")
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': '*/*',
            }
            
            response = requests.get(pdf_url, headers=headers, timeout=30, allow_redirects=True)
            print(f"[DEBUG] Download response status: {response.status_code}")
            print(f"[DEBUG] Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            print(f"[DEBUG] Content-Length: {len(response.content)} bytes")
            response.raise_for_status()
            
        except requests.RequestException as e:
            print(f"[ERROR] Download failed: {str(e)}")
            raise Exception(f"Failed to download file: {str(e)}")

        # Step 3: Process file based on type
        try:
            if ".pdf" in pdf_url.lower():
                print(f"[DEBUG] File detected as PDF, converting to image...")
                image_base64 = self.convert_pdf_to_image(response.content)
                print(f"[DEBUG] PDF converted to image, base64 length: {len(image_base64)} chars")
            else:
                print(f"[DEBUG] File detected as image, encoding to base64...")
                image_base64 = base64.b64encode(response.content).decode('utf-8')
                print(f"[DEBUG] Image encoded, base64 length: {len(image_base64)} chars")
                
        except Exception as e:
            print(f"[ERROR] File processing failed: {str(e)}")
            raise Exception(f"Failed to process file: {str(e)}")

        # Step 4: Use GPT Vision to extract transaction ID
        try:
            print(f"[DEBUG] Sending to GPT Vision for transaction ID extraction...")
            transaction_id = self.get_transaction_id_with_vision(image_base64)
            print(f"[DEBUG] GPT Vision response: {transaction_id}")
            return transaction_id
        except Exception as e:
            print(f"[ERROR] GPT Vision failed: {str(e)}")
            raise Exception(f"Failed to extract transaction ID with GPT Vision: {str(e)}")

    def convert_pdf_to_image(self, pdf_bytes):
        """
        Convert PDF to image using PyMuPDF
        """
        try:
            print(f"[DEBUG] Opening PDF document from {len(pdf_bytes)} bytes...")
            # Open PDF from bytes
            pdf_document = fitz.open(stream=pdf_bytes, filetype="pdf")
            print(f"[DEBUG] PDF opened successfully, pages: {len(pdf_document)}")
            
            # Get first page (receipts are usually single page)
            page = pdf_document[0]
            print(f"[DEBUG] Processing page 1, page rect: {page.rect}")
            
            # Convert to image with high DPI for better text recognition
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
            pix = page.get_pixmap(matrix=mat)
            print(f"[DEBUG] Generated pixmap: {pix.width}x{pix.height} pixels")
            
            # Convert to PIL Image
            img_data = pix.tobytes("png")
            print(f"[DEBUG] Pixmap converted to PNG bytes: {len(img_data)} bytes")
            
            image = Image.open(io.BytesIO(img_data))
            print(f"[DEBUG] PIL Image created: {image.size}, mode: {image.mode}")
            
            # Convert to base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            print(f"[DEBUG] Final base64 encoding completed: {len(image_base64)} chars")
            
            pdf_document.close()
            print(f"[DEBUG] PDF document closed successfully")
            return image_base64
            
        except Exception as e:
            print(f"[ERROR] PDF conversion error: {str(e)}")
            print(f"[ERROR] Error type: {type(e).__name__}")
            raise Exception(f"PDF to image conversion failed: {str(e)}")

    def get_transaction_id_with_vision(self, image_base64):
        """
        Use OpenAI GPT Vision to extract transaction ID from receipt image
        """
        print(f"[DEBUG] Initializing OpenAI client...")
        client = OpenAI(api_key=settings.OPENAI_API_KEY)
        
        try:
            print(f"[DEBUG] Preparing GPT Vision request...")
            print(f"[DEBUG] Image base64 preview: {image_base64[:50]}...")
            
            response = client.chat.completions.create(
                model="gpt-4o",  # GPT-4 Vision model
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": """
                                You are a transaction ID extractor for Liberty Pay receipts. 
                                
                                Look at this receipt and find the transaction ID. It could be labeled as:
                                - Transaction ID
                                - Reference Number  
                                - Transaction Reference
                                - Ref ID
                                - Payment Reference
                                - Transaction Ref
                                - Or similar terms
                                
                                The transaction ID is usually a long alphanumeric string like:
                                - LP123456789012
                                - TXN-ABC123DEF456
                                - REF_1234567890
                                - Or similar patterns
                                
                                Return ONLY the transaction ID string, nothing else. 
                                If you cannot find a clear transaction ID, return "NOT_FOUND".
                                """
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=100,
                temperature=0  # Deterministic output
            )
            
            print(f"[DEBUG] GPT Vision API call successful")
            transaction_id = response.choices[0].message.content.strip()
            print(f"[DEBUG] Raw GPT response: '{transaction_id}'")
            
            if transaction_id == "NOT_FOUND" or not transaction_id:
                print(f"[DEBUG] No transaction ID found in response")
                return None
            
            print(f"[DEBUG] Transaction ID extracted: '{transaction_id}'")
            return transaction_id
            
        except Exception as e:
            print(f"[ERROR] GPT Vision API error: {str(e)}")
            print(f"[ERROR] Error type: {type(e).__name__}")
            raise Exception(f"GPT Vision API call failed: {str(e)}")

class FreshdeskCheckBillsPaymentStatusAPIView(APIView):
    def get(self, request):
        # Extract API Key from headers
        api_key = request.headers.get("X-API-KEY")

        # Validate API Key
        if not api_key or api_key != settings.LIB_PAY_API_KEY:
            return Response({"success": False, "error": "Invalid or missing API key."}, status=403)

        # Get transaction_id from query parameters
        transaction_id = request.GET.get("transaction_id")
        if not transaction_id:
            return Response({"success": False, "error": "Transaction ID is required."}, status=400)

        try:
            bill_payment = BillsPaymentDumpData.objects.get(transaction_id=transaction_id)
        except BillsPaymentDumpData.DoesNotExist:
            return Response({"success": False, "error": "Transaction not found."}, status=404)

        # Check the transaction status first
        transaction_status = bill_payment.status
        log_info(str(transaction_status))

        if transaction_status == "SUCCESSFUL":
            # Now safely access external_service_data
            external_data = bill_payment.external_service_data
            if not external_data:
                return Response({"success": False, "error": "No external service data available.", "transaction_status":
                    transaction_status}, status=500)

            try:
                external_data = eval(external_data)  # Convert string to dictionary
                response_data = external_data.get("responseData", {})
                customer_message = response_data.get("customerMessage", "")
            except Exception:
                return Response({"success": False, "error": "Invalid external service data format.", "transaction_status":
                    transaction_status}, status=500)

            return Response({"success": True, "message": customer_message, "transaction_status": transaction_status})

        elif transaction_status == "FAILED":
            reversed_time = localtime(bill_payment.last_updated).strftime("%Y-%m-%d %I:%M %p")
            return Response(
                {"success": False, "message": f"Your transaction was unsuccessful and has been reversed to your wallet o"
                                              f"n {reversed_time}.", "transaction_status": transaction_status}
            )

        return Response(
            {"success": False, "message": "Transaction is still pending.", "transaction_status": transaction_status}, status=200
        )


class InAppTransactionNotificationsHistoryAPIView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = InAppTransactionNotificationsHistorySerializer
    pagination_class = CustomPagination

    def get_queryset(self):
        user_instance = self.request.user

        return InAppTransactionNotification.objects.filter(user=user_instance).order_by("-date_created")


####################################################################################

# Beneficiaries View

# Buddy Beneficiaries
class BuddyBeneficiariesAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = BuddyBeneficiariesSerializer
    post_serializer_class = RemoveBeneficiariesSerializer

    def get(self, request):
        user_instance = request.user

        beneficiaries_list = Beneficiaries.objects.filter(
            Q(user=request.user) & Q(beneficiary_type="BUDDY") & Q(is_active=True)
        ).order_by("-date_added")

        if beneficiaries_list:
            serializer = self.serializer_class(beneficiaries_list, many=True)
            response = {"message": "beneficiaries found", "data": serializer.data}

        else:
            response = {"message": "beneficiaries not found", "data": []}
        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = self.post_serializer_class(data=request.data)
        if serializer.is_valid():
            id = serializer.validated_data["id"]

            beneficiary = Beneficiaries.objects.filter(
                Q(id=id)
                & Q(user=request.user)
                & Q(beneficiary_type="BUDDY")
                & Q(is_active=True)
            ).first()

            if beneficiary:
                beneficiary.is_active = False
                beneficiary.date_removed = datetime.now()
                beneficiary.save()

                response = {"message": "beneficiary successfully removed"}

            else:
                response = {
                    "error": "beneficiary does not exist",
                    "message": "failed to remove beneficiary",
                }

            return Response(response, status=status.HTTP_204_NO_CONTENT)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Bank Transfer Beneficiaries
class BankTransferBeneficiariesAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    get_serializer_class = BankTransferBeneficiariesSerializer
    post_serializer_class = RemoveBeneficiariesSerializer

    def get(self, request):
        user_instance = request.user

        beneficiaries_list = Beneficiaries.objects.filter(
            Q(user=request.user)
            & Q(beneficiary_type="BANK_TRANSFER")
            & Q(is_active=True)
        ).order_by("-date_added")

        if beneficiaries_list:
            serializer = self.get_serializer_class(beneficiaries_list, many=True)
            response = {"message": "beneficiaries found", "data": serializer.data}

        else:
            response = {"message": "beneficiaries not found", "data": []}
        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = self.post_serializer_class(data=request.data)
        if serializer.is_valid():
            id = serializer.validated_data["id"]

            beneficiary = Beneficiaries.objects.filter(
                Q(id=id)
                & Q(user=request.user)
                & Q(beneficiary_type="BANK_TRANSFER")
                & Q(is_active=True)
            ).first()

            if beneficiary:
                beneficiary.is_active = False
                beneficiary.date_removed = datetime.now()
                beneficiary.save()

                response = {"message": "beneficiary successfully removed"}

            else:
                response = {
                    "error": "beneficiary does not exist",
                    "message": "failed to remove beneficiary",
                }

            return Response(response, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# VAS
class BillAndPaymentPayableAmountAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC, CheckWalletAvailable, CheckAccountAvailable]
    serializer_class = GetVASPayableSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        # user_instance = request.user

        if serializer.is_valid():
            amount = serializer.validated_data["amount"]
            biller = serializer.validated_data["biller"]

            get_biller = BillsPaymentConstant.objects.filter(biller=biller).last()
            if not get_biller:
                response = {
                    "error": "246",
                    "message": "Service currently unavailable, Please try again later"
                    # "message": "Biller does not exist"
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            bill_provider = ConstantTable.default_bills_payment_provider()
            amount_payable = BillsPaymentConstant.get_amount_payable(amount, biller, bill_provider)
            response = {
                "amount_payable": amount_payable["amount_payable"],
                "commission": amount_payable["liberty_main_commission"]
            }

            return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BillersListAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = BillerListSerializer

    def get(self, request):
        all_billers = BillsPaymentConstant.objects.all()
        serializer = self.serializer_class(all_billers, many=True)

        response = {
            "message": "Response received successfully",
            "billers": serializer.data
        }

        return Response(response, status=status.HTTP_200_OK)


class BillAndPaymentAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, CanSendMoney, OTPVerified, HasTransactionPin, HasKYC, CheckWalletAvailable]
    serializer_class = VASSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        user_instance: User = request.user

        # request.data["transaction_pin"] = removed_transaction_pin

        # Regulator

        if ConstantTable.get_constant_table_instance().bills_and_payment_regulator == False:
            response = {
                "error": True,
                "success": False,
                "message": "Service currently unavailable, Please try again later"
            }
            return Response(response, status=status.HTTP_403_FORBIDDEN)

        if serializer.is_valid():
            customerId = serializer.validated_data["customerId"]
            packageSlug = serializer.validated_data["packageSlug"]
            channel = serializer.validated_data["channel"]
            amount = serializer.validated_data["amount"]
            customerName = serializer.validated_data["customerName"]
            phoneNumber = serializer.validated_data["phoneNumber"]
            bills_type = serializer.validated_data["bills_type"]
            biller = serializer.validated_data.get("biller")
            tID = serializer.validated_data.get("tID")
            transaction_pin = serializer.validated_data["transaction_pin"]
            customer_reference = serializer.validated_data.get("customer_reference")
            from_wallet_type = serializer.validated_data.get("from_wallet_type")
            tID = user_instance.terminal_id
            from_wallet_type = "COLLECTION"

            if customer_reference:
                if Escrow.objects.filter(customer_reference=customer_reference).exists():
                    response = {
                        "error": True,
                        "success": False,
                        "message": "Customer reference already exists"
                    }

                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

            local_channel = request.GET.get("local_channel")

            first_dump_data = serializer.validated_data.copy()
            if transaction_pin is not None:
                del first_dump_data["transaction_pin"]

            GeneralDataDump.objects.create(
                user=user_instance,
                transaction_type="BILLS_AND_PAYMENT",
                payload=json.dumps(first_dump_data)
            )

            liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-BILLPAY")

            vas_dump_data = BillsPaymentDumpData.objects.create(
                user=user_instance,
                liberty_reference=liberty_reference,
                transaction_type="BILLS_AND_PAYMENT",
                dump=json.dumps(first_dump_data)
            )

            # if local_channel is not None:
            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=user_instance, pincode=transaction_pin
            )

            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(user_instance)

                response = {
                    "error": True,
                    "success": False,
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)


            else:
                User.reset_transaction_pin_retries(user_instance)

            calculate_daily_limit = calculate_transaction_limit(user_instance, request.data)
            if calculate_daily_limit is not None:
                return Response(calculate_daily_limit, status=status.HTTP_400_BAD_REQUEST)

            get_biller = BillsPaymentConstant.objects.filter(biller=biller).last()
            if not get_biller:
                response = {
                    "error": True,
                    "success": False,
                    "message": "Biller does not exist"
                    # "message": f"Service currently unavailable"
                }

                vas_dump_data.internal_service_data = response
                vas_dump_data.save()

                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            constant_variable = ConstantTable.get_constant_table_instance()

            bill_provider = ConstantTable.default_bills_payment_provider()

            amount_payable = BillsPaymentConstant.get_amount_payable(amount, biller, bill_provider)

            ussd_extra_comm = constant_variable.ussd_bills_fee if local_channel == "USSD" else 0

            liberty_commission = amount_payable["liberty_main_commission"] + ussd_extra_comm

            total_amount_charged = amount_payable["amount_payable"]
            narration = "BILLS_AND_PAYMENT"

            log_info(str(local_channel))
            #####################################################################################
            # Check minumum amount
            if amount < 50:
                response = {
                    "error": True,
                    "success": False,
                    "message": "Please enter an amount greater than N50"
                }

                vas_dump_data.internal_service_data = response
                vas_dump_data.save()

                return Response(response, status=status.HTTP_403_FORBIDDEN)

            if amount - int(amount) != 0:
                response = {
                    "error": True,
                    "success": False,
                    "message": "Please enter a whole number"
                }
                vas_dump_data.internal_service_data = response
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            wallet_instance = user_instance.wallets.filter(wallet_type=from_wallet_type.upper()).last()
            if wallet_instance:
                wallet_instance = wallet_instance
            else:
                wallet_instance = user_instance.wallets.filter(wallet_type="COLLECTION").last()

            # Main Check Balance Before Transfer

            get_user_wallet_balance = WalletSystem.check_wallet_balance(
                amount=total_amount_charged,
                wallet_instance=wallet_instance
            )

            if get_user_wallet_balance["status"] == False:
                response = {
                    "error": True,
                    "success": False,
                    "message": f"You do not have sufficient balance to make this transaction of {total_amount_charged}",
                }
                vas_dump_data.internal_service_data = response
                vas_dump_data.save()

                return Response(response, status=status.HTTP_403_FORBIDDEN)

            else:

                user_balance_before = wallet_instance.available_balance

                user_balance_after = WalletSystem.get_balance_after(
                    user=user_instance,
                    balance_before=user_balance_before,
                    total_amount=total_amount_charged,
                    is_credit=False,
                    transaction_type="BILLS_AND_PAYMENT"
                )

                # Get IP ADDRESS
                address = request.META.get('HTTP_X_FORWARDED_FOR')
                if address:
                    ip_addr = address.split(',')[-1].strip()
                else:
                    ip_addr = request.META.get('REMOTE_ADDR')

                # Deduct Balance
                deduct_balance = WalletSystem.deduct_balance(
                    user=user_instance,
                    wallet=wallet_instance,
                    amount=total_amount_charged,
                    trans_type="BILLS_PAYMENT"
                )

                debit_credit_record_id = deduct_balance.get("debit_credit_record_id")
                escrow_instance = Escrow.objects.create(
                    user=user_instance,
                    transfer_type="SEND_COMMISSION",
                    narration=narration,
                    liberty_reference=liberty_reference,
                    pos_charge=0,
                    pos_charge_type="BANK",
                    liberty_commission=liberty_commission,
                    extra_fee=ussd_extra_comm,
                    customer_reference=customer_reference,
                )

                escrow_id = escrow_instance.escrow_id

                create_transaction = Transaction.objects.create(
                    user=user_instance,
                    wallet_id=wallet_instance.wallet_id,
                    wallet_type=wallet_instance.wallet_type,
                    transaction_type="BILLS_AND_PAYMENT",
                    transaction_sub_type=packageSlug,
                    amount=amount,
                    ip_addr=ip_addr,
                    balance_before=user_balance_before,
                    balance_after=user_balance_after,
                    escrow_id=escrow_id,
                    liberty_reference=liberty_reference,
                    liberty_commission=liberty_commission,
                    extra_fee=ussd_extra_comm,
                    total_amount_charged=total_amount_charged,
                    narration=narration,
                    status="IN_PROGRESS",
                    transaction_leg="EXTERNAL",
                    terminal_id=tID,
                    payload=json.dumps(first_dump_data),
                    debit_credit_record_id=debit_credit_record_id,
                    transaction_mode=f"{local_channel}"
                )

                vas_dump_data.amount = total_amount_charged
                vas_dump_data.package_slug = packageSlug
                vas_dump_data.bills_type = bills_type
                vas_dump_data.biller = biller
                vas_dump_data.transaction_instance = create_transaction
                vas_dump_data.save()

                log_info("first: ", wallet_instance.available_balance)

                # Get debit_credit_record_id

                debit_credit_record_instance = DebitCreditRecordOnAccount.objects.filter(id=debit_credit_record_id).last()
                if debit_credit_record_instance:
                    debit_credit_record_instance.transaction_instance_id = create_transaction.transaction_id
                    debit_credit_record_instance.save()

                main_data = {
                    "paymentReference": liberty_reference,
                    "customerId": customerId,
                    "packageSlug": packageSlug,
                    "channel": channel,
                    "amount": amount,
                    "customerName": customerName,
                    "phoneNumber": phoneNumber
                }

                all_data = {
                    "main_data": main_data,
                    "vas_id": vas_dump_data.id,
                    "trans_id": create_transaction.id,
                    "wallet_id": wallet_instance.id,
                    "biller": biller,
                    "bill_provider": bill_provider,
                    "ussd_extra_comm": ussd_extra_comm
                }

                from accounts.tasks import handle_bills_payment_task

                handle_bills_payment_task.apply_async(
                    queue="ussdbills",
                    kwargs={
                        "all_data": all_data
                    }
                )

                response = {
                    "error": False,
                    "success": True,
                    "message": "Transaction Processing. Thank You for Using This Channel",
                    "customer_reference": customer_reference,
                    "escrow_id": escrow_id,
                    "transaction_id": vas_dump_data.transaction_id
                }
                return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


################################################################################################################################################
# REDBILLER


class AirtimePinAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, CanSendMoney, OTPVerified, HasTransactionPin, HasKYC, CheckWalletAvailable, CheckAccountAvailable]
    serializer_class = AirtimePinSerializer
    response_serializer_class = AirtimeToPinObjectSerializer

    def post(self, request):
        request_user = request.user

        # Regulator

        if ConstantTable.get_constant_table_instance().airtime_pin_regulator == False:
            response = {
                "error": "324",
                "message": "Service currently unavailable, Please try again later"
            }
            return Response(response, status=status.HTTP_403_FORBIDDEN)

        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            from_wallet_type = serializer.validated_data["from_wallet_type"]
            network_provider = serializer.validated_data["network_provider"]
            quantity = serializer.validated_data["quantity"]
            pin_amount = serializer.validated_data["pin_amount"]
            total_pin_amount = serializer.validated_data["total_pin_amount"]
            transaction_pin = serializer.validated_data["transaction_pin"]

            # LOGIC
            calculate_daily_limit = calculate_transaction_limit(request_user, request.data)
            if calculate_daily_limit is not None:
                return Response(calculate_daily_limit, status=status.HTTP_400_BAD_REQUEST)

            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=request_user, pincode=transaction_pin
            )

            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(request_user)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)


            else:
                User.reset_transaction_pin_retries(request_user)

                removed_transaction_pin = request.data.pop("transaction_pin")

                GeneralDataDump.objects.create(
                    user=request_user,
                    transaction_type="AIRTIME_PIN",
                    payload=json.dumps(request.data)
                )

                # request.data["transaction_pin"] = removed_transaction_pin

                # Check Minimum Printable
                if pin_amount not in [100, 200, 500, 1000]:
                    response = {
                        "error": "532",
                        "message": "Amount must be either N100, N200, N500 or N1,000"
                        # "message": "Please enter an amount in multiples of N100"
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                # Get wallet type
                from_provider_type = AccountSystem.get_provider_type(user=request_user)

                wallet_instance = WalletSystem.get_wallet(
                    user=request_user, from_wallet_type=from_wallet_type
                )

                get_total_amount = quantity * pin_amount

                liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-AIRPIN")

                vas_dump_data = BillsPaymentDumpData.objects.create(
                    user=request.user,
                    transaction_type="AIRTIME_PIN",
                    liberty_reference=liberty_reference,
                    amount=get_total_amount,
                    dump=json.dumps(request.data)
                )

                # Main Check Balance Before Transfer
                deduce_user_balance = WalletSystem.check_wallet_balance(
                    amount=get_total_amount,
                    wallet_instance=wallet_instance
                )

                if deduce_user_balance["status"] == False:
                    response = {
                        "error": "244",
                        "message": f"You do not have sufficient balance to make this transaction.",
                    }

                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                else:
                    # Check Duplicate Transaction

                    last_send_airtime_pin_transaction = detect_duplicate_transactions(
                        user=request_user,
                        transaction_type="AIRTIME_PIN",
                        amount=pin_amount,
                    )

                    # last_send_airtime_pin_transaction = False

                    if last_send_airtime_pin_transaction == True:
                        response = {
                            "error": "143",
                            "message": "Duplicate Transaction. Please try again after 5 minutes!"
                        }
                        return Response(response, status=status.HTTP_403_FORBIDDEN)

                    ############################################################################

                    liberty_commission = 0.00
                    narration = "AIRTIME PIN"

                    user_balance_before = wallet_instance.available_balance
                    user_balance_before = wallet_instance.available_balance

                    # Get IP ADDRESS
                    address = request.META.get('HTTP_X_FORWARDED_FOR')
                    if address:
                        ip_addr = address.split(',')[-1].strip()
                    else:
                        ip_addr = request.META.get('REMOTE_ADDR')

                    # Deduct Balance
                    deduct_balance = WalletSystem.deduct_balance(
                        user=request_user,
                        wallet=wallet_instance,
                        amount=get_total_amount,
                        trans_type="AIRTIME_PIN",
                    )

                    debit_credit_record_id = deduct_balance.get("debit_credit_record_id")

                    escrow_instance = Escrow.objects.create(
                        user=request_user,
                        transfer_type="SEND_COMMISSION",
                        narration=narration,
                        liberty_reference=liberty_reference,
                        pos_charge=0,
                        pos_charge_type="BANK"
                    )

                    escrow_id = escrow_instance.escrow_id

                    create_transaction = Transaction.objects.create(
                        user=request_user,
                        wallet_id=wallet_instance.wallet_id,
                        wallet_type=wallet_instance.wallet_type,
                        transaction_type="AIRTIME_PIN",
                        transaction_sub_type=network_provider,
                        amount=get_total_amount,
                        ip_addr=ip_addr,
                        balance_before=user_balance_before,
                        escrow_id=escrow_id,
                        liberty_reference=liberty_reference,
                        liberty_commission=liberty_commission,
                        total_amount_charged=get_total_amount + liberty_commission,
                        narration=narration,
                        status="IN_PROGRESS",
                        transaction_leg="EXTERNAL",
                        payload=json.dumps(request.data),
                        debit_credit_record_id=debit_credit_record_id

                    )

                    vas_dump_data.transaction_instance = create_transaction
                    vas_dump_data.save()

                    # Get debit_credit_record_id

                    debit_credit_record_instance = DebitCreditRecordOnAccount.objects.filter(id=debit_credit_record_id).last()
                    if debit_credit_record_instance:
                        debit_credit_record_instance.transaction_instance_id = create_transaction.transaction_id
                        debit_credit_record_instance.save()

                    user_balance_after = WalletSystem.get_balance_after(
                        user=request_user,
                        balance_before=user_balance_before,
                        total_amount=get_total_amount,
                        is_credit=False
                    )

                    create_transaction.balance_after = user_balance_after
                    create_transaction.save()

                    #####################################################################################################

                    airtime_pin_parent = AirtimeToPinParent.objects.create(
                        user=request_user,
                        network=network_provider,
                        batch_id=escrow_id,
                        quantity=quantity,
                        pin_amount=pin_amount,
                        liberty_reference=liberty_reference,
                        from_wallet_type=from_wallet_type,
                        total_pin_amount=get_total_amount
                    )

                    for _ in range(quantity):
                        airtime_to_pin_child = AirtimeToPinObject.objects.create(
                            biller_parent=airtime_pin_parent,
                            pin_amount=pin_amount,
                            liberty_reference=liberty_reference,
                            serial_number=f"LP-RP-{str(uuid.uuid4())[:12]}"
                        )

                    if network_provider == "MTN_NIGERIA":
                        network_name = "MTN"
                    elif network_provider == "AIRTEL_NIGERIA":
                        network_name = "Airtel"
                    elif network_provider == "GLO_NIGERIA":
                        network_name = "Glo"
                    elif network_provider == "NINE_MOBILE":
                        network_name = "9mobile"

                    data_out = dict(
                        batch_id=str(airtime_pin_parent.batch_id),
                        product=network_name,
                        amount=pin_amount,
                        quantity=quantity
                    )

                    if settings.ENVIRONMENT == "development":
                        send_out_request_to_bill = {
                            "status": "SUCCESSFUL",
                            "message": "Minimum amount allowed is NGN 100.00",
                            "data": {
                                "product": "MTN",
                                "quantity": 1,
                                "pins": "dsdfrefsdfcwe",
                                "reference": "b747d0f46bfe8935d8b27073210af7a2",
                                "batch_id": "589bff56-e44d-446e-9f5f-ed50df516981"
                            }
                        }

                    else:

                        send_out_request_to_bill = print_airtime_pin_request(data=data_out)

                    # {'status': 'SUCCESSFUL', 'message': 'Transaction successful.', 'data': {'product': 'MTN', 'quantity': 2, 'pins': ['68863965054039974', '02818463976919012'], 'reference': 'fb97d1ce015523f077729e710912bb2a', 'batch_id': '651846a4-7413-4647-aa03-706d552dd775'}}

                    # {
                    #                     #     "status":"PENDING",
                    #                     #     "message":"Transaction successful.",
                    #                     #     "data":{
                    #                     #         "product":"MTN",
                    #                     #         "quantity":2,
                    #                     #         "pins":[
                    #                     #             "68863965054039974",
                    #                     #             "02818463976919012"
                    #                     #         ],
                    #                     #         "reference":"fb97d1cerw015523f077729e710912bb2abc",
                    #                     #         "batch_id":"651846a4-74dds13-4647-aa03-706d552dd775abc"
                    #                     #     }
                    #                     # }

                    vas_dump_data.external_service_data = json.dumps(send_out_request_to_bill)
                    vas_dump_data.save()

                    airtime_pin_parent.payload = send_out_request_to_bill
                    airtime_pin_parent.unique_reference = send_out_request_to_bill.get("data").get("reference") if send_out_request_to_bill.get(
                        "data") else None
                    airtime_pin_parent.save()

                    all_created_pin_instances = airtime_pin_parent.airtime_pin_children.all()

                    unique_reference = send_out_request_to_bill.get("data", {"reference": None}).get("reference")

                    if send_out_request_to_bill["status"] == "SUCCESSFUL":
                        # and send_out_request_to_bill["data"]["pins"] is not None:

                        pins_list_length = len(send_out_request_to_bill["data"]["pins"])
                        pins_list = send_out_request_to_bill["data"]["pins"]

                        objects_to_update = all_created_pin_instances.order_by('id')[0:pins_list_length]

                        airtime_pin_parent.is_successful = True
                        airtime_pin_parent.save()

                        for (pin, object) in zip(pins_list, objects_to_update):
                            object.card_pin = pin
                            object.unique_reference = unique_reference
                            object.transaction_status = "SUCCESSFUL"
                            object.save()

                        create_transaction.status = "SUCCESSFUL"
                        create_transaction.provider_status = send_out_request_to_bill["status"]
                        create_transaction.unique_reference = unique_reference
                        create_transaction.save()

                        vas_dump_data.status = "SUCCESSFUL"
                        vas_dump_data.save()

                        serialized_data = self.response_serializer_class(all_created_pin_instances, many=True)
                        response = {
                            "message": "SUCCESSFUL",
                            "data": serialized_data.data
                        }

                        ##########################################################################################################################################

                        # SEND OUT APP NOTIFICATION
                        not_token = request_user.firebase_key
                        not_title = "Transaction Successful"
                        not_body = f"You have successfully performed an Airtime to Pin transaction of N{get_total_amount}"
                        not_data = {"amount_sent": f"{get_total_amount}", "available_balance": f"{wallet_instance.available_balance}"}

                        send_out_notification = cloud_messaging.send_broadcast(
                            token=not_token,
                            title=not_title,
                            body=not_body,
                            data=not_data
                        )

                        InAppTransactionNotification.create_in_app_transaction_notification(
                            user=request_user,
                            title=not_title,
                            message_body=not_body
                        )

                        #####################################################################################################

                        # HANDLE COMMISSIONS AND PROFITS

                        get_profits = BillsPaymentConstant.share_commissions_profit(biller=network_provider, amount=pin_amount,
                                                                                    bill_provider="REDBILLER")

                        total_profit = get_profits["total_profit"]
                        liberty_profit = get_profits["liberty_profit"]
                        agent_cash_profit = get_profits["agent_cash_profit"]
                        provider_fee = get_profits["provider_fee"]

                        for _ in range(quantity):
                            # Fund User commission wallet
                            CommissionsRecord.create_and_top_up_bill_and_pay_commissions(
                                user=request_user,
                                amount=pin_amount,
                                biller=network_provider,
                                transaction_id=create_transaction.transaction_id,
                                total_profit=total_profit,
                                liberty_profit=liberty_profit,
                                agent_cash_profit=agent_cash_profit,
                                provider_fee=provider_fee,
                                escrow_id=escrow_id
                            )

                        #########################################################################################################################

                        return Response(response, status=status.HTTP_200_OK)

                    elif send_out_request_to_bill["status"] in ["DUPLICATE", "CANCELLED", "FAILED"]:

                        provider_status = send_out_request_to_bill.get("status")

                        # Refund Money
                        reversal_transaction = Transaction.reverse_bills_airpin_transactions(transaction=create_transaction,
                                                                                             provider_status=provider_status)

                        for pin in all_created_pin_instances:
                            pin.transaction_status = "FAILED"
                            pin.is_active = False
                            pin.save()

                        vas_dump_data.status = "FAILED"
                        vas_dump_data.save()

                        if "Insufficient balance" in send_out_request_to_bill.get("message"):
                            notify_admin_on_bills_airtime_low_balance_task.delay(service_name="REDBILLER",
                                                                                 message=send_out_request_to_bill.get("message"))

                        response = {
                            "error": "143",
                            "message": "Transaction Failed. Please Try Again Later"
                        }

                        return Response(response, status=status.HTTP_403_FORBIDDEN)

                    else:

                        create_transaction.status = "PENDING"
                        create_transaction.provider_status = send_out_request_to_bill["status"]
                        create_transaction.unique_reference = unique_reference
                        create_transaction.save()

                        response = {
                            "message": "error",
                            "data": []

                        }
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                    {'response': 200, 'status': 'true', 'message': 'Transaction successful.',
                     'details': {'product': 'MTN', 'quantity': 1, 'amount_requested': 200,
                                 'order': {'amount_paid': 196, 'discount_percentage': 2, 'discount_value': 4, 'pins': ['23049367171906737']},
                                 'reference': 'ce1ba3517320ffecbade8b5de6bf0de0', 'date': '2022-08-10 19:32:23'}, 'meta': {'status': 'Approved'}}

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CallBackAirtimePinAPIView(APIView):
    permission_classes = [CheckDynamicAuthentication]
    serializer_class = AirtimePinSerializer

    def post(self, request):
        request_data = request.data

        # send_out_request_to_bill = {
        #     "status":"FAILED",
        #     "message":"Minimum amount allowed is NGN 100.00",
        #     "data":{
        #         "product":"MTN",
        #         "quantity":1,
        #         "pins":"dsdfrefsdfcwe",
        #         "reference":"b747d0f46bfe8935d8b27073210af7a2",
        #         "batch_id":"589bff56-e44d-446e-9f5f-ed50df516981"
        #     }
        # }

        # send_out_request_to_bill = {'status': 'SUCCESSFUL', 'message': 'Transaction successful.', 'data': {'product': 'MTN', 'quantity': 2, 'pins': ['68863965054039974', '02818463976919012'], 'reference': 'fb97d1ce015523f077729e710912bb2a', 'batch_id': '651846a4-7413-4647-aa03-706d552dd775'}}
        batch_id = request_data.get("data")["batch_id"]
        response = BillsPaymentDumpData.handle_airtime_pin_resolve(batch_id, request_data)

        return Response(response, status=status.HTTP_200_OK)


class AirtimePinHistoryAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    response_serializer_class = AirtimeToPinParentSerializer

    def get(self, request):
        request_user = request.user

        biller_parent = AirtimeToPinParent.objects.filter(user=request_user).order_by("-date_added")
        serializer = self.response_serializer_class(biller_parent, many=True)

        response = {
            "data": serializer.data
        }
        return Response(response, status=status.HTTP_200_OK)


#####################################################################################################################################
# WEMA VIEWS

class WEMAAcountLookupAPIView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated]

    def post(self, request):
        serializer = WEMAAcountLookupSerializer(data=request.data)
        if serializer.is_valid():
            account_number = serializer.validated_data.get('accountnumber')
            account_instance = AccountSystem.objects.filter(account_number=account_number).last()
            if account_instance:
                response = {
                    "accountname": account_instance.account_name,
                    "status": "00",
                    "status_desc": "account number found",
                }
                return Response(response, status=status.HTTP_200_OK)
            else:
                response = {
                    "accountname": "-",
                    "status": "07",
                    "status_desc": "account number does not exist"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class WemaCallBackAPIView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated]

    def post(self, request):
        WEMACallBack.objects.create(payload=str(request.data))

        response = {
            "message": "Response received successfully"
        }
        return Response(response, status=status.HTTP_200_OK)


class BankListAPIView(APIView):
    permission_classes = [CustomIsAuthenticated | WhitelistPermission]
    serializer_class = BankListSerializer

    def get(self, request):
        queryset = AllBankList.get_all_bank_list()

        serializer = self.serializer_class(queryset, many=True)

        response = {
            "message": "Response received successfully",
            "bank": serializer.data
        }

        return Response(response, status=status.HTTP_200_OK)


class TransactionEnquiryAPIView(APIView):
    permission_classes = [CheckDynamicAuthentication]
    serializer_class = TransferReferenceSerializer
    post_serializer_class = TransactionEnquirySerializer

    def post(self, request):
        custom_permission_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        request_user = custom_permission_user

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            liberty_reference = serializer.validated_data.get('liberty_reference')

            transaction = Transaction.objects.filter(Q(liberty_reference=liberty_reference) | Q(unique_reference=liberty_reference)).last()

            if transaction:
                transaction_serializer = self.post_serializer_class(transaction)
                response = {
                    "status": "transaction found",
                    "status_code": "00",
                    "data": transaction_serializer.data
                }
                return Response(response, status=status.HTTP_200_OK)
            else:
                response = {
                    "status": "no transaction found",
                    "status_code": "99",
                    "data": []
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GetBalanceOnUserAccount(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]

    def get(self, request):
        hostname = socket.gethostname()
        IPAddr = socket.gethostbyname(hostname)

        nuban = request.query_params.get('nuban')

        response = VFDBank.vfd_account_enquiry(account_number=nuban)
        response["ip_addr"] = request.META['REMOTE_ADDR']
        response["socket"] = IPAddr
        return Response(response, status=status.HTTP_200_OK)


class GetProviderTrnasactionAccount(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]

    def get(self, request, liberty_reference):

        if settings.ENVIRONMENT == "development":
            is_test_trans = True
        elif settings.ENVIRONMENT == "production":
            is_test_trans = False

        response = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference)

        return Response(response, status=status.HTTP_200_OK)


class ManualResolveSendBankTransfer(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]
    # serializer_class = TransactionManualResolveSerializer
    serializer_class = RecreateTransactionVerfObjectSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():

            if settings.ENVIRONMENT == "development":
                is_test_trans = True
            elif settings.ENVIRONMENT == "production":
                is_test_trans = False

            trans_ids = serializer.validated_data["trans_ids"]
            run_instant = serializer.validated_data.get("run_instant")

            results = []

            for data in trans_ids:

                transaction_verf = TransferVerificationObject.objects.filter(id=data).last()

                if transaction_verf:
                    if transaction_verf.transaction_ver_status in ["NOT_INITIATED", "PENDING"]:

                        verification_instance = dict(
                            transaction_instance=transaction_verf.transaction_instance,
                            user_id=transaction_verf.user_id,
                            user_email=transaction_verf.user_email,
                            account_provider=transaction_verf.account_provider,
                            transaction_leg=transaction_verf.transaction_leg,
                            transaction_type=transaction_verf.transaction_type,
                            timestamp=str(datetime.now()),
                            escrow_id=transaction_verf.escrow_id,
                            amount=transaction_verf.amount,
                            liberty_reference=transaction_verf.liberty_reference,
                            is_test=is_test_trans
                        )

                        verf_pending_trans = TransferVerificationObject.create_verfication_check(verification_instance, instant=run_instant)

                        response = {
                            "message": "sent_out",
                            "data": verf_pending_trans
                        }

                        # return Response(response, status=status.HTTP_404_NOT_FOUND)
                    else:
                        response = {
                            "message": "Transaction has been initiated or is not pending"
                        }

                        # return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                    results.append({"id": data, "liberty_reference": transaction_verf.liberty_reference, "response": response})

                else:
                    response = {
                        "error": "323",
                        "message": "Transaction does not exist"
                    }

            return Response(results, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RetriggerVFDInflowsAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = RetriggerInflowSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            start_id = serializer.validated_data["start_id"]
            end_id = serializer.validated_data["end_id"]

            check_start_id = AccountInflowPayload.objects.filter(id=start_id).last()
            check_end_id = AccountInflowPayload.objects.filter(id=end_id).last()

            if not check_start_id or not check_end_id or start_id > end_id:
                response = {
                    "error": "start id or end id does not exist or start id is greater than end id"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

            else:
                for item in AccountInflowPayload.objects.filter(id__range=[start_id, end_id]):
                    main_data = item.payload
                    send_to_vfd_call_back = vfd_call_back_for_funding(data=main_data)

                response = {
                    "message": "done"
                }

            return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# TODO Deprecated
class ManualReversalsExternalLegAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = TransactionManualResolveSerializer

    def post(self, request):
        return Response("Deprecated")

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            liberty_reference = serializer.validated_data["liberty_reference"]
            # transaction_verf = TransferVerificationObject.objects.filter(liberty_reference=liberty_reference).last()

            transaction = TransferVerificationObject.objects.filter(liberty_reference=liberty_reference).last()
            if transaction:
                if transaction.transaction_ver_status in ["NOT_FOUND"]:

                    escrow_id = transaction.transaction_instance.escrow_id

                    escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).last()

                    wallet_to_be_credited = WalletSystem.get_wallet(user=escrow_instance.user, from_wallet_type=escrow_instance.from_wallet_type)
                    account_to_be_debited_for_reversal = AccountSystem.objects.filter(wallet=wallet_to_be_credited,
                                                                                      account_provider=escrow_instance.account_provider).last()

                    liberty_commission = 0.00
                    total_amount_sent_out = escrow_instance.amount + escrow_instance.liberty_commission

                    transaction.transaction_instance.status = "SUCCESSFUL"
                    # transaction.transaction_instance.unique_reference = "TRANSACTION NOT FOUND"
                    # transaction.transaction_instance.leg_two_verification_payload = "TRANSACTION NOT FOUND"

                    transaction.transaction_ver_status = "REVERSAL"
                    # transaction.verification_payload = "TRANSACTION NOT FOUND"
                    # transaction.unique_reference = "TRANSACTION NOT FOUND"

                    transaction.transaction_instance.save()
                    transaction.save()

                    float_account = AccountSystem.get_float_account(
                        from_wallet_type="FLOAT",
                        from_provider_type=escrow_instance.account_provider
                    )

                    if escrow_instance.account_provider == "WOVEN":
                        liberty_reversal_reference = f"LGLP-WOV-{uuid.uuid4()}"

                    elif escrow_instance.account_provider == "VFD":
                        liberty_reversal_reference = f"LGLP-VFD-{uuid.uuid4()}"

                    reverse_transaction = AccountSystem.reversals_or_move_money_to_float(
                        user=escrow_instance.user,
                        account_number_to_be_debited=account_to_be_debited_for_reversal.account_number,
                        float_account_name=float_account.account_name,
                        float_account_number=float_account.account_number,
                        float_bank_code=float_account.bank_code,
                        transfer_charge=0.00,
                        narration="LP-REVERSAL",
                        amount=escrow_instance.amount + escrow_instance.liberty_commission,
                        escrow_id=escrow_instance.escrow_id,
                        liberty_reference=liberty_reversal_reference,
                        liberty_commission=liberty_commission,
                        total_amount_sent_out=total_amount_sent_out,
                        account_provider=escrow_instance.account_provider,
                        transaction_type="REVERSAL_BANK_TRANSFER",
                        transaction_leg="REVERSAL",
                        reversal_type="SECOND_LEG_REVERSAL"
                    )

                    response = {
                        "message": "reversal ongoing"
                    }

                    return Response(response, status=status.HTTP_200_OK)
                else:
                    response = {
                        "error": "343",
                        "message": "transaction verification status wrong"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

            else:
                response = {
                    "error": "343",
                    "message": "transaction not found"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CommissionsHistoryAPIView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = CommissionsHistorySerializer
    pagination_class = CustomPagination
    search_fields = ['biller', 'bills_type']
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = CommissionsDateFilter

    def get_queryset(self):
        user_instance = self.request.user

        return CommissionsRecord.objects.filter(user=user_instance).order_by("-date_created")


###################################################
# Merchant Sweep / Disbursements
###################################################

class MerchantSweep(APIView):
    # other merchant permissions
    permission_classes = [CustomIsAuthenticated]

    def post(self, request):
        data = request.data
        user = request.user
        serializer = MerchantDisbursementsSerializer(data=data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        wallet = WalletSystem.get_wallet(user=user, from_wallet_type="COLLECTION")
        if not wallet:
            response = {
                "error": "656",
                "message": "User either has no wallet or no collection wallet"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        if data['frequency'] == "HOURLY" and 'interval' not in data.keys():
            return Response({'message': 'Interval is required for hourly disbursement'},
                            status=status.HTTP_400_BAD_REQUEST)
        if data['frequency'] == "DAILY" and 'time' not in data.keys():
            return Response({'message': 'Time is required for daily disbursement'}, status=status.HTTP_400_BAD_REQUEST)
        if data['frequency'] == "WEEKLY" and 'day' not in data.keys():
            return Response({'message': 'Day and time is required for weekly disbursement'},
                            status=status.HTTP_400_BAD_REQUEST)
        if data['frequency'] == "MONTHLY" and 'date' not in data.keys():
            return Response({'message': 'Date and time is required for monthly disbursement'},
                            status=status.HTTP_400_BAD_REQUEST)

        data['time'] = data['time'] if 'time' in data.keys() and data['time'] != "" else None
        data['date'] = data['date'] if 'date' in data.keys() and data['date'] != "" else None
        data['day'] = data['day'] if 'day' in data.keys() and data['day'] != "" else None
        data['interval'] = data['interval'] if 'interval' in data.keys() and data['interval'] != "" else None
        data['sweep_limit'] = data['sweep_limit'] if 'sweep_limit' in data.keys() and data['sweep_limit'] != "" else None
        data['min_balance'] = data['min_balance'] if 'min_balance' in data.keys() and data['min_balance'] != "" else None

        disbursement = MerchantDisbursements.objects.create(
            user=user,
            wallet=wallet,
            bank_code=data['bank_code'],
            account_number=data['account_number'],
            sweep_limit=data['sweep_limit'],
            min_balance=data['min_balance'],
            frequency=data['frequency'],
            time=data['time'],
            date=data['date'],
            day=data['day'],
            interval=data['interval']
        )
        return Response({'message': 'Sweep created successfully'},
                        status=status.HTTP_200_OK)


############################################

# TODO Depracated
class ManaualResolveInternalReversal(APIView):
    serializer_class = InternalTrnasferManualResolveSerializer
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]

    #     def post(self, request):
    #         request_data = request.data

    def post(self, request):
        return Response("Deprecated")

        request_data = request.data

        serializer = self.serializer_class(data=request_data)

        if serializer.is_valid():
            liberty_reference = serializer.validated_data["liberty_reference"]

            transaction_instance = Transaction.objects.filter(liberty_reference=liberty_reference).last()
            if transaction_instance:

                user_instance = transaction_instance.user
                wallet_instance = WalletSystem.objects.filter(wallet_id=transaction_instance.wallet_id).last()

                if transaction_instance.status != "SUCCESSFUL" and transaction_instance.transaction_leg == "INTERNAL":

                    # Check bank balance
                    get_balance_response = VFDBank.vfd_account_enquiry(account_number=transaction_instance.beneficiary_nuban)

                    if get_balance_response["message"] == 'Account Not Found':

                        pass
                    else:

                        # If balance found
                        get_user_bank_balance = float(get_balance_response["data"]["accountBalance"])

                        total_amount_to_reverse = transaction_instance.amount + transaction_instance.liberty_commission

                        if get_user_bank_balance >= total_amount_to_reverse:

                            user_balance_before = wallet_instance.available_balance

                            user_balance_after = WalletSystem.get_balance_after(
                                user=user_instance,
                                balance_before=user_balance_before,
                                total_amount=total_amount_to_reverse,
                                is_credit=True
                            )

                            if transaction_instance.account_provider == "WOVEN":
                                new_liberty_reference = Transaction.create_liberty_reference(suffix="LP_T1RVRSL-WOV")
                                send_money_to_float_reference = Transaction.create_liberty_reference(suffix="LGLP-WOVF")


                            elif transaction_instance.account_provider == "VFD":
                                new_liberty_reference = Transaction.create_liberty_reference(suffix="LP_T1RVRSL-VFD")
                                send_money_to_float_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDF")

                            reversal_transaction_instance = Transaction.objects.create(
                                user=transaction_instance.user,
                                amount=total_amount_to_reverse,
                                wallet_id=transaction_instance.wallet_id,
                                account_id=transaction_instance.account_id,
                                account_provider=transaction_instance.account_provider,
                                wallet_type=transaction_instance.wallet_type,
                                transaction_type="FUND_BANK_TRANSFER",
                                narration="LP_T1RVRSL",
                                status="SUCCESSFUL",
                                balance_before=user_balance_before,
                                balance_after=user_balance_after,
                                source_account_name=transaction_instance.beneficiary_account_name,
                                source_nuban=transaction_instance.beneficiary_nuban,
                                source_bank_code=transaction_instance.beneficiary_bank_code,
                                liberty_reference=new_liberty_reference,
                                # unique_reference=unique_reference,
                                provider_fee=transaction_instance.provider_fee,
                                provider_status="ACTIVE",
                                is_internal_reversal=True
                            )

                            # fund wallet
                            fund_wallet = WalletSystem.fund_balance(
                                user=user_instance,
                                wallet=wallet_instance,
                                amount=total_amount_to_reverse,
                                trans_type="REFUND_BANK_TRANSFER",
                                transaction_instance_id=reversal_transaction_instance.transaction_id
                            )

                            # DEBIT ALERT MANAGER
                            manage_alert = WalletSystem.transaction_alert_notfication_manager(
                                user=user_instance,
                                amount=total_amount_to_reverse,
                                cr_dr="CR",
                                narration="REVERSAL",
                                from_wallet_type=wallet_instance.wallet_type
                            )

                            transaction_instance.status = "SUCCESSFUL"
                            transaction_instance.provider_status = "CONFIRMED"
                            transaction_instance.save()

                            ############################################################################################

                            # Get Account Balance
                            AccountSystem.get_balance_on_account_per_time(
                                account_number=transaction_instance.beneficiary_nuban,
                                account_provider=transaction_instance.account_provider
                            )

                            ##########################################################################################
                            # SEND OUT APP NOTIFICATION
                            receiver_not_token = user_instance.firebase_key
                            receiver_not_title = "Payment Received"
                            receiver_not_body = f"You have recieved a CREDIT of N{total_amount_to_reverse} from {transaction_instance.beneficiary_account_name}"
                            receiver_not_data = {"amount_sent": f"{total_amount_to_reverse}",
                                                 "available_balance": f"{wallet_instance.available_balance}"}

                            send_out_notification = cloud_messaging.send_broadcast(
                                token=receiver_not_token,
                                title=receiver_not_title,
                                body=receiver_not_body,
                                data=receiver_not_data
                            )

                            InAppTransactionNotification.create_in_app_transaction_notification(
                                user=user_instance,
                                title=receiver_not_title,
                                message_body=receiver_not_body
                            )

                            float_account = AccountSystem.get_float_account(
                                from_wallet_type="FLOAT", from_provider_type=transaction_instance.account_provider
                            )

                            # SEND MONEY OUT TO FLOAT

                            transfer_charge = ConstantTable.get_provider_fee(from_provider_type=transaction_instance.account_provider)

                            account_instance = AccountSystem.objects.filter(account_number=transaction_instance.beneficiary_nuban).last()

                            # escrow_id = AccountSystem.move_to_escrow_bank_transfer(
                            #     user=user_instance,
                            #     from_wallet_id=wallet_instance.wallet_id,
                            #     from_wallet_type=wallet_instance.wallet_type,
                            #     amount=total_amount_to_reverse,
                            #     balance_before=reversal_transaction_instance.balance_before,
                            #     balance_after=reversal_transaction_instance.balance_after,
                            #     from_provider_type=reversal_transaction_instance.account_provider,
                            #     user_account_number = account_instance.account_number if account_instance else None,
                            #     user_account_name= account_instance.account_name if account_instance else None,
                            #     user_bank_name= account_instance.bank_name if account_instance else None,
                            #     user_account_provider= account_instance.account_provider if account_instance else None,
                            #     total_amount_charged=total_amount_to_reverse,
                            #     to_account_name=float_account.account_name,
                            #     to_nuban=float_account.account_number,
                            #     to_bank_name=float_account.bank_name,
                            #     to_bank_code=float_account.bank_code,
                            #     narration="QLP_FUND_HOUSE",
                            #     liberty_commission=float(0),
                            #     is_beneficiary=False,
                            #     is_recurring=False,
                            #     transaction_mode="SEND_MONEY_ONLINE",
                            #     transfer_type="SEND_BACK_TO_FLOAT_TRANSFER",
                            # )

                            escrow_instance = Escrow.objects.filter(escrow_id=transaction_instance.escrow_id).last()

                            liberty_commission = escrow_instance.liberty_commission
                            total_amount_sent_out = escrow_instance.amount + escrow_instance.liberty_commission

                            send_money_back = AccountSystem.reversals_or_move_money_to_float(
                                user=user_instance,
                                account_number_to_be_debited=transaction_instance.beneficiary_nuban,
                                float_account_name=float_account.account_name,
                                float_account_number=float_account.account_number,
                                float_bank_code=float_account.bank_code,
                                transfer_charge=transfer_charge,
                                narration="RVSL_QLP_FUND_HOUSE",
                                amount=total_amount_to_reverse,
                                escrow_id=escrow_instance.escrow_id,
                                liberty_reference=send_money_to_float_reference,
                                liberty_commission=liberty_commission,
                                total_amount_sent_out=total_amount_sent_out,
                                account_provider=transaction_instance.account_provider,
                                transaction_type="SEND_BACK_TO_FLOAT_TRANSFER",
                                transaction_leg="INFLOW_TO_FLOAT",
                                reversal_type=None
                            )

                        else:
                            pass

                else:
                    pass

            else:
                pass

        else:
            pass

        response = {"message": "Recieved"}
        return Response(response, status=status.HTTP_200_OK)


class OtherCommissionsRecordAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]
    response_serializer_class = OtherCommissionsRecordSerializer

    def get(self, request):
        request_user = request.user

        cache_key = f"other_comm_history_{request_user.id}"
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            queryset = cached_data
        else:
            queryset = OtherCommissionsRecord.objects.filter(
                Q(agent=request_user, transaction_owner="AGENT") | Q(sales_rep=request_user, transaction_owner="SALES_REP")).order_by("-date_created")

            # Store the data in the cache
            cache.set(cache_key, queryset)

        serializer = self.response_serializer_class(queryset, many=True)

        response = {
            "data": serializer.data
        }
        return Response(response, status=status.HTTP_200_OK)


class OtherCommissionsRecordUpdateAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]
    serializer_class = OtherCommissionsRecordExternalUpdateSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():

            request_user = request.user
            user = User.objects.get(phone_number=serializer.data["recipient"])
            wallet_instance = request_user.wallets.filter(wallet_type="COLLECTION").last()

            if serializer.data["profit"] > 0:

                liberty_reference = f"LP-AJO-INTERNAL-COMM-{str(uuid.uuid4())}"
                transaction_type = serializer.data["transaction_type"]
                transaction_reason = serializer.data["transaction_reason"]

                # transaction_type="AJO_LOAN_COMMISSIONS",
                # narration="COMMISSIONS FROM AJO DISBURSEMENT",

                bank_float_balance_before = VFDBank.get_vfd_float_balance()
                bank_float_balance_after = bank_float_balance_before

                transaction_instance = Transaction.objects.create(
                    user=request_user,
                    amount=serializer.data["profit"],
                    total_amount_received=0,
                    liberty_commission=0,
                    bank_float_balance_before=bank_float_balance_before,
                    wallet_id=wallet_instance.wallet_id,
                    account_id=None,
                    account_provider=None,
                    wallet_type=wallet_instance.wallet_type,
                    transaction_type=transaction_type,
                    narration=transaction_reason,
                    status="PENDING",
                    balance_before=wallet_instance.available_balance,
                    balance_after=0,
                    beneficiary_account_name=f"PHONE-{serializer.data['recipient']}",
                    beneficiary_nuban=serializer.data["recipient"],
                    source_account_name=None,
                    source_nuban=None,
                    source_bank_code=None,
                    escrow_id=None,
                    liberty_reference=liberty_reference,
                    unique_reference=serializer.data["transaction_ref"],
                    provider_fee=0,
                    provider_status="00",
                    payload="{}",
                )

                try:
                    # Debit Agent with amount due
                    deduct_balance_from_user = WalletSystem.deduct_balance(
                        user=request_user,
                        wallet=wallet_instance,
                        amount=serializer.data["profit"],
                        trans_type="COMMISSIONS_DISBURSEMENT",
                        transaction_instance_id=transaction_instance.transaction_id,
                        unique_reference=transaction_instance.unique_reference
                    )

                    user_instance_balance_before = deduct_balance_from_user["balance_before"]
                    user_instance_balance_after = deduct_balance_from_user["balance_after"]
                    debit_credit_record_id = deduct_balance_from_user["debit_credit_record_id"]

                except:
                    response = {
                        "status": False,
                        "data": serializer.data,
                        "errors": "Insufficient balance"
                    }
                    return Response(response, status=status.HTTP_403_FORBIDDEN)

                user_instance_balance_before = deduct_balance_from_user["balance_before"]
                user_instance_balance_after = deduct_balance_from_user["balance_after"]
                debit_credit_record_id = deduct_balance_from_user["debit_credit_record_id"]

                handle_liberty_commisson = OtherCommissionsRecord.create_and_top_up_other_commissions_for_agent(
                    agent=user,
                    sales_rep=None,
                    amount=serializer.data["amount"],
                    transaction_id=transaction_instance.transaction_id,
                    transaction_type=transaction_type,
                    transaction_reason=transaction_reason,
                    total_profit=serializer.data["profit"],
                    liberty_profit=0,
                    ro_cash_profit=0,
                    agent_cash_profit=serializer.data["profit"]
                )

                transaction_instance = Transaction.objects.get(id=transaction_instance.id)
                transaction_instance.balance_before = user_instance_balance_before
                transaction_instance.after = user_instance_balance_after
                transaction_instance.status = "SUCCESSFUL"
                transaction_instance.save()

            response = {
                "status": True,
                "data": serializer.data,
                "errors": serializer.errors,
            }
            return Response(response, status=status.HTTP_200_OK)

        else:

            response = {
                "status": False,
                "data": serializer.data,
                "errors": serializer.errors
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class TransferBillsCommissionsOutAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC, CheckWalletAvailable, CheckAccountAvailable]
    serializer_class = VASSerializer

    def post(self, request):
        # serializer = self.serializer_class(data=request.data)
        user_instance = request.user

        if user_instance.bills_pay_comm_balance < 1:
            response = {
                "error": "342",
                "message": "No commissions to transfer"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        else:

            # balance_before = fund_buddy_balance["balance_before"]

            wallet = WalletSystem.get_wallet(user_instance, "COLLECTION")
            amount = user_instance.bills_pay_comm_balance
            liberty_reference = f"LP-TRNSF-COMM{uuid.uuid4()}"

            balance_before = wallet.available_balance

            balance_after = WalletSystem.get_balance_after(
                user=user_instance,
                balance_before=balance_before,
                total_amount=amount,
                is_credit=True
            )

            # Create transaction for Commission
            create_transaction = Transaction.objects.create(
                user=wallet.user,
                wallet_id=wallet.wallet_id,
                wallet_type=wallet.wallet_type,
                transaction_type="FUND_TRANSFER_FROM_COMMISSION",
                amount=amount,
                balance_before=balance_before,
                balance_after=balance_after,
                liberty_reference=liberty_reference,
                liberty_commission=0.00,
                total_amount_received=amount,
                source_account_name="SELF",
                narration="COMMISSIONS_TRANSFER",
                status="SUCCESSFUL",
                transaction_leg="EXTERNAL",
            )

            User.objects.filter(pk=user_instance.pk).update(
                bills_pay_comm_balance=0.00,
                bills_pay_comm_balance_daily=0.00
            )

            fund_buddy_balance = WalletSystem.fund_balance(
                user=user_instance,
                wallet=wallet,
                amount=amount,
                trans_type="TRANSFER_BILLS_PAY_COMM",
                transaction_instance_id=create_transaction.transaction_id,
                unique_reference=create_transaction.transaction_id
            )

            ##########################################################################################################################################
            # SEND OUT APP NOTIFICATION
            not_token = user_instance.firebase_key
            not_title = "Transaction Successful"
            not_body = f"You have successfully trnasfered your commissions of N{amount}. to your main wallet"
            not_data = {"amount_sent": f"{amount}", "available_balance": f"{wallet.available_balance}"}

            send_out_notification = cloud_messaging.send_broadcast(
                token=not_token,
                title=not_title,
                body=not_body,
                data=not_data
            )

            InAppTransactionNotification.create_in_app_transaction_notification(
                user=user_instance,
                title=not_title,
                message_body=not_body
            )

            response = {
                "message": "Transfer successful"
            }

            return Response(response, status=status.HTTP_200_OK)
        # return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class TransferOtherCommissionsOutAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC, CheckWalletAvailable, CheckAccountAvailable]
    serializer_class = VASSerializer

    def post(self, request):
        # serializer = self.serializer_class(data=request.data)
        user_instance = request.user

        if user_instance.other_comm_balance < 1:
            response = {
                "error": "342",
                "message": "No commissions to transfer"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        else:

            wallet = WalletSystem.get_wallet(user_instance, "COLLECTION")
            amount = user_instance.other_comm_balance
            liberty_reference = f"LP-OTH-TRNSF-COMM{uuid.uuid4()}"

            # balance_before = fund_buddy_balance["balance_before"]

            balance_before = wallet.available_balance

            balance_after = WalletSystem.get_balance_after(
                user=user_instance,
                balance_before=balance_before,
                total_amount=amount,
                is_credit=True
            )

            # Create transaction for Commission
            create_transaction = Transaction.objects.create(
                user=wallet.user,
                wallet_id=wallet.wallet_id,
                wallet_type=wallet.wallet_type,
                transaction_type="FUND_TRANSFER_FROM_OTHER_COMMISSION",
                amount=amount,
                balance_before=balance_before,
                balance_after=balance_after,
                liberty_reference=liberty_reference,
                liberty_commission=0.00,
                total_amount_received=amount,
                source_account_name="SELF",
                narration="COMMISSIONS_TRANSFER",
                status="SUCCESSFUL",
                transaction_leg="EXTERNAL",
            )

            # Use F here
            User.objects.filter(pk=user_instance.pk).update(
                other_comm_balance=0.00,
                other_comm_balance_daily=0.00
            )

            fund_buddy_balance = WalletSystem.fund_balance(
                user=user_instance,
                wallet=wallet,
                amount=amount,
                trans_type="TRANSFER_OTHER_COMM",
                transaction_instance_id=create_transaction.transaction_id,
                unique_reference=create_transaction.transaction_id
            )

            ##########################################################################################################################################
            # SEND OUT APP NOTIFICATION
            not_token = user_instance.firebase_key
            not_title = "Transaction Successful"
            not_body = f"You have successfully trnasfered your commissions of N{amount}. to your main wallet"
            not_data = {"amount_sent": f"{amount}", "available_balance": f"{wallet.available_balance}"}

            send_out_notification = cloud_messaging.send_broadcast(
                token=not_token,
                title=not_title,
                body=not_body,
                data=not_data
            )

            InAppTransactionNotification.create_in_app_transaction_notification(
                user=user_instance,
                title=not_title,
                message_body=not_body
            )

            response = {
                "message": "Transfer successful"
            }

            return Response(response, status=status.HTTP_200_OK)


class RequestPosAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC, HasKYCLevelTwo, CheckWalletAvailable, CheckAccountAvailable]

    def post(self, request):
        user = request.user

        serializer = POSRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        request_payload = serializer.validated_data["requested_pos"]

        total_payable = sum([v["amount"] for k, v in request_payload.items()])

        verification_fee = ConstantTable.get_constant_table_instance().request_pos_verf_fee

        pos_request = serializer.save(user=user, total_payable=total_payable, verf_fee=verification_fee)

        data = {
            "status": True,
            "message": "Success",
            "request_id": pos_request.request_id,
            "payment_summary": pos_request.get_payment_summary
        }
        return Response(data=data, status=status.HTTP_201_CREATED)


class RequestDetailsAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):

        user = request.user
        request_id = request.query_params.get('request_id')
        if request_id is None:
            response = {
                "error": "549",
                "message": "No request id attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        request_details_qs = POSRequest.objects.filter(user=user, request_id=request_id)  # .last()
        if request_details_qs.exists():
            data = request_details_qs.first().get_request_details
        else:
            data = {
                "error": "invalid request ID"
            }

        return Response(data=data, status=status.HTTP_200_OK)


class RequestPosMakePaymentAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC, HasKYCLevelTwo, CheckWalletAvailable]

    def post(self, request):
        user = request.user
        serializer = PayForTerminalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        amount = serializer.validated_data["amount"]
        request_id = serializer.validated_data["request_id"]

        request_details = POSRequest.objects.filter(user=user, request_id=request_id).last()
        if not request_details:
            response = {
                "error": "invalid request ID"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        if request_details.payment_received and request_details.amount_received > 0:
            response = {
                "error": "This order has already been paid for"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        order_total = request_details.total_payable + request_details.verf_fee

        if request_details:
            if amount < order_total:
                response = {
                    "error": f"amount is less than order total. payable is {order_total}"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        terminal_payment_receiver = User.objects.filter(email=settings.TERMINAL_PAYMENT_USER).last()
        sender_wallet_instance = WalletSystem.get_wallet(user=user, from_wallet_type="COLLECTION")
        liberty_wallet_instance = WalletSystem.get_wallet(user=terminal_payment_receiver, from_wallet_type="COLLECTION")
        transaction_type = "TERMINAL_PURCHASE"

        # Check Balance against amount
        if sender_wallet_instance.available_balance < amount:
            return Response({"error": "error", "message": "Insufficient wallet balance"}, status=status.HTTP_400_BAD_REQUEST)

        transaction_pin = serializer.validated_data["transaction_pin"]
        check_pin = User.check_sender_transaction_pin(user=user, pincode=transaction_pin)
        if not check_pin:
            retries = User.count_down_transaction_pin_retries(user)
            response = {
                "error": "error",
                "message": "Incorrect Pin",
                "retry_count": retries["retry_count"],
                "remaining_retries": retries["remaining_retries"],
            }
            return Response(response, status=status.HTTP_401_UNAUTHORIZED)
        User.reset_transaction_pin_retries(user)

        narration = "Payment for terminal purchase"

        pay_terminal_commission(transaction_type, amount, user, sender_wallet_instance, narration, liberty_wallet_instance, terminal_payment_receiver)

        request_details.payment_received = True
        request_details.amount_received = order_total
        request_details.save()

        # CREATE COMMISSION INSTANCE FOR STAFF_AGENT AND SUPERVISOR
        manager_email = ConstantTable.get_constant_table_instance().manager_terminal_sales_email
        manager_user = User.objects.get(email=manager_email)

        agent_filter = SalesRep.objects.filter(sales_rep_code=request_details.relationship_officer_code)
        # agent_filter = User.objects.filter(referral_code=request_details.relationship_officer_code)
        agent = agent_filter.last() if agent_filter else None
        if agent is not None:
            # AGENT COMMISSION
            TerminalPurchaseCommission.objects.create(user=agent.sales_rep, pos_request=request_details)
            agent_teams = AgencyTeam.objects.filter(agents__in=[agent.sales_rep])
            agent_team = agent_teams.last() if agent_teams.exists() else None
            if agent_team and agent_team.supervisor:
                # SUPERVISOR COMMISSION
                TerminalPurchaseCommission.objects.create(
                    user=agent_team.supervisor, pos_request=request_details, commission_type="supervisor"
                )
            # MANAGER COMMISSION
            TerminalPurchaseCommission.objects.create(user=manager_user, pos_request=request_details, commission_type="manager")

        return Response(data={"message": "Payment successful"}, status=status.HTTP_201_CREATED)


# *******************************    VIEW   ******************************* #

class GetBalancesAPIView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated]
    serializer_class = UserBalanceFormSerializer
    user_serializer = UserBalanceSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():

            users = User.objects.filter(id__in=serializer.data.get("user_ids"))
            serializer = self.user_serializer(users, many=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

        else:

            response = serializer.errors
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class RetrieveTransactionUniquIDAPIView(generics.ListAPIView):
    permission_classes = [CheckDynamicAuthentication]
    serializer_class = ServiceTransactionHistorySerializer

    def get(self, request):
        custom_permission_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        request_user = custom_permission_user
        unique_ref = request.query_params.get('unique_reference')

        if unique_ref is None:
            response = {
                "error": "549",
                "message": "No Unique reference attached"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        get_data = Transaction.objects.filter(user=request_user).filter(Q(unique_reference=unique_ref) | Q(liberty_reference=unique_ref)).last()

        if get_data is None:
            response = {
                "error": "550",
                "message": "No Transaction Found"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        else:
            serializer = self.serializer_class(get_data)

            response = serializer.data
            return Response(response, status=status.HTTP_200_OK)


class BeginFundByUSSDTransAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC]
    serializer_class = BeginFundByUSSDSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            trans_type = serializer.validated_data["trans_type"]
            bank_code = serializer.validated_data["bank_code"]
            amount = serializer.validated_data["amount"]

            # if not AllBankList.objects.filter(bank_code=bank_code).exists():
            bank_inst = AllBankList.objects.filter(bank_code=bank_code).last()
            if not bank_inst:
                response = {
                    "error": "527",
                    "message": "Invalid Bank"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # Get IP ADDRESS
            address = request.META.get('HTTP_X_FORWARDED_FOR')
            if address:
                ip_addr = address.split(',')[-1].strip()
            else:
                ip_addr = request.META.get('REMOTE_ADDR')

            start_trans = StartFundByUSSDTran.create_fund_by_ussd_trans(
                user=request.user,
                amount_started=amount,
                bank_code=bank_code,
                bank_name=bank_inst.name,
                ip_addr=ip_addr,
                trans_type=trans_type
            )

            initiate_external_trans = ServicesVASApp.initiate_transaction(
                first_name=start_trans.first_name,
                last_name=start_trans.last_name,
                phone_number=start_trans.user.phone_number,
                user_email=start_trans.user.email,
                user_bvn=start_trans.user_bvn,
                bank_code=start_trans.bank_code,
                amount=start_trans.amount_started,
                liberty_reference=start_trans.liberty_reference
            )

            if initiate_external_trans.get("status") == "true":
                start_trans.redbiller_initiate_data = initiate_external_trans
                start_trans.ussd_code = initiate_external_trans.get("details").get("account").get("ussd_code")
                start_trans.save()

                response = {
                    "message": "USSD Code Successfully Generated",
                    "ussd_code": start_trans.ussd_code,
                    "amount": start_trans.amount_started,
                    "bank_name": start_trans.bank_name,
                    "reference": start_trans.liberty_reference
                }

            else:
                response = {
                    "error": "875",
                    "message": initiate_external_trans.get("message"),
                    "reference": start_trans.liberty_reference
                }

            return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RedBillerCallbackView(APIView):
    def post(self, request):
        response = request.data

        if response.get("details"):
            reference = response.get("details").get("reference")
            request_from = "CALLBACK"

            handle_data = StartFundByUSSDTran.handle_reference_from_funding(reference, request, request_from)

            return handle_data
        else:
            response = {
                "error": "549",
                "message": "No Reference Attached"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class VerifyFundByUSSDTransAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC]

    def get(self, request):
        reference = request.query_params.get('reference')
        request_from = "USER"

        handle_data = StartFundByUSSDTran.handle_reference_from_funding(reference, request, request_from)

        return handle_data

        # if reference is None:
        #     response = {
        #         "error": "549",
        #         "message": "No Reference Attached"
        #     }
        #     return Response(response, status=status.HTTP_404_NOT_FOUND)

        # get_trans = StartFundByUSSDTran.objects.filter(liberty_reference=reference).last()
        # if not get_trans:
        #     response = {
        #         "error":"527",
        #         "message": "Reference does not exist"
        #     }
        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # # Get IP ADDRESS
        # address = request.META.get('HTTP_X_FORWARDED_FOR')
        # if address:
        #     ip_addr = address.split(',')[-1].strip()
        # else:
        #     ip_addr = request.META.get('REMOTE_ADDR')

        # if get_trans.trans_complete:
        #     response = {
        #         "message": "Payment Receeived",
        #         "amount": get_trans.amount_resolved,
        #         "bank_name": get_trans.bank_name,
        #     }

        # else:
        #     verify_trans = ServicesVASApp.verify_redbiller_payment(liberty_reference=reference)

        #     if verify_trans.get("status") == True and "Approved" in verify_trans.get("details").get("status"):
        #         get_trans.trans_complete = True
        #         get_trans.redbiller_resolve_data = verify_trans
        #         get_trans.amount_resolved = verify_trans.get("details").get("amount")
        #         get_trans.charge = verify_trans.get("details").get("charge")
        #         get_trans.settlement = verify_trans.get("details").get("settlement")
        #         get_trans.check_ip_addr = ip_addr
        #         get_trans.save()

        #         if Transaction.objects.filter(liberty_reference=reference).exists():
        #             pass
        #         else:
        #             StartFundByUSSDTran.resolve_fund_by_ussd_trans(
        #                 user = request.user,
        #                 trans_instance = get_trans,
        #             )

        #         response = {
        #             "message": "Payment Receeived",
        #             "amount": get_trans.amount_resolved,
        #             "bank_name": get_trans.bank_name,
        #         }

        #     else:
        #         response = {
        #             "error": "546",
        #             "message": "No Payment",
        #             "amount_expected": get_trans.amount_started,
        #             "ussd_code": get_trans.ussd_code,
        #             "bank_name": get_trans.bank_name,
        #         }

        # return Response(response, status=status.HTTP_200_OK)


################################################
# Ledger
################################################


class RecordFunding(APIView):
    permission_classes = [CustomIsAuthenticated]

    def post(self, request):
        date = datetime.now().date()
        try:
            daily_funding_record = DailyFundingRecord.objects.filter(date=date, agent=request.user)
        except DailyFundingRecord.DoesNotExist:
            daily_funding_record = DailyFundingRecord.objects.create(
                agent=request.user,
                date=date
            )
        serializer = FundingRecordSerializer(data=request.data)
        if serializer.is_valid():
            serializer.validated_data['day'] = daily_funding_record
            serializer.save()
            return Response({
                'message': '',
                'status': 'error',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
        return Response({
            'message': '',
            'status': 'error',
            'data': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class FetchLedgerSummary(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        # get all transactions based on stipulated date
        try:
            custom_date = request.data.custom_date
        except Exception as e:
            custom_date = datetime.now().date()
        transactions = Transaction.objects.filter(user=request.user) \
            .filter(status='SUCCESSFUL') \
            .filter(date_created__date=custom_date).order_by('date_created__time')
        daily_funding_record = DailyFundingRecord.objects.filter(date=custom_date, agent=request.user).last()
        cash_in_hand = 0.0
        if daily_funding_record:
            cash_in_records = FundingRecord.objects.filter(day=daily_funding_record) \
                .filter(record_type='CASH_IN')
            if len(cash_in_records) > 0:
                for record in cash_in_records:
                    log_info(str(record.amount))
                    cash_in_hand += record.amount
        json_resp = {
            'tt': {
                'send_money': {'count': 0, 'amount': 0},
                'withdrawal': {'count': 0, 'amount': 0},
                'deposit': {'count': 0, 'amount': 0},
                'reversal': {'count': 0, 'amount': 0},
                'airtime': {'count': 0, 'amount': 0},
                'billsNPayments': {'count': 0, 'amount': 0},
            },
            'charges': {
                'bank': {'count': 0, 'amount': 0},
                'cash': {'count': 0, 'amount': 0},
            },
            'commissions': {},
            'summary': {}
        }
        document_resp = {}
        total_commission = 0
        wallet = WalletSystem.get_wallet(user=request.user, from_wallet_type="COLLECTION")
        send_money_choices = ['SEND_BANK_TRANSFER', 'SEND_BUDDY', 'SEND_LIBERTY_COMMISSION']
        withdrawal_choices = ['CARD_TRANSACTION_FUND_TRANSFER', 'CARD_TRANSACTION_FUND']
        deposit_choices = ['FUND_BUDDY', 'FUND_BANK_TRANSFER', 'FUND_PAYSTACK', 'FUND_TRANSFER_FROM_COMMISSION']
        reversal_choices = ['REVERSAL_BANK_TRANSFER', 'REVERSAL_BUDDY']
        for transaction in transactions:
            # json resp
            json_resp['charges']['bank']['count'] += 1
            json_resp['charges']['bank']['amount'] += transaction.liberty_commission + transaction.sms_charge
            if transaction.transaction_type in withdrawal_choices:
                json_resp['tt']['withdrawal']['count'] += 1
                json_resp['tt']['withdrawal']['amount'] += transaction.amount
            elif transaction.transaction_type in deposit_choices:
                json_resp['tt']['deposit']['count'] += 1
                json_resp['tt']['deposit']['amount'] += transaction.amount
            elif transaction.transaction_type in reversal_choices:
                json_resp['tt']['reversal']['count'] += 1
                json_resp['tt']['reversal']['amount'] += transaction.amount
            elif transaction.transaction_type in send_money_choices:
                json_resp['tt']['send_money']['count'] += 1
                json_resp['tt']['send_money']['amount'] += transaction.amount
            elif transaction.transaction_type == 'BILLS_AND_PAYMENT':
                json_resp['tt']['billsNPayments']['count'] += 1
                json_resp['tt']['billsNPayments']['amount'] += transaction.amount
            elif transaction.transaction_type == 'AIRTIME_PIN':
                json_resp['tt']['airtime']['count'] += 1
                json_resp['tt']['airtime']['amount'] += transaction.amount

            # commissions
            commission_amount = 0
            try:
                commission = CommissionsRecord.objects.get(transaction_id=transaction.id)
                other_commission = OtherCommissionsRecord.objects.get(transaction_id=transaction.id)
                commission_amount = commission.amount + other_commission.amount if other_commission.transaction_owner == "AGENT" else commission.amount
                total_commission += commission_amount
                if commission.bills_type in json_resp['commissions'].keys():
                    json_resp['commissions'][commission.bills_type] += commission_amount
                else:
                    json_resp['commissions'][commission.bills_type] = commission_amount
            except CommissionsRecord.DoesNotExist:
                pass
            except OtherCommissionsRecord.DoesNotExist:
                pass

            # document response
            terminal_type = None
            if transaction.terminal_id:
                terminal_type = Stock.objects.get(terminal_id=transaction.terminal_id).device_name
                if transaction.transaction_type == "CARD_TRANSACTION_FUND":
                    cash_in_hand -= transaction.amount
                elif transaction.transaction_type == "SEND_BANK_TRANSFER":
                    cash_in_hand += transaction.amount
                # to add other transaction types that would affect cash in hand
                # e.g. airtime, bills
            date = transaction.date_created.date()

            if str(date) in document_resp.keys():
                document_resp[str(date)].append(
                    {
                        'beneficiary': transaction.beneficiary_account_name,
                        'amount': transaction.amount,
                        'transaction_type': transaction.transaction_type,
                        'charges': transaction.sms_charge + transaction.liberty_commission,
                        'cash_in_bank': wallet.available_balance,
                        'cash_in_hand': cash_in_hand,
                        'total_cash': wallet.available_balance + cash_in_hand,
                        'commissions': commission_amount,
                        'terminal_type': terminal_type
                    }
                )
            else:
                document_resp[str(date)] = [{
                    'beneficiary': transaction.beneficiary_account_name,
                    'amount': transaction.amount,
                    'transaction_type': transaction.transaction_type,
                    'charges': transaction.sms_charge + transaction.liberty_commission,
                    'cash_in_bank': wallet.available_balance,
                    'cash_in_hand': cash_in_hand,
                    'total_cash': wallet.available_balance + cash_in_hand,
                    'commissions': commission_amount,
                    'terminal_type': terminal_type
                }]
        json_resp['summary'] = {
            'cash_in_bank': wallet.available_balance,
            'cash_in_hand': cash_in_hand,
            'commission': total_commission,
            'total': wallet.available_balance + cash_in_hand + total_commission
        }
        return Response({
            'message': '',
            'data': {'json': json_resp, 'document': document_resp}
        })


class DailyRewardAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        user_instance = request.user
        log_info(str(user_instance))
        today_date = datetime.now().date()
        log_info(str(today_date))
        send_money_count = TransactionReward.objects.filter(
            Q(transaction_type="SEND_MONEY", user=user_instance, transaction_date__date=today_date)).count()
        withdrawal_count = TransactionReward.objects.filter(
            Q(transaction_type="WITHDRAWAL", user=user_instance, transaction_date__date=today_date)).count()
        airtime_data_count = TransactionReward.objects.filter(
            Q(transaction_type="AIRTIME_DATA", user=user_instance, transaction_date__date=today_date)).count()
        total_coin = int(send_money_count + withdrawal_count + airtime_data_count)
        log_info(str(total_coin))
        eligible_coin = int(send_money_count + withdrawal_count)

        if eligible_coin >= int(settings.DAILY_REWARD_TARGET_COIN):
            transactions = TransactionReward.objects.filter(
                Q(transaction_date__date=today_date, user=user_instance) & Q(transaction_type__in=["WITHDRAWAL", "SEND_MONEY"])).aggregate(
                Sum('transaction_percentage_amount'))
            log_info(str(transactions))
            transaction_amount = transactions["transaction_percentage_amount__sum"] or float(0.00)
            amount = float(transaction_amount)
        else:
            amount = float(0.00)

        data = {
            "total_coin": total_coin,
            "eligible_coin": eligible_coin,
            "send_money": send_money_count,
            "withdrawal": withdrawal_count,
            "airtime_data_count": airtime_data_count,
            "cash_back": amount,
        }
        # done = TransactionReward.objects.create(coin_amount=3, amount=19.87, transaction_type='SEND_MONEY', last_updated='2022-10-05', user_id=user_instance, transaction_date='2022-10-05')
        return Response(data, status=status.HTTP_200_OK)


# class DailyRewardHistoryAPIView(APIView):
class DailyRewardHistoryAPIView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated, ]
    serializer_class = DailyRewardHistorySerializer
    pagination_class = CustomPagination

    def get_queryset(self):
        user = self.request.user
        instance = DailyRewardHistory.objects.filter(Q(user=user)).order_by("-transaction_date")
        return instance


class SingleDailyRewardHistoryAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        user = request.user
        try:
            history_id = request.query_params["history_id"]
            try:
                instance = DailyRewardHistory.objects.get(Q(user=user, id=history_id))

                data = {
                    "user_id": instance.user_id,
                    "history_id": instance.id,
                    "send_money_count": instance.send_money_count,
                    "withdrawal_count": instance.withdrawal_count,
                    "airtime_data_count": instance.airtime_data_count,
                    "transaction_count": instance.transaction_count,
                    "eligible_transaction_count": instance.eligible_transaction_count,
                    "coin": instance.coin,
                    "eligible_coin": instance.eligible_coin,
                    "cash_back_amount": instance.cash_back_amount,
                    "eligible_cash_back_amount": instance.eligible_cash_back_amount,
                    "transaction_date": instance.transaction_date

                }
                return Response(data, status=status.HTTP_200_OK)
            except DailyRewardHistory.DoesNotExist:
                data = {
                    "error": True,
                    "message": "history not found"
                }
                return Response(data, status=status.HTTP_404_NOT_FOUND)
            except ValueError:
                data = {
                    "error": True,
                    "message": "invalid history id "
                }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except MultiValueDictKeyError:
            return Response(
                {"params": "you must pass in the history_id"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        #     transaction_count = int(send_money_count + withdrawal_count + airtime_data_count)
        #     eligible_transaction_count = int(send_money_count + withdrawal_count)
        #     history_data = {
        #         "user_id": user_id,
        #         "send_money_count":send_money_count,
        #         "withdrawal_count":withdrawal_count,
        #         "airtime_data_count":airtime_data_count,
        #         "transaction_count": transaction_count,
        #         "eligible_transaction_count": eligible_transaction_count,
        #         "coin":coin,
        #         "cash_back_amount": cash_back_reward,
        #         "date": yesterday
        #     }
        #     user_history_data.append(history_data)
        #     print(user_history_data)
        # return Response(user_history_data, status=status.HTTP_200_OK)


class WeeklyRewardAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        user_id = request.user
        today_date = datetime.now().date()
        log_info(str(today_date))

        a_week = WeekHistory.objects.last()
        if a_week is None: return Response(
            {
                "weekly_coin": 0,
                "weekly_target": 0,
                "weekly_target_percentage": 0,
                "eligible_coin": 0,
                "reward_qualify": 0,
                "daily_rewards_data": {
                    "total_coin": 0,
                    "eligible_coin": 0,
                    "send_money": 0,
                    "withdrawal": 0,
                    "airtime_data_count": 0,
                    "cash_back": 0,
                }
            }, status=status.HTTP_200_OK)
        start_date = a_week.week_start_date
        end_date = a_week.week_end_date
        # week_data=TransactionReward.objects.filter(transaction_date__range=(start_date, end_date))

        transaction_coin = TransactionReward.objects.filter(Q(transaction_date__date__range=(start_date, end_date), user=user_id)).aggregate(
            Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or float(0)
        transaction_reward_coin = TransactionReward.objects.filter(Q(transaction_date__date__range=(start_date, end_date), user=user_id),
                                                                   transaction_type__in=["SEND_MONEY", "WITHDRAWAL"]).aggregate(Sum('coin_amount'))
        reward_coin = transaction_reward_coin["coin_amount__sum"] or float(0)
        weekly_target = int(settings.WEEKLY_REWARD_TARGET_COIN)
        weekly_target_percentage = math.floor((reward_coin / weekly_target) * 100)
        log_info(str(weekly_target_percentage))

        if weekly_target_percentage >= 0 | weekly_target_percentage < 100:
            percentage = weekly_target_percentage
            reward_qualify = False
        elif weekly_target_percentage >= 100:
            percentage = 100
            reward_qualify = True
        else:
            percentage = 0
            reward_qualify = False

        ### beginning of daily rewards
        send_money_count = TransactionReward.objects.filter(Q(transaction_type="SEND_MONEY", user=user_id, transaction_date__date=today_date)).count()
        withdrawal_count = TransactionReward.objects.filter(Q(transaction_type="WITHDRAWAL", user=user_id, transaction_date__date=today_date)).count()
        airtime_data_count = TransactionReward.objects.filter(
            Q(transaction_type="AIRTIME_DATA", user=user_id, transaction_date__date=today_date)).count()
        total_coin = int(send_money_count + withdrawal_count + airtime_data_count)
        log_info(str(total_coin))
        eligible_coin = int(send_money_count + withdrawal_count)

        if eligible_coin >= int(settings.DAILY_REWARD_TARGET_COIN):
            transactions = TransactionReward.objects.filter(
                Q(transaction_date__date=today_date, user=user_id) & Q(transaction_type__in=["WITHDRAWAL", "SEND_MONEY"])).aggregate(
                Sum('transaction_percentage_amount'))
            log_info(str(transactions))
            transaction_amount = transactions["transaction_percentage_amount__sum"] or float(0.00)
            amount = transaction_amount
        else:
            amount = float(0.00)

        daily_data = {
            "total_coin": total_coin,
            "eligible_coin": eligible_coin,
            "send_money": send_money_count,
            "withdrawal": withdrawal_count,
            "airtime_data_count": airtime_data_count,
            "cash_back": amount,
        }
        ### end of daily rewards data

        data = {
            "weekly_coin": coin,
            "weekly_target": weekly_target,
            "weekly_target_percentage": percentage,
            "eligible_coin": reward_coin,
            "reward_qualify": reward_qualify,
            "daily_rewards_data": daily_data,
        }
        log_info("start_date", start_date)
        log_info("date", end_date)

        return Response(data, status=status.HTTP_200_OK)


class WeeklyRewardHistoryAPIView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated, ]
    serializer_class = WeekHistorySerializer
    pagination_class = CustomPagination

    def get_queryset(self):
        a_week = WeekHistory.objects.all().order_by("-week_end_date")
        return a_week


class WeeklyRewardsDailyDataHistoryAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        user_id = request.user
        try:
            start_date = request.query_params["start_date"]
            end_date = request.query_params["end_date"]
        except MultiValueDictKeyError:
            return Response(
                {"params": "you must pass in the start_date and end_date"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        weekly_data = []

        filter_weekly_transaction = DailyRewardHistory.objects.filter(Q(transaction_date__range=(start_date, end_date)) & Q(user=user_id)).order_by(
            "-transaction_date")
        for _week in filter_weekly_transaction:
            w_data = {
                "send_money_count": _week.send_money_count,
                "withdrawal_count": _week.withdrawal_count,
                "airtime_data_count": _week.airtime_data_count,
                "transaction_date": _week.transaction_date
            }
            weekly_data.append(w_data)
        log_info(str(weekly_data))
        return Response(weekly_data, status=status.HTTP_200_OK)


class WeeklyRewardsDataHistoryAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        user_id = request.user
        try:
            start_date = request.query_params["start_date"]
            end_date = request.query_params["end_date"]
        except MultiValueDictKeyError:
            return Response(
                {"params": "you must pass in the start_date and end_date"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        date = datetime.strptime(start_date, '%Y-%m-%d')
        log_info(str(date))
        transaction_month = date.strftime("%B")
        month = date.strftime("%m")
        transaction_year = date.strftime("%Y")
        transaction_day = date.strftime("%d")
        week_num_qs = datetime(int(transaction_year), int(month), int(transaction_day)).isocalendar()
        log_info("week_num_qs", week_num_qs)
        week_num = datetime(int(transaction_year), int(month), int(transaction_day)).isocalendar()[1]

        log_info("week_num", week_num)
        log_info(str(type(week_num)))
        transaction_coin = TransactionReward.objects.filter(Q(transaction_date__date__range=(start_date, end_date), user=user_id)).aggregate(
            Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or float(0.00)
        transaction_reward_coin = TransactionReward.objects.filter(Q(transaction_date__date__range=(start_date, end_date), user=user_id),
                                                                   transaction_type__in=["SEND_MONEY", "WITHDRAWAL"]).aggregate(Sum('coin_amount'))
        reward_coin = transaction_reward_coin["coin_amount__sum"] or float(0.00)
        log_info(str(reward_coin))
        weekly_target = int(settings.WEEKLY_REWARD_TARGET_COIN)
        log_info(str(weekly_target))
        weekly_target_percentage = math.floor((reward_coin / weekly_target) * 100)
        log_info(str(weekly_target_percentage))

        if weekly_target_percentage >= 0 | weekly_target_percentage < 100:
            percentage = weekly_target_percentage
            reward_qualify = False
        elif weekly_target_percentage >= 100:
            percentage = 100
            reward_qualify = True
        else:
            percentage = 0
            reward_qualify = False

        data = {
            "Month": transaction_month,
            "Week": week_num,
            "weekly_coin": coin,
            "weekly_target": weekly_target,
            "weekly_target_percentage": percentage,
            "elibgible_coin": reward_coin,
            "reward_qualify": reward_qualify,
        }
        return Response(data, status=status.HTTP_200_OK)


class WeekHistoryFilterAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        user_id = request.user
        try:
            month_num = int(request.query_params["month"])
            year = int(request.query_params["year"])
            week_num = int(request.query_params["week"])
        except MultiValueDictKeyError:
            return Response(
                {"params": "you must pass in the month, year and week"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if month_num <= 0 or month_num > 12: return Response({"message": "Enter a valid month number"}, status.HTTP_400_BAD_REQUEST)
        if week_num <= 0 or week_num > 4: return Response({"message": "Enter a valid week 1 to 4 number"}, status.HTTP_400_BAD_REQUEST)
        month_name = calendar.month_name[month_num]

        log_info(str(month_name))
        week_data = []

        def filter_week(year, month_name, week_num):
            month_number = list(calendar.month_name).index(month_name)
            days = monthrange(year, month_number)
            month_days = days[1]
            log_info(str(days))
            calculated_week = 7 * week_num
            log_info("cals", calculated_week)
            if calculated_week < month_days:
                date = f'{year}-{month_number}-{calculated_week}'
                return date
            else:
                return "Not found"

        get_week = filter_week(year, month_name, week_num)
        log_info(str(get_week))

        week_filter = WeekHistory.objects.filter(Q(week_start_date__lte=get_week) & Q(week_end_date__gte=get_week)).order_by("-week_end_date")
        date_list = []
        for _ in week_filter:
            date_list.append(_.week_start_date)
            date_list.append(_.week_end_date)
        log_info(str(date_list))
        try:
            start_date = date_list[0]
            end_date = date_list[1]
        except IndexError:
            return Response({"message": "please use a correct week format"}, status=status.HTTP_400_BAD_REQUEST)

        transaction_month = start_date.strftime("%B")

        transaction_coin = TransactionReward.objects.filter(Q(transaction_date__date__range=(start_date, end_date), user=user_id)).aggregate(
            Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or 0
        transaction_reward_coin = TransactionReward.objects.filter(Q(transaction_date__date__range=(start_date, end_date), user=user_id),
                                                                   transaction_type__in=["SEND_MONEY", "WITHDRAWAL"]).aggregate(Sum('coin_amount'))
        reward_coin = transaction_reward_coin["coin_amount__sum"] or 0
        weekly_target = int(settings.WEEKLY_REWARD_TARGET_COIN)
        log_info(str(weekly_target))
        weekly_target_percentage = math.floor((reward_coin / weekly_target) * 100)
        log_info(str(weekly_target_percentage))

        if weekly_target_percentage >= 0 | weekly_target_percentage < 100:
            percentage = weekly_target_percentage
            reward_qualify = False
        elif weekly_target_percentage >= 100:
            percentage = 100
            reward_qualify = True
        else:
            percentage = 0
            reward_qualify = False

        weekly_data = []
        filter_weekly_transaction = DailyRewardHistory.objects.filter(Q(transaction_date__range=(start_date, end_date)) & Q(user=user_id))
        # date_filter = TransactionReward.objects.filter(Q(transaction_date__in=get_week, user=user_id, transaction_type__in=["SEND_MONEY", "WITHDRAWAL"]))
        for _week in filter_weekly_transaction:
            data = {
                "send_money_count": _week.send_money_count,
                "withdrawal_count": _week.withdrawal_count,
                "airtime_data_count": _week.airtime_data_count,
                "transaction_date": _week.transaction_date
            }
            weekly_data.append(data)
        log_info(str(weekly_data))
        data = {
            "Month": transaction_month,
            "Week": week_num,
            "weekly_coin": coin,
            "weekly_target": weekly_target,
            "weekly_target_percentage": percentage,
            "elibgible_coin": reward_coin,
            "reward_qualify": reward_qualify,
            "week_data": weekly_data
        }
        week_data.append(data)
        return Response(week_data, status=status.HTTP_200_OK)


class MonthlyRewardAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        user_instance = request.user
        today_month = datetime.now().month
        today_year = datetime.now().year
        log_info(str(today_month))
        send_money_count = TransactionReward.objects.filter(
            Q(transaction_type="SEND_MONEY", user=user_instance, transaction_date__year=today_year, transaction_date__month=today_month)).count()
        withdrawal_count = TransactionReward.objects.filter(
            Q(transaction_type="WITHDRAWAL", user=user_instance, transaction_date__year=today_year, transaction_date__month=today_month)).count()
        airtime_data_count = TransactionReward.objects.filter(
            Q(transaction_type="AIRTIME_DATA", user=user_instance, transaction_date__year=today_year, transaction_date__month=today_month)).count()
        total_coin = int(send_money_count + withdrawal_count + airtime_data_count)
        eligible_coin = int(send_money_count + withdrawal_count)
        transactions = TransactionReward.objects.filter(Q(transaction_date__year=today_year, transaction_date__month=today_month, user=user_instance),
                                                        transaction_type__in=["WITHDRAWAL", "SEND_MONEY"]).aggregate(
            Sum('transaction_percentage_amount'))
        log_info(str(transactions))
        try:
            cash_back_amount = round(transactions["transaction_percentage_amount__sum"], 2) or float(0.00)
            data = {
                "total_coin": total_coin,
                "eligible_coin": eligible_coin,
                "send_money": send_money_count,
                "withdrawal": withdrawal_count,
                "airtime_data_count": airtime_data_count,
                "cash_back": cash_back_amount,
            }
        except TypeError:
            data = {
                "total_coin": total_coin,
                "eligible_coin": eligible_coin,
                "send_money": send_money_count,
                "withdrawal": withdrawal_count,
                "airtime_data_count": airtime_data_count,
                "cash_back": 0.00,
            }
        return Response(data, status=status.HTTP_200_OK)


class MonthlyRewardHistoryAPIView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated, ]
    serializer_class = MonthlyRewardHistorySerializer
    pagination_class = CustomPagination

    def get_queryset(self):
        monthly_data = MonthlyRewardHistory.objects.filter(user=self.request.user).order_by("-date_created")
        return monthly_data


class MonthlyRewardFilterHistoryAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        try:
            year = str(request.query_params["year"])
            month = str(request.query_params["month"])
        except MultiValueDictKeyError:
            return Response(
                {"params": "you must pass in the year and month"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        user_data = []
        try:
            monthly_data = MonthlyRewardHistory.objects.filter(user=request.user, date_created__year=year).filter(date_created__month=month)
            for data in monthly_data:
                user_data.append(
                    {
                        "cashback_amount": data.cash_back_amount,
                        "date": data.date_created,
                        "total_coin": data.total_coin,
                        "eligible_coin": data.eligible_coin,
                        "send_money_count": data.send_money_count,
                        "withdrawal_count": data.withdrawal_count,
                        "airtime_data_count": data.airtime_data_count
                    }
                )
            all_data = {
                "user_data": user_data
            }
            return Response(all_data, status=status.HTTP_200_OK)
        except ValueError:
            return Response({"message": "enter a valid year or month"}, status=status.HTTP_400_BAD_REQUEST)


class LeaderBoardFilterbyDateTypeAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        user = request.user
        try:
            device_type = request.query_params["device_param"].upper()
            date_param = request.query_params["date_param"].lower()
            log_info(str(device_type))
            log_info(str(date_param))
        except MultiValueDictKeyError:
            return Response(
                {"params": "you must pass in the device_param and date_param"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        get_rank = get_leader_board_by_date_type(device_type, date_param, user)
        return Response(get_rank, status=status.HTTP_200_OK)


class LeaderBoardFilterbyTodayAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        user = request.user
        try:
            device_type = request.query_params["device_param"].upper()
            date_param = request.query_params["date_param"]
            log_info(str(device_type))
            log_info(str(date_param))
        except MultiValueDictKeyError:
            return Response(
                {"params": "you must pass in the device_param and date_param"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        def get_params(device_type, date_param):
            user_email = str(user.email)
            if device_type == "POS" or device_type == "MOBILE":
                date = date_param
                try:
                    instance = TransactionReward.objects.distinct("user").all().filter(device_type=device_type, transaction_date__date=date).values(
                        'user', 'user__first_name', 'user__last_name', 'user__phone_number')
                    get_rank = today_rank(device_type, date, user_email, instance)
                except ValidationError:
                    get_rank = {"message": "enter a valid date YYYY-MM-DD"}
            else:
                get_rank = {"message": "enter a valid date or device type"}
            return get_rank

        get_rank = get_params(device_type, date_param)
        return Response(get_rank, status=status.HTTP_200_OK)


class LeaderBoardFilterByLastSevenDaysAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        user = request.user
        try:
            device_type = request.query_params["device_param"].upper()
            date_param = request.query_params["date_param"].lower()
            log_info(str(device_type))
            log_info(str(date_param))
        except MultiValueDictKeyError:
            return Response(
                {"params": "you must pass in the device_param and date_param"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        def get_params(device_type, date_param):
            user_email = str(user.email)
            today_date = datetime.now().date()
            filter_date = datetime.now().date() - timedelta(days=6)
            log_info(str(today_date))
            log_info(str(filter_date))
            date = (filter_date, today_date)
            log_info(str(date))
            if date_param == "last_seven_days":
                if device_type == "POS" or device_type == "MOBILE":
                    instance = TransactionReward.objects.distinct("user").all().filter(device_type=device_type, transaction_date__range=date).values(
                        'user', 'user__first_name', 'user__last_name', 'user__phone_number')
                    get_rank = last_seven_days_rank(device_type, date, user_email, instance)
                else:
                    get_rank = {"message": "enter a valid device_type"}
            else:
                get_rank = {"message": "enter a valid date or device type"}
            return get_rank

        get_rank = get_params(device_type, date_param)
        return Response(get_rank, status=status.HTTP_200_OK)


class LeaderBoardFilterByLastMonthAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, ]

    def get(self, request):
        try:
            device_type = request.query_params["device_param"].upper()
            date_param = request.query_params["date_param"].lower()
        except MultiValueDictKeyError:
            return Response(
                {"params": "you must pass in the device_param and date_param"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        user = request.user
        user_email = str(user.email)
        today_date = datetime.now().date()
        first = today_date.replace(day=1)
        last_month = first - timedelta(days=1)
        today_month = last_month.month
        today_year = last_month.year

        def get_params(device_type, date_param, today_year, today_month):
            user_email = str(user.email)
            if date_param == "last_month":
                if device_type == "POS" or device_type == "MOBILE":
                    month = last_month.month
                    year = last_month.year
                    instance = TransactionReward.objects.distinct("user").all().filter(device_type=device_type, transaction_date__year=year,
                                                                                       transaction_date__month=month).values('user',
                                                                                                                             'user__first_name',
                                                                                                                             'user__last_name',
                                                                                                                             'user__phone_number')
                    get_rank = monthly_rank(device_type, today_year, today_month, user_email, instance)
                else:
                    get_rank = {"message": "enter a valid device_type"}
            else:
                get_rank = {"message": "enter a valid date or device type"}
            return get_rank

        get_rank = get_params(device_type, date_param, today_year, today_month)
        return Response(get_rank, status=status.HTTP_200_OK)


class RecreateTransactionVerfObject(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]
    serializer_class = RecreateTransactionVerfObjectSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            trans_ids = serializer.validated_data["trans_ids"]

            no_trans_list = []
            verf_found = []
            created_verfs = []

            if len(trans_ids) > 0:
                for trans_id in trans_ids:
                    get_trans = Transaction.objects.filter(id=trans_id, transaction_type__in=["SEND_BANK_TRANSFER", "SEND_LIBERTY_COMMISSION",
                                                                                              "SEND_BACK_TO_FLOAT_TRANSFER", "REVERSAL_BANK_TRANSFER",
                                                                                              "SEND_BACK_TO_FLOAT_TRANSFER"]).last()
                    if not get_trans:
                        no_trans_list.append(trans_id)
                    else:
                        get_verf = TransferVerificationObject.objects.filter(liberty_reference=get_trans.liberty_reference).last()
                        if get_verf:
                            verf_found.append(trans_id)
                        else:
                            escrow_instance = Escrow.objects.filter(escrow_id=get_trans.escrow_id).last()

                            if get_trans.transaction_leg in ["INTERNAL", "REVERSAL"]:
                                amount = escrow_instance.amount + (
                                            escrow_instance.send_money_transfer_fee - escrow_instance.send_money_transfer_extra_fee)
                            else:
                                amount = escrow_instance.amount

                            verification_instance = dict(
                                transaction_instance=get_trans,
                                user_id=get_trans.user.id,
                                user_email=get_trans.user.email,
                                account_provider=get_trans.account_provider,
                                transaction_leg=get_trans.transaction_leg,
                                transaction_type=get_trans.transaction_type,
                                amount=amount,
                                escrow_id=get_trans.escrow_id,
                                liberty_reference=get_trans.liberty_reference
                            )

                            create_verf = TransferVerificationObject.objects.create(**verification_instance)

                            created_verfs.append(trans_id)

                response = {
                    "message": "Successful",
                    "no_trans_list": no_trans_list,
                    "verf_found": verf_found,
                    "created_verfs": created_verfs
                }

            else:

                response = {
                    "status": "error",
                    "message": "No IDs attached"
                }

            return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class NotifyAppTransAPIView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated]
    serializer_class = NotifyAppTransSerializer

    def get(self, request):

        unique_id = request.query_params.get("unique_id")

        if unique_id is not None or unique_id == " ":
            try:
                clean_unique_id = int(unique_id)
                get_trans = OtherAppTransNotify.objects.filter(id=clean_unique_id).last()
                if get_trans:
                    serializer = self.serializer_class(get_trans)
                    response = {
                        "status": True,
                        "data": serializer.data
                    }
                    return Response(response, status=status.HTTP_200_OK)
                else:

                    response = {
                        "error": "549",
                        "message": "No Transactions with given ID"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
            except:
                response = {
                    "error": "550",
                    "message": "Unique ID must be numberic"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # Calculate the start and end dates for the range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=4)

        queryset = OtherAppTransNotify.objects.filter(date_added__range=[start_date, end_date])
        if queryset:

            notify = Paginator.paginate(request=request, queryset=queryset, size=20)

            serializer = self.serializer_class(notify, many=True)

            response = {
                "status": True,
                "count": len(serializer.data),
                "data": serializer.data
            }
        else:
            response = {
                "status": True,
                "count": 0,
                "data": []
            }

        return Response(response, status=status.HTTP_200_OK)


# class CreateSubAccountPersonal(APIView):
#     serializer_class = CreateSubAccountPersonalSerializer
#     permission_classes = [CustomIsAuthenticated, AdminLockPermission]

#     def post(self, request):
#         request_data = request.data

#         serializer = self.serializer_class(data=request_data)

#         if serializer.is_valid():
#             master_email = serializer.validated_data["master_email"]
#             for_user_email = serializer.validated_data["for_user_email"]
#             provider = serializer.validated_data["provider"]

#             if ConstantTable.get_constant_table_instance().allow_create_other_accounts == True:

#                 master_user = User.objects.filter(email=master_email).last()
#                 if not master_user:
#                     response = {
#                         "error":"412",
#                         "message": "No user exists with entered email"
#                     }
#                     return Response(response, status=status.HTTP_400_BAD_REQUEST)

#                 else:

#                     if master_user.check_kyc.bvn_rel.is_verified == True and master_user.check_kyc.bvn_rel.bvn_number != None:


#                     create_acct = OtherServiceAccountSystem.create_vfd_others_collection_acct(user=user, requested_by=request.user, overide=new_overide)

#                         response = {
#                             "data": create_acct,
#                             "overide": new_overide
#                         }
#                         return Response(response, status=status.HTTP_200_OK)
#                     else:
#                         response = {
#                             "error":"649",
#                             "message": "User does not have BVN",
#                             "overide": overide
#                         }
#                         return Response(response, status=status.HTTP_400_BAD_REQUEST)


#             else:
#                 response = {
#                     "error":"650",
#                     "message": "Cannot create account at this time, please try again later"
#                 }
#                 return Response(response, status=status.HTTP_400_BAD_REQUEST)

#         return Response(serializer.errors, status=status.HTTP_200_OK)


class CreateOtherAccountCorporate(APIView):
    serializer_class = CreateCorporateAccountSerializer
    permission_classes = [CheckDynamicAuthentication]

    def post(self, request):
        custom_permission_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        request_user = custom_permission_user

        request_data = request.data

        serializer = self.serializer_class(data=request_data)

        if serializer.is_valid():
            corporate_id = serializer.validated_data["corporate_id"]
            company_name = serializer.validated_data["company_name"]
            # incorp_date = serializer.validated_data["incorp_date"]
            # bvn = serializer.validated_data["bvn"]
            for_user_email = serializer.validated_data["for_user_email"]

            # bvn_obj = BVNDetail.objects.filter(kyc__user__email=for_user_email, bvn_number=bvn).first()
            # bvn_obj = BVNDetail.objects.filter(kyc__user__email=for_user_email).first()
            # if not bvn_obj or not bvn_obj.bvn_number:
            #     response = {
            #         "status":"error",
            #         "message": "No user data exists"
            #     }
            #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # else:
            # bvn_user = bvn_obj.kyc.user
            bvn_user = User.objects.filter(email=for_user_email).last()
            if not bvn_user:
                response = {
                    "status": "error",
                    "message": "Entered User Email Does Not Exist"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # if get_for_user.check_kyc.bvn_rel.bvn_number != bvn:
            #     response = {
            #         "status":"error",
            #         "message": "BVN does not match 'for_user' bvn"
            #     }
            #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # if get_for_user.check_kyc.bvn_rel.bvn_number != bvn:
            #     response = {
            #         "status":"error",
            #         "message": "BVN does not match 'for_user' bvn"
            #     }
            #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # if Other.check_kyc.bvn_rel.bvn_number != bvn:
            #     response = {
            #         "status":"error",
            #         "message": "BVN does not match 'for_user' bvn"
            #     }
            #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # bvn_user = get_for_user

            get_corporate = CorporateAccount.objects.filter(onboarded_by=request_user, user=bvn_user, corporate_id=corporate_id).last()
            if not get_corporate:
                response = {
                    "status": "error",
                    "message": "No Profiled RC Number",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            bvn_number = get_corporate.bvn
            incorp_date = get_corporate.incorp_date

            if settings.ENVIRONMENT == "development":
                is_test_account = True
            else:
                is_test_account = False

            create_corp = OtherServiceAccountSystem.create_corporate_account(
                user=bvn_user,
                is_test_account=is_test_account,
                requested_by=request_user,
                rc_number=get_corporate.rc_number,
                incorp_date=incorp_date,
                company_name=company_name,
                bvn=bvn_number,
            )

            response = {"message": create_corp}
            return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_200_OK)


class CreateOtherAccountPersonal(APIView):
    serializer_class = CreatePersonalAccountSerializer
    permission_classes = [CustomIsAuthenticated, LottoUserPermission]

    def post(self, request):
        request_data = request.data
        requested_by = self.request.user

        serializer = self.serializer_class(data=request_data)

        if serializer.is_valid():
            user_id = serializer.validated_data["user_id"]
            provider = serializer.validated_data["provider"]
            overide = serializer.validated_data.get("overide")
            extra = serializer.validated_data.get("extra", False)

            if ConstantTable.get_constant_table_instance().allow_create_other_accounts == True:

                user = User.objects.filter(id=user_id).last()
                if not user:
                    response = {
                        "error": "412",
                        "message": "No user exists with entered ID"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:

                    if user.check_kyc.bvn_rel.is_verified == True and user.check_kyc.bvn_rel.bvn_number != None:

                        if overide != None:
                            new_overide = overide
                        else:
                            new_overide = False

                        get_coll_account = AccountSystem.objects.filter(user=user, account_type="COLLECTION").last()

                        if not get_coll_account:
                            response = {
                                "error": "650",
                                "message": "User must create wallet first",
                            }
                            return Response(response, status=status.HTTP_400_BAD_REQUEST)

                        if get_coll_account.true_account_type == "CORPORATE":
                            import random
                            random_choice = random.choice([1, 2, 3, 4, 5, 6, 7, 8, 9, 0])

                            create_acct_corp = OtherServiceAccountSystem.arrange_corporate_detail(user=requested_by, new_user=user,
                                                                                                  corporate_id="A41D7E" if settings.ENVIRONMENT == "development" else "CF1F5A",
                                                                                                  suffix=None, get_location=None,
                                                                                                  company_name=f"AJO {get_coll_account.account_name} 1{random_choice}" if get_coll_account.user.type_of_user == "AJO_AGENT" else f"{get_coll_account.account_name} 1{random_choice}",
                                                                                                  others=True)

                            if create_acct_corp.get("data"):
                                create_acct = create_acct_corp.get("data")
                            else:
                                create_acct = create_acct_corp


                        else:
                            create_acct = OtherServiceAccountSystem.create_vfd_others_collection_acct(user=user, requested_by=requested_by,
                                                                                                      overide=new_overide, extra=extra)

                        response = {
                            "data": create_acct,
                            "overide": new_overide
                        }
                        return Response(response, status=status.HTTP_200_OK)
                    else:
                        response = {
                            "error": "649",
                            "message": "User does not have BVN",
                            "overide": overide
                        }
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)


            else:
                response = {
                    "error": "650",
                    "message": "Cannot create account at this time, please try again later"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_200_OK)


class GetOtherAccountsAPIView(APIView):
    serializer_class = GetOtherAccountSerializer
    permission_classes = [CustomIsAuthenticated, LottoUserPermission]

    def get(self, request):

        request_user = request.user

        account_num = request.query_params.get('account_num')

        if account_num is None:
            wall_det = request_user.wallets.filter(wallet_type="COLLECTION").last()
            acct_det = request_user.accounts.filter(account_type="COLLECTION").last()
            response = {
                "user_id": request_user.id,
                "account_provider": acct_det.account_provider,
                "account_number": acct_det.account_number,
                "account_name": acct_det.account_name,
                "bank_name": acct_det.bank_name,
                "bank_code": acct_det.bank_code,
                "is_test": acct_det.is_test,
                "is_active": acct_det.is_active,
                "available_balance": wall_det.available_balance

            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        get_account_det = OtherServiceAccountSystem.objects.filter(account_number=account_num).last()
        if not get_account_det:
            response = {
                "error": "550",
                "message": "No Account Found"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        else:
            if request.user != get_account_det.requested_by and request.user.email not in ["<EMAIL>", "<EMAIL>"]:
                response = {
                    "error": "650",
                    "message": "Account Does Not Exist For This User"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

            serializer = self.serializer_class(get_account_det)

            response = serializer.data
            return Response(response, status=status.HTTP_200_OK)


class ReverseManyFundTransAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = LibertyRefListSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            lib_refs = serializer.validated_data.get("lib_refs")
            owner = serializer.validated_data.get("owner")

            if lib_refs == None:
                response = {
                    "error": "error",
                    "message": "lib refs cannot be empty"
                }

                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            else:

                get_owner = User.objects.filter(email=owner).last()
                if get_owner:

                    finished = []
                    unfinished = []

                    for ref in lib_refs:
                        check_trans = Transaction.objects.filter(unique_reference=ref).last()
                        if not check_trans:
                            unfinished.append(ref)
                            pass
                        else:
                            if check_trans.user != get_owner:

                                old_user = check_trans.user

                                # Start Reversal

                                debit_credit_trans = DebitCreditRecordOnAccount.objects.filter(user=get_owner).last()
                                new_balance_before = debit_credit_trans.balance_after
                                new_balance_after = new_balance_before + check_trans.amount

                                get_user_account = AccountSystem.objects.filter(user=get_owner).filter(account_type__in=["COLLECTION"]).last()

                                if get_user_account:
                                    wallet_instance = get_user_account.wallet
                                    account_id = get_user_account.account_id
                                    wallet_id = wallet_instance.wallet_id
                                    wallet_type = wallet_instance.wallet_type
                                else:
                                    account_id = None
                                    wallet_id = None
                                    wallet_type = None

                                check_trans.balance_before = new_balance_before
                                check_trans.balance_after = new_balance_after
                                check_trans.user = get_owner
                                check_trans.user_full_name = get_owner.bvn_full_name
                                check_trans.user_email = get_owner.email
                                check_trans.account_id = account_id
                                check_trans.wallet_id = wallet_id
                                check_trans.is_other_account = False
                                check_trans.is_other_account_number = None
                                check_trans.is_other_account_owner = None

                                check_trans.save()

                                # SEND BACK TO FLOAT TRANS
                                send_back_to_float_trans = Transaction.objects.filter(escrow_id=check_trans.escrow_id,
                                                                                      transaction_type="SEND_BACK_TO_FLOAT_TRANSFER").last()
                                if not send_back_to_float_trans:
                                    unfinished.append(ref)
                                    pass
                                else:
                                    send_back_to_float_trans.user = get_owner
                                    send_back_to_float_trans.user_full_name = get_owner.bvn_full_name
                                    send_back_to_float_trans.user_email = get_owner.email
                                    send_back_to_float_trans.account_id = account_id
                                    send_back_to_float_trans.wallet_id = wallet_id

                                    send_back_to_float_trans.save()

                                    # TRANSFER VERFF
                                    get_trans_verf = TransferVerificationObject.objects.filter(
                                        liberty_reference=send_back_to_float_trans.liberty_reference).last()
                                    if not get_trans_verf:
                                        unfinished.append(ref)
                                        pass
                                    else:
                                        get_trans_verf.user_id = get_owner.id
                                        get_trans_verf.user_email = get_owner.email

                                        get_trans_verf.save()

                                        # ESCROW
                                        get_escrow_obj = Escrow.objects.filter(escrow_id=check_trans.escrow_id).last()
                                        if not get_escrow_obj:
                                            unfinished.append(ref)

                                        else:
                                            get_escrow_obj.user = get_owner
                                            get_escrow_obj.from_wallet_id = wallet_id
                                            get_escrow_obj.balance_before = new_balance_before
                                            get_escrow_obj.balance_after = new_balance_after
                                            get_escrow_obj.user_account_name = get_owner.bvn_full_name

                                            get_escrow_obj.save()

                                            # DEBIT CREDIT:

                                            wrong_dcs = DebitCreditRecordOnAccount.objects.filter(user=old_user,
                                                                                                  transaction_instance_id=check_trans.transaction_id)

                                            for dc in wrong_dcs:
                                                dc.delete()

                                            fund_wallet = WalletSystem.fund_balance(
                                                user=get_owner,
                                                wallet=wallet_instance,
                                                amount=check_trans.amount,
                                                trans_type="FUND_BANK_TRANSFER",
                                                transaction_instance_id=check_trans.transaction_id
                                            )

                                            finished.append(ref)

                    response = {
                        "status": "success",
                        "message": "DONE PROCESSING",
                        "finished": finished,
                        "unfinished": unfinished
                    }

                    return Response(response, status=status.HTTP_200_OK)


                else:
                    response = {
                        "error": "error",
                        "message": "owner does not exist"
                    }

                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class POSRequestHistoryAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        user = request.user
        request_history_qs = POSRequest.objects.filter(user=user)
        if request_history_qs.exists():
            paginated_data = Paginator.paginate(request=request, queryset=request_history_qs)
            serializer = POSRequestHistorySerializer(paginated_data, many=True)
            data = {
                "status": True,
                "message": "POS request(s) successfully fetched.",
                "data": serializer.data
            }
        else:
            data = {
                "status": False,
                "message": "User has not made any request yet.",
                "data": []
            }
        return Response(data=data, status=status.HTTP_200_OK)


# Deprecated
class FetchTransWithEscrowID(APIView):
    permission_classes = [CheckDynamicAuthentication]
    serializer_class = GetTransWithEscrowSerializer

    def get(self, request):
        custom_permission_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        request_user = custom_permission_user

        escrow_id = request.query_params.get('escrow_id')
        if escrow_id is None:
            response = {
                "status": "error",
                "message": "No escrow id attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            if request_user.email == "<EMAIL>":
                get_escrow_inst = Escrow.objects.filter(Q(escrow_id=escrow_id) | Q(customer_reference=escrow_id)).first()
            else:
                get_escrow_inst = Escrow.objects.filter(Q(user=request_user) & Q(escrow_id=escrow_id) | Q(customer_reference=escrow_id)).first()

            if get_escrow_inst:

                trans_details_qs = Transaction.objects.filter(escrow_id=get_escrow_inst.escrow_id)

                if trans_details_qs:
                    trans_leg_list = list(trans_details_qs.values_list('transaction_leg', flat=True))

                    if "EXTERNAL" in trans_leg_list:
                        serializer = self.serializer_class(trans_details_qs.filter(transaction_leg="EXTERNAL").last())
                    elif "TEMP_EXTERNAL" in trans_leg_list:
                        serializer = self.serializer_class(trans_details_qs.filter(transaction_leg="INTERNAL").last())
                    elif "REVERSAL" in trans_leg_list:
                        serializer = self.serializer_class(trans_details_qs.filter(transaction_leg="REVERSAL").last())
                    else:
                        serializer = self.serializer_class(trans_details_qs.filter(transaction_leg="INTERNAL").last())

                    new_data = serializer.data
                    new_data["customer_reference"] = get_escrow_inst.customer_reference
                    new_data["bulk_id"] = get_escrow_inst.bulk_id

                    return Response(new_data, status=status.HTTP_200_OK)

                else:
                    response = {
                        "status": "error",
                        "message": "transaction with escrow ID does not exist"
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)
            else:
                response = {
                    "status": "error",
                    "message": "transaction with ID does not exist"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        except:
            response = {
                "status": "error",
                "message": "transaction reference ID is not a valid ID"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class FetchTransactionVersion2(APIView):
    permission_classes = [CheckDynamicAuthentication]
    serializer_class = EscrowRetrieveTransactionSerializer

    def get(self, request):
        log_info("At the headd of get")
        custom_permission_user = [*self.permission_classes][0]().has_permission(request, self.as_view())

        request_user = custom_permission_user

        log_info(str(request_user))

        reference = request.query_params.get('reference')
        if reference is None:
            response = {
                "status": "error",
                "error": "577",
                "message": "No Reference Attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        group_names = ['Tech Support']

        if request_user.email in ["<EMAIL>", "<EMAIL>"] or request.user.groups.filter(name__in=group_names) != None:
            log_info("came here")
            get_escrow_inst = Escrow.objects.select_related('user').filter(Q(escrow_id=reference) | Q(customer_reference=reference)).first()
        else:
            get_escrow_inst = Escrow.objects.select_related('user').filter(
                Q(user=request_user) & Q(escrow_id=reference) | Q(customer_reference=reference)).first()

        if get_escrow_inst:
            escrow_id = get_escrow_inst.escrow_id
            # cache_key = f"{escrow_id}_fetch_all_trans"
            # data = cache.get(cache_key)
            # if not data:

            if get_escrow_inst.transfer_type == "SEND_BANK_TRANSFER":
                data = ReturnEscrowTransactions.get_trans_with_escrow(get_escrow_inst)
            else:
                trans = Transaction.objects.filter(escrow_id=escrow_id).filter(~Q(transaction_type="SEND_LIBERTY_COMMISSION")).last()
                data = ServiceTransactionHistorySerializer(trans).data

            log_info("I am about to return^^^^^^^^^^")

            response = {
                "status": "success",
                "message": "transaction retrieved",
                "data": data
            }

            return Response(response, status=status.HTTP_200_OK)


        else:
            response = {
                "status": "error",
                "message": "transaction with ID does not exist"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)


class ResendOtherAccountDataAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]
    serializer_class = RecreateTransactionVerfObjectSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            trans_ids = serializer.validated_data["trans_ids"]

            no_trans_list = []
            created_verfs = []
            created_verfs_resp = []

            if len(trans_ids) > 0:
                for trans_id in trans_ids:
                    instance = Transaction.objects.filter(id=trans_id, is_other_account=True, callback_sent=False, status="SUCCESSFUL",
                                                          transaction_type="FUND_BANK_TRANSFER").last()

                    if instance:

                        created_verfs.append(instance.id)

                        payload = {
                            "user_id": instance.user.id,
                            "reference": instance.liberty_reference,
                            "amount": instance.amount,
                            "agent_phone": instance.user.phone_number,
                            "account_number": instance.is_other_account_number,
                            "response": instance.payload
                        }

                        send_callback = CallbackSystem.send_callback(
                            user=instance.is_other_account_owner,
                            transaction_type=instance.transaction_type,
                            payload=payload,
                            transaction_instance=instance
                        )

                        created_verfs_resp.append(send_callback)

                    else:

                        no_trans_list.append(trans_id)

                response = {
                    "message": "Successful",
                    "no_trans_list": no_trans_list,
                    "created_verfs": created_verfs,
                    "created_verfs_resp": created_verfs_resp
                }

            else:

                response = {
                    "status": "error",
                    "message": "No IDs attached"
                }

            return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class QRCyberPayWebhookAPIView(APIView):
    # permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    # serializer_class = RecreateTransactionVerfObjectSerializer

    def post(self, request):
        data_payload = request.data

        # Get IP ADDRESS
        address = request.META.get('HTTP_X_FORWARDED_FOR')
        if address:
            ip_addr = address.split(',')[-1].strip()
        else:
            ip_addr = request.META.get('REMOTE_ADDR')

        log_info(str(ip_addr))

        QRCyberWebhookData.objects.create(
            payload=json.dumps(data_payload)
        )

        # {
        #     "Code": "PY00",
        #     "Succeeded": true,
        #     "Data": {
        #         "Status": "Successful",
        #         "Message": "Transaction successful",
        #         "Reference": "LIB130423845587458747",
        #         "MerchantRef": "LP_QR-1681406039-f153bec9-dee0-4c78-9868-a45943cbebc2",
        #         "TransactionDate": "4/13/2023 6:15:39 PM +01:00",
        #         "CustomerId": "f7bc8169-6419-438a-a499-9b5eed613e62",
        #         "CustomerName": null,
        #         "ValuePin": null,
        #         "Amount": 10000
        #     }
        #     }

        resp = data_payload.get("Data")
        if resp is not None:
            unique_reference = resp["Reference"]
            first_amount = resp["Amount"]
            liberty_reference = resp["MerchantRef"]
            provider_status = resp["Status"]
            provider_message = resp["Message"]
            customer_id = resp["CustomerId"]

            transaction_type = "FUND_QRCODE"
            account_provider = "CYBERPAY"

            # Convert Amount
            amount = QRCode.remove_amount_from_kobo(amount=first_amount)

            # {'status': True, 'data': {'code': 'PY00', 'succeeded': True, 'data': {'status': 'Successful', 'message': 'Transaction is successful', 'reference': 'LIB130423845587458747', 'transactionDate': '4/13/2023 6:15:39 PM +01:00', 'amount': 10000, 'amountAfterCharge': 10000, 'charge': 3000, 'customerId': 'f7bc8169-6419-438a-a499-9b5eed613e62'}}}

            # Verify Transaction

            run_tsq = CyberPayClass.tsq_on_transaction(reference=unique_reference)

            if run_tsq.get("data").get("succeeded") == True \
                    and run_tsq.get("data").get("data").get("status") == "Successful" \
                    and run_tsq.get("data").get("data").get("message") == "Transaction is successful" \
                    and run_tsq.get("data").get("data").get("customerId") == customer_id \
                    and run_tsq.get("data").get("data").get("reference") == unique_reference \
                    and run_tsq.get("data").get("data").get("amount") == first_amount:

                provider_fee = QRCode.remove_amount_from_kobo(amount=run_tsq.get("data").get("data").get("charge"))

                # Check QRRaw Status
                get_qr = QRCode.objects.filter(liberty_reference=liberty_reference, provider_reference=unique_reference, is_paid=False).last()
                if get_qr:

                    user_instance = get_qr.user
                    narration = get_qr.narration if get_qr.narration else transaction_type

                    wallet_instance = user_instance.wallets.filter(wallet_type="COLLECTION").first()

                    check_for_transaction = Transaction.objects.filter(unique_reference=unique_reference)
                    if check_for_transaction:
                        transaction_instance = check_for_transaction.last()
                        escrow_id = transaction_instance.escrow_id
                        escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).last()

                        liberty_commission = escrow_instance.liberty_commission

                    else:

                        user_balance_before = wallet_instance.available_balance

                        user_balance_after = WalletSystem.get_balance_after(
                            user=user_instance,
                            balance_before=user_balance_before,
                            total_amount=amount,
                            is_credit=True
                        )

                        liberty_commission = 0.00

                        escrow_instance = Escrow.objects.create(
                            user=user_instance,
                            from_wallet_id=wallet_instance.wallet_id,
                            from_wallet_type=wallet_instance.wallet_type,
                            amount=amount,
                            balance_before=user_balance_before,
                            balance_after=user_balance_after,
                            total_amount_charged=amount,
                            narration=narration,
                            liberty_commission=liberty_commission,
                            is_beneficiary=False,
                            is_recurring=False,
                            transaction_mode=transaction_type,
                            transfer_type=transaction_type,
                        )

                        escrow_id = escrow_instance.escrow_id

                        transaction_instance = Transaction.objects.create(
                            user=user_instance,
                            amount=amount,
                            total_amount_received=amount,
                            liberty_commission=liberty_commission,
                            wallet_id=wallet_instance.wallet_id,
                            account_provider=account_provider,
                            wallet_type=wallet_instance.wallet_type,
                            transaction_type=transaction_type,
                            narration=narration,
                            status="PENDING",
                            balance_before=user_balance_before,
                            balance_after=user_balance_after,
                            beneficiary_account_name=user_instance.bvn_full_name,
                            escrow_id=escrow_id,
                            liberty_reference=liberty_reference,
                            unique_reference=unique_reference,
                            provider_fee=provider_fee,
                            ip_addr=ip_addr,
                            provider_status=provider_status,
                            payload=json.dumps(data_payload),
                            leg_one_verification_payload=json.dumps(run_tsq),
                        )

                    if transaction_instance.status not in ["SUCCESSFUL", "FAIILED"]:

                        get_qr.is_active = False
                        get_qr.is_paid = True
                        get_qr.transaction_instance = transaction_instance
                        get_qr.save()

                        # check_debit_credit
                        if DebitCreditRecordOnAccount.objects.filter(user=user_instance, entry="CREDIT",
                                                                     transaction_instance_id=transaction_instance.transaction_id).exists():
                            transaction_instance.status = "SUCCESSFUL"
                            transaction_instance.provider_status = provider_status
                            transaction_instance.save()

                            response = {
                                "status": "error",
                                "message": "Credit Exists"
                            }
                            return Response(response, status=status.HTTP_400_BAD_REQUEST)

                        else:

                            # fund wallet
                            fund_wallet = WalletSystem.fund_balance(
                                user=user_instance,
                                wallet=wallet_instance,
                                amount=amount,
                                trans_type=transaction_type,
                                transaction_instance_id=transaction_instance.transaction_id,
                                unique_reference=unique_reference
                            )

                            transaction_instance.status = "SUCCESSFUL"
                            transaction_instance.provider_status = provider_status
                            transaction_instance.save()

                            ##########################################################################################
                            # SEND OUT APP NOTIFICATION
                            receiver_not_token = user_instance.firebase_key
                            receiver_not_title = "Payment Received"
                            receiver_not_body = f"You have recieved a CREDIT of N{amount} from your QR CODE"
                            receiver_not_data = {"amount_sent": f"{amount}", "available_balance": f"{fund_wallet['balance_after']}"}

                            send_out_notification = cloud_messaging.send_broadcast(
                                token=receiver_not_token,
                                title=receiver_not_title,
                                body=receiver_not_body,
                                data=receiver_not_data
                            )

                            InAppTransactionNotification.create_in_app_transaction_notification(
                                user=user_instance,
                                title=receiver_not_title,
                                message_body=receiver_not_body
                            )

                            ##########################################################################################

                            # check KYC AND DECATIVATE SEND MONEY
                            check_kyc = user_instance.check_kyc.kyc_level
                            if check_kyc == 1:
                                balance_limit = (
                                    ConstantTable.get_constant_table_instance().kyc_one_receivable_balance_limit
                                )

                            elif check_kyc == 2:
                                balance_limit = (
                                    ConstantTable.get_constant_table_instance().kyc_two_receivable_balance_limit
                                )

                            elif check_kyc == 3:
                                balance_limit = (
                                    ConstantTable.get_constant_table_instance().kyc_three_receivable_balance_limit
                                )

                            else:
                                balance_limit = None

                            if amount is not None and amount > balance_limit:
                                user_instance.send_money_status = False
                                user_instance.save()

                            # DEBIT ALERT MANAGER
                            manage_alert = WalletSystem.transaction_alert_notfication_manager(
                                user=user_instance,
                                amount=amount,
                                cr_dr="CR",
                                narration=narration,
                                from_wallet_type=wallet_instance.wallet_type,
                                transaction_instance_id=transaction_instance.transaction_id,
                                liberty_commission=liberty_commission
                            )

                            response = {"message": "new transaction created"}
                            return Response(response, status=status.HTTP_200_OK)



                    else:
                        response = {
                            "status": "error",
                            "message": "Transaction is Successful or Failed"
                        }
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:

                    response = {
                        "status": "error",
                        "message": "QR is paid"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                response = {
                    "status": "error",
                    "message": "Could not verify"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
        else:
            response = {
                "status": "error",
                "message": "Bad Data"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class GetCreditCardsAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = GetCreditCardSerializer

    def get(self, request):
        request_user = request.user

        get_card = CreditCardDetail.objects.filter(user=request_user)
        if get_card:
            serializer = self.serializer_class(get_card, many=True)
            response = {
                "status": "success",
                "message": "Cards Retrieved",
                "data": serializer.data
            }
        else:
            response = {
                "status": "failed",
                "message": "No Cards Retrieved",
                "data": []
            }

        return Response(response, status=status.HTTP_200_OK)


class ViewDebitCreditRecordAPIView(generics.ListAPIView):
    permission_classes = [OtherServiceOtherPermissions]
    serializer_class = ViewDebitCreditRecordSerializer
    pagination_class = CustomPaginationV2

    def get_queryset(self):
        return DebitCreditRecordOnAccount.objects.exclude(user__email=settings.FLOAT_USER_EMAIL).order_by("-date_created")


class GetAllBankListAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]

    def get(self, request):
        response = VFDBank.get_all_bank_list()

        return Response(response, status=status.HTTP_200_OK)


class AutoSweepAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasKYC, SendMoneyRegulatorPermission, HasTransactionPin, CanSendMoney,
                          CheckWalletAvailable, CheckAccountAvailable]
    get_serializer_class = GetAutoSweepSerializer
    post_serializer_class = PostAutoSweepSerializer

    def post(self, request):
        request_user = request.user

        serializer = self.post_serializer_class(data=request.data)

        if serializer.is_valid():
            transaction_pin = serializer.validated_data["transaction_pin"]
            bank_name = serializer.validated_data["bank_name"]
            bank_code = serializer.validated_data["bank_code"]
            account_number = serializer.validated_data["account_number"]
            account_name = serializer.validated_data["account_name"]
            min_balance = serializer.validated_data["min_balance"]
            max_amount = serializer.validated_data["max_amount"]
            sweep_interval = serializer.validated_data["sweep_interval"]
            start_date = serializer.validated_data["start_date"]
            sweep_hour = serializer.validated_data["sweep_hour"]
            recur_type = serializer.validated_data["recur_type"]
            narration = serializer.validated_data.get("narration")

            if not transaction_pin.isnumeric():
                response = {
                    "error": "14",
                    "message": "You must supply an integer for transaction pin"
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            chcek_pin = User.check_sender_transaction_pin(
                user=request_user, pincode=transaction_pin
            )
            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(request_user)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            else:
                User.reset_transaction_pin_retries(request_user)

                sweep_id = request.query_params.get('sweep_id')
                if sweep_id not in [None, "", " "]:
                    try:
                        get_sweep = AutoSweepRecurringChargeTable.objects.get(user=request_user, id=sweep_id)

                        get_sweep.recur_type = recur_type
                        get_sweep.bank_name = bank_name
                        get_sweep.bank_code = bank_code
                        get_sweep.account_number = account_number
                        get_sweep.account_name = account_name
                        get_sweep.min_balance = min_balance
                        get_sweep.max_amount = max_amount
                        get_sweep.sweep_interval = sweep_interval
                        get_sweep.start_date = start_date
                        get_sweep.sweep_hour = sweep_hour
                        get_sweep.narration = narration
                        get_sweep.first_trans_key = transaction_pin
                        get_sweep.save()

                        response = {
                            "status": "success",
                            "message": "Auto Sweep Updated"
                        }
                        return Response(response, status=status.HTTP_200_OK)

                    except:
                        response = {
                            "error": "959",
                            "message": "sweep id does not exist"
                        }
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:

                    if AutoSweepRecurringChargeTable.objects.filter(user=request_user, recur_type=recur_type).exists():
                        response = {
                            "error": "848",
                            "message": "Sweep Already Exists",
                        }

                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                    else:
                        AutoSweepRecurringChargeTable.objects.create(
                            user=request_user,
                            recur_type=recur_type,
                            bank_name=bank_name,
                            bank_code=bank_code,
                            account_number=account_number,
                            account_name=account_name,
                            min_balance=min_balance,
                            max_amount=max_amount,
                            sweep_interval=sweep_interval,
                            start_date=start_date,
                            sweep_hour=sweep_hour,
                            narration=narration,
                            first_trans_key=transaction_pin
                        )

                        response = {
                            "status": "success",
                            "message": "Auto Sweep Created"
                        }
                        return Response(response, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request):
        request_user = request.user

        sweep_id = request.query_params.get('sweep_id')
        if sweep_id not in [None, "", " "]:
            try:
                get_sweep = AutoSweepRecurringChargeTable.objects.get(user=request_user, id=sweep_id)
                serializer = self.get_serializer_class(get_sweep)

                response = {
                    "status": "success",
                    "data": serializer.data
                }
                return Response(response, status=status.HTTP_200_OK)
            except:
                response = {
                    "error": "959",
                    "message": "sweep id does not exist"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        else:
            get_all_sweep = AutoSweepRecurringChargeTable.objects.filter(user=request_user)
            if get_all_sweep:

                serializer = self.get_serializer_class(get_all_sweep, many=True)

                response = {
                    "status": "success",
                    "data": serializer.data
                }
                return Response(response, status=status.HTTP_200_OK)
            else:
                response = {
                    "error": "999",
                    "message": "No auto sweep exist"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

    #     queryset = DebitCreditRecordOnAccount.objects.exclude(user__email=settings.FLOAT_USER_EMAIL)
    #     total_length = queryset.count()

    #     if queryset:

    #         data = Paginator.paginate(request=request, queryset=queryset, size=200)

    #         serializer = self.serializer_class(data, many=True)

    #         response = {
    #             "status": True,
    #             "total_length": total_length,
    #             "list_count": len(serializer.data),
    #             "data": serializer.data
    #         }
    #     else:
    #         next_page = self.get_next_link()
    #         prev_page = self.get_previous_link()
    #         response = {
    #             "status": True,
    #             "total_length": total_length,
    #             "list_count": len(serializer.data),
    #             "data": []
    #         }

    #     return Response(response, status=status.HTTP_200_OK)


class LottoRemitanceTransactionHistoryAPIView(generics.ListAPIView):
    permission_classes = [CheckDynamicAuthentication]
    serializer_class = TransactionHistorySerializer
    search_fields = ['narration']
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = TransactionDateFilter

    def get_queryset(self):

        service_user_check = OtherServiceDetail.objects.filter(service_name="LOTTO_PLAY").last()
        if service_user_check:
            service_user = service_user_check.user

            return Transaction.objects.filter(user=service_user, status="SUCCESSFUL") \
                .filter(Q(transaction_type__in=["FUND_LOTTO_WALLET", "FUND_COLLECTION_ACCOUNT"]) | Q(transaction_type="LOTTO_PLAY_CR",
                                                                                                     narration="REMITTANCE_DEDUCTION")) \
                .order_by("-date_created")

        else:
            return Transaction.objects.none()


class BankBeneficiarySuccessRateAPIView(generics.ListAPIView):
    permission_classes = [CheckDynamicAuthentication]
    serializer_class = EscrowHistorySerializer
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = EscrowDateFilter

    def get_queryset(self):
        return Escrow.objects.filter(transfer_type="SEND_BANK_TRANSFER").order_by("-date_created")


class LibertyCardOperationsTransactionHistoryAPIView(generics.ListAPIView):
    permission_classes = [CheckDynamicAuthentication]
    serializer_class = LibertyCardOperationsTransactionHistorySerializer
    search_fields = ['narration']
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = TransactionDateFilter

    def get_queryset(self):

        return Transaction.objects.filter(transaction_type__in=["CARD_PURCHASE"], status="SUCCESSFUL").order_by("-date_created")

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        # Calculate the total number of card requests
        card_requests_per_day = CustomerCardDetail.objects.all() \
            .annotate(day=Count('date_created', distinct=True)) \
            .values('day')

        total_card_request = sum([day['day'] for day in card_requests_per_day])
        earliest_date = CustomerCardDetail.objects.all().order_by('date_created').values_list('date_created', flat=True).first()
        latest_date = timezone.now()
        num_days = (latest_date - earliest_date).days
        average_daily_card_request = total_card_request / num_days

        all_time_base_trans = Transaction.objects.filter(transaction_type__in=["CARD_PURCHASE"], status="SUCCESSFUL")
        all_time_trans_count = all_time_base_trans.count()
        all_time_trans_amount = all_time_base_trans.aggregate(Sum("amount")).get("amount__sum")
        all_time_trans_comm_amount = all_time_base_trans.aggregate(Sum("liberty_commission")).get("liberty_commission__sum")
        all_time_trans_channel = all_time_base_trans.values('transaction_sub_type').annotate(count=Count('transaction_sub_type'))

        # Calculate the total number of card requests by date filter
        date_filter = self.filterset_class(data=request.GET)
        date_filter.is_valid()
        earliest_date_date_range = date_filter.data.get('timestamp_gte')
        latest_date_date_range = date_filter.data.get('timestamp_lte')

        if earliest_date_date_range is not None:
            try:
                earliest_date_date_range = datetime.strptime(earliest_date_date_range, '%Y-%m-%d')
            except ValueError:
                error_resp = {
                    "error": True,
                    "message": "Invalid timestamp_gte format"
                }
                return Response(error_resp, status=status.HTTP_400_BAD_REQUEST)

        if latest_date_date_range is not None:
            try:
                latest_date_date_range = datetime.strptime(latest_date_date_range, '%Y-%m-%d')
            except ValueError:
                error_resp = {
                    "error": True,
                    "message": "Invalid timestamp_lte format"
                }
                return Response(error_resp, status=status.HTTP_400_BAD_REQUEST)

        earliest_date_date_range = datetime.strptime(date_filter.data.get('timestamp_gte'), '%Y-%m-%d').date() if date_filter.data.get(
            'timestamp_gte') is not None else earliest_date
        latest_date_date_range = datetime.strptime(date_filter.data.get('timestamp_lte'), '%Y-%m-%d').date() if date_filter.data.get(
            'timestamp_lte') is not None else latest_date

        card_requests_per_day_date_range = CustomerCardDetail.objects.filter(date_created__range=(earliest_date_date_range, latest_date_date_range)) \
            .annotate(day=Count('date_created', distinct=True)) \
            .values('day')

        total_card_request_date_range = sum([day['day'] for day in card_requests_per_day_date_range])
        num_days_date_range = (latest_date_date_range - earliest_date_date_range).days + 1
        average_daily_card_request_date_range = total_card_request_date_range / num_days_date_range if total_card_request_date_range > 0 and num_days_date_range > 0 else 0

        date_range_trans_count = queryset.count()
        date_range_trans_amount = queryset.aggregate(Sum("amount")).get("amount__sum")
        date_range_trans_comm_amount = queryset.aggregate(Sum("liberty_commission")).get("liberty_commission__sum")
        date_range_trans_channel = all_time_base_trans.filter(date_created__range=(earliest_date_date_range, latest_date_date_range)).values(
            'transaction_sub_type').annotate(count=Count('transaction_sub_type'))

        response_data = {
            "all_time_total_card_request": total_card_request,
            "all_time_average_daily_card_request": average_daily_card_request,
            "date_range_total_card_request": total_card_request_date_range,
            "date_range_average_daily_card_request": average_daily_card_request_date_range,
            "all_time_trans_count": all_time_trans_count,
            "all_time_trans_amount": all_time_trans_amount,
            "all_time_trans_comm_amount": all_time_trans_comm_amount,
            "date_range_trans_count": date_range_trans_count,
            "date_range_trans_amount": date_range_trans_amount,
            "date_range_trans_comm_amount": date_range_trans_comm_amount,
            "all_time_trans_channel": all_time_trans_channel,
            "date_range_trans_channel": date_range_trans_channel,
        }

        serializer = self.get_serializer(queryset, many=True)
        response_data["data"] = serializer.data
        return Response(response_data)


class BeginWithdrawalByUSSDTransAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC]
    serializer_class = BeginFundByUSSDSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        serializer.is_valid(raise_exception=True)
        trans_type = serializer.validated_data.get("trans_type")
        bank_code = serializer.validated_data.get("bank_code")
        amount = serializer.validated_data.get("amount")

        # if not AllBankList.objects.filter(bank_code=bank_code).exists():
        bank_inst = AllBankList.objects.filter(ussd_short_code=bank_code).last()
        if not bank_inst:
            response = {
                "error": "527",
                "message": "Invalid Bank"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # Get IP ADDRESS
        ip_addr = get_ip_address(request)
        log_info(bank_inst.name, "bank name")
        log_info(bank_inst.ussd_short_code, "bank code")
        start_trans = StartFundByUSSDTran.create_fund_by_ussd_trans(
            user=request.user,
            amount_started=amount,
            bank_code=bank_code,
            bank_name=bank_inst.name,
            ip_addr=ip_addr,
            trans_type=trans_type,
            biller="CORALPAY",
        )

        initiate_external_trans = CoralWithdrawByUSSDClass.invoke_reference(
            sub_merchant_name=start_trans.first_name,
            bank_code=start_trans.bank_code,
            amount=start_trans.amount_started,
            reference=start_trans.liberty_reference,
        )
        log_info(initiate_external_trans, "coralpay resp")

        if initiate_external_trans.get("ResponseHeader").get("ResponseCode") == "00":
            start_trans.initiate_data = initiate_external_trans
            start_trans.ussd_code = initiate_external_trans.get("ResponseDetails").get("UssdString")
            response = {
                "message": "USSD Code Successfully Generated",
                "ussd_code": start_trans.ussd_code,
                "amount": start_trans.amount_started,
                "bank_name": start_trans.bank_name,
                "reference": start_trans.liberty_reference
            }
            transaction_status = "SUCCESSFUL"

        else:
            response = {
                "error": "875",
                "message": initiate_external_trans,
                "reference": start_trans.liberty_reference
            }
            transaction_status = "FAILED"

        start_trans.internal_response = response
        start_trans.external_response = json.dumps(initiate_external_trans)
        start_trans.status = transaction_status
        start_trans.save()

        return Response(response, status=status.HTTP_200_OK)


class WithdrawalByUSSDStatusQueryAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC]

    def post(self, request, format=None):
        serializer = WithdrawalByUSSDStatusQuerySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        amount = serializer.validated_data.get("amount")
        reference = serializer.validated_data.get("transaction_id")
        response = CoralWithdrawByUSSDClass.verify_transaction(
            reference=reference,
            amount=amount
        )
        return Response(response, status=status.HTTP_200_OK)


# class WithdrawalByUSSDTransactionNotificationAPIView(APIView):
#     permission_classes = [BasicAuthWithUsernameAsUsernameFieldAuthentication]

#     def post(self, request):
#         response = request.data
#         WithdrawalByUSSDNotificationData.objects.create(payload=json.dumps(response))
#         return Response(
#             {"status": "true", "message": "response received"},
#             status=status.HTTP_200_OK,
#         )

class WithdrawalByUSSDTransactionNotificationAPIView(APIView):
    permission_classes = [BasicAuthWithUsernameAsUsernameFieldAuthentication]

    def post(self, request):
        resp = request.data

        # Get IP ADDRESS
        address = request.META.get('HTTP_X_FORWARDED_FOR')
        if address:
            ip_addr = address.split(',')[-1].strip()
        else:
            ip_addr = request.META.get('REMOTE_ADDR')

        WithdrawalByUSSDNotificationData.objects.create(payload=json.dumps(resp))

        log_info(str(ip_addr))

        if resp is not None:
            unique_reference = resp["TransactionId"]
            first_amount = resp["Amount"]
            liberty_reference = resp["TraceId"]
            provider_status = resp["ResponseCode"]
            provider_message = resp["ResponseMessage"]
            customer_id = resp["CustomerId"]
            sender = resp["CustomerMobile"]

            transaction_type = "USSD_WITHDRAW"
            account_provider = "CORALPAY"

            # Convert Amount
            amount = first_amount

            # {'ResponseCode': '00', 'ResponseMessage': 'Success', 'Reference': '2053', 'Amount': 20.0, 'TerminalId': '1057LBA1', 'MerchantId': '1057LBA10000001', 'BankCode': 'None', 'BankName': 'Sink Node', 'CustomerMobile': '0809***7503', 'SubMerchantName': 'Agent Roosevelt 007', 'TransactionId': '22071367402070502053', 'TraceId': '***************', 'CustomerId': '**************', 'Signature': '1cb6c5381aaef93eb3dfde981b42c171f600fc44ea5ac9f9aac9af420570255e'}

            get_ussd_trans = StartFundByUSSDTran.objects.filter(liberty_reference=liberty_reference).last()
            if get_ussd_trans and amount == get_ussd_trans.amount_started and not get_ussd_trans.trans_complete:

                # Verify Transaction
                run_tsq = CoralWithdrawByUSSDClass.verify_transaction(reference=unique_reference, amount=amount)
                log_info(run_tsq, "run_tsq")
                log_info(resp, "response")

                get_ussd_trans.internal_response = json.dumps(run_tsq)

                if run_tsq.get("ResponseCode") == "00" \
                        and run_tsq.get("ResponseMessage") == "Success" \
                        and run_tsq.get("Amount") == amount \
                        and run_tsq.get("CustomerId") == customer_id \
                        and run_tsq.get("TraceId") == liberty_reference \
                        and run_tsq.get("TransactionId") == unique_reference:

                    provider_fee = 0

                    # Check CoralpayUSSDWithdraw Status

                    user_instance = get_ussd_trans.user
                    narration = get_ussd_trans.narration if get_ussd_trans.narration else transaction_type
                    # if get_ussd_trans.trans_complete is False:
                    get_ussd_trans.trans_complete = True
                    get_ussd_trans.resolve_data = run_tsq
                    get_ussd_trans.amount_resolved = run_tsq.get("Amount")
                    # get_ussd_trans.check_ip_addr = ip_addr
                    # get_ussd_trans.returned_checked_from = request_from
                    get_ussd_trans.save()

                    wallet_instance = user_instance.wallets.filter(wallet_type="COLLECTION").first()

                    check_for_transaction = Transaction.objects.filter(unique_reference=unique_reference)
                    if check_for_transaction:
                        transaction_instance = check_for_transaction.last()
                        escrow_id = transaction_instance.escrow_id
                        escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).last()

                        liberty_commission = escrow_instance.liberty_commission

                    else:

                        user_balance_before = wallet_instance.available_balance

                        user_balance_after = WalletSystem.get_balance_after(
                            user=user_instance,
                            balance_before=user_balance_before,
                            total_amount=amount,
                            is_credit=True
                        )

                        liberty_commission = 0.00

                        escrow_instance = Escrow.objects.create(
                            user=user_instance,
                            from_wallet_id=wallet_instance.wallet_id,
                            from_wallet_type=wallet_instance.wallet_type,
                            amount=amount,
                            balance_before=user_balance_before,
                            balance_after=user_balance_after,
                            total_amount_charged=amount,
                            narration=narration,
                            liberty_commission=liberty_commission,
                            is_beneficiary=False,
                            is_recurring=False,
                            transaction_mode=transaction_type,
                            transfer_type=transaction_type,
                        )

                        escrow_id = escrow_instance.escrow_id

                        transaction_instance = Transaction.objects.create(
                            user=user_instance,
                            amount=amount,
                            total_amount_received=amount,
                            liberty_commission=liberty_commission,
                            wallet_id=wallet_instance.wallet_id,
                            account_provider=account_provider,
                            wallet_type=wallet_instance.wallet_type,
                            transaction_type=transaction_type,
                            narration=narration,
                            status="PENDING",
                            balance_before=user_balance_before,
                            balance_after=user_balance_after,
                            beneficiary_account_name=user_instance.bvn_full_name,
                            escrow_id=escrow_id,
                            liberty_reference=liberty_reference,
                            unique_reference=unique_reference,
                            provider_fee=provider_fee,
                            ip_addr=ip_addr,
                            provider_status=provider_status,
                            payload=json.dumps(resp),
                            leg_one_verification_payload=json.dumps(run_tsq),
                        )

                    if transaction_instance.status not in ["SUCCESSFUL", "FAIILED"]:

                        # get_ussd_trans.is_active= False
                        # get_ussd_trans.is_paid = True
                        # get_ussd_trans.transaction_instance = transaction_instance
                        # get_ussd_trans.save()

                        # check_debit_credit
                        if DebitCreditRecordOnAccount.objects.filter(user=user_instance, entry="CREDIT",
                                                                     transaction_instance_id=transaction_instance.transaction_id).exists():
                            transaction_instance.status = "SUCCESSFUL"
                            transaction_instance.provider_status = provider_status
                            transaction_instance.save()

                            response = {
                                "status": "error",
                                "message": "Credit Exists"
                            }
                            # return Response(response, status=status.HTTP_400_BAD_REQUEST)

                        else:

                            # fund wallet
                            fund_wallet = WalletSystem.fund_balance(
                                user=user_instance,
                                wallet=wallet_instance,
                                amount=amount,
                                trans_type=transaction_type,
                                transaction_instance_id=transaction_instance.transaction_id,
                                unique_reference=unique_reference
                            )

                            transaction_instance.status = "SUCCESSFUL"
                            transaction_instance.provider_status = provider_status
                            transaction_instance.save()

                            ##########################################################################################
                            # SEND OUT APP NOTIFICATION
                            receiver_not_token = user_instance.firebase_key
                            receiver_not_title = "Payment Received"
                            receiver_not_body = f"You have recieved a CREDIT of N{amount} from your {sender}"
                            receiver_not_data = {"amount_sent": f"{amount}", "available_balance": f"{fund_wallet['balance_after']}"}

                            send_out_notification = cloud_messaging.send_broadcast(
                                token=receiver_not_token,
                                title=receiver_not_title,
                                body=receiver_not_body,
                                data=receiver_not_data
                            )

                            InAppTransactionNotification.create_in_app_transaction_notification(
                                user=user_instance,
                                title=receiver_not_title,
                                message_body=receiver_not_body
                            )

                            ##########################################################################################

                            # check KYC AND DECATIVATE SEND MONEY
                            check_kyc = user_instance.check_kyc.kyc_level
                            if check_kyc == 1:
                                balance_limit = (
                                    ConstantTable.get_constant_table_instance().kyc_one_receivable_balance_limit
                                )

                            elif check_kyc == 2:
                                balance_limit = (
                                    ConstantTable.get_constant_table_instance().kyc_two_receivable_balance_limit
                                )

                            elif check_kyc == 3:
                                balance_limit = (
                                    ConstantTable.get_constant_table_instance().kyc_three_receivable_balance_limit
                                )

                            else:
                                balance_limit = None

                            if amount is not None and amount > balance_limit:
                                user_instance.send_money_status = False
                                user_instance.save()

                            # DEBIT ALERT MANAGER
                            manage_alert = WalletSystem.transaction_alert_notfication_manager(
                                user=user_instance,
                                amount=amount,
                                cr_dr="CR",
                                narration=narration,
                                from_wallet_type=wallet_instance.wallet_type,
                                transaction_instance_id=transaction_instance.transaction_id,
                                liberty_commission=liberty_commission
                            )

                            response = {"message": "new transaction created"}
                            # return Response(response, status=status.HTTP_200_OK)



                    else:
                        response = {
                            "status": "error",
                            "message": "Transaction is Successful or Failed"
                        }
                        # return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:

                    response = {
                        "status": "error",
                        "message": "USSD Withdraw is paid"
                    }
                    # return Response(response, status=status.HTTP_400_BAD_REQUEST)

                get_ussd_trans.external_response = response
                get_ussd_trans.save()

            else:
                response = {
                    "status": "error",
                    "message": "Could not verify"
                }
                # return Response(response, status=status.HTTP_400_BAD_REQUEST)


        else:
            response = {
                "status": "error",
                "message": "Bad Data"
            }

        return Response(
            {"status": "true", "message": "response received"},
            status=status.HTTP_200_OK,
        )


class SetCoralPasswordAPIView(APIView):
    permission_classes = [CheckDynamicAuthentication]

    def post(self, request):
        data = request.data

        password = data.get("password")
        get_user = User.objects.filter(username="liberty_coral").first()

        if get_user:
            get_user.set_password(raw_password=password)
            get_user.save()

        return Response(
            {"status": "true", "message": "response received"},
            status=status.HTTP_200_OK,
        )


class CoralPayUSSDLoginAPIView(APIView):
    def post(self, request, format=None):
        serializer = CoralUSSDLoginSerializer(data=request.data)
        if serializer.is_valid():
            Username = serializer.validated_data.get("Username")
            Password = serializer.validated_data.get("Password")
            data = CoralWithdrawByUSSDClass.coral_pay_login(
                Username=Username, Password=Password
            )
            # data = {"username":Username, "password":Password}
            return Response(data=data, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CreateLibertyReference(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]
    serializer_class = CreateLibertyReferenceSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            prefix = serializer.validated_data["prefix"]

            create_ref = Transaction.create_liberty_reference(suffix=prefix)

            response = {
                "status": "success",
                "reference": create_ref
            }

            return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class HandleOOBTransfers(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = HandleOOBSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():

            account_provider = serializer.validated_data["account_provider"]
            from_acct_no = serializer.validated_data["from_acct_no"]
            to_acct_no = serializer.validated_data["to_acct_no"]
            amount = serializer.validated_data["amount"]
            user_email = serializer.validated_data["user_email"]
            pin = serializer.validated_data["pin"]
            escrow_id = serializer.validated_data.get("escrow_id")
            liberty_reference = serializer.validated_data.get("liberty_reference")
            old_liberty_reference = serializer.validated_data.get("old_liberty_reference")

            user_instance = request.user

            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=user_instance, pincode=pin
            )

            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(user_instance)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)


            else:
                User.reset_transaction_pin_retries(user_instance)

                try:
                    user = User.objects.get(email=user_email)
                except Exception as err:
                    response = {
                        "error": "545",
                        "message": "User with email does not exist",
                    }

                    return Response(response, status=status.HTTP_200_OK)

                # Get Receiver Account
                get_ben_account_name = AccountSystem.objects.filter(user=user, account_number=to_acct_no).first()
                if not get_ben_account_name:
                    response = {
                        "error": "545",
                        "message": "Account Does Not Exist For User"
                    }

                    return Response(response, status=status.HTTP_200_OK)

                get_float_account = AccountSystem.get_float_account(from_wallet_type="FLOAT", from_provider_type=account_provider)
                get_comm_account = AccountSystem.get_float_account(from_wallet_type="COMMISSIONS", from_provider_type=account_provider)

                transaction_inst = Transaction.objects.filter(liberty_reference=liberty_reference).last()
                old_transaction_inst = Transaction.objects.filter(liberty_reference=old_liberty_reference).last() if old_liberty_reference else None

                if not liberty_reference and old_transaction_inst:
                    response = {
                        "error": "546",
                        "message": "You must provide new_trans_ref if providing old_trans_ref",
                    }

                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                if not transaction_inst and not old_transaction_inst:
                    # if (old_liberty_reference and not old_transaction_inst) or (not old_liberty_reference and not Transaction.objects.filter(liberty_reference=liberty_reference).exists()):
                    # Get Sender Account
                    get_sender_account = AccountSystem.objects.filter(account_number=from_acct_no).first()

                    if get_float_account.account_number == to_acct_no:
                        trans_user = get_sender_account.user
                        transaction_type = "BANK_OOB_IN"
                        transaction_leg = "INTERNAL"

                    elif get_float_account.account_number == from_acct_no:
                        trans_user = get_ben_account_name.user
                        transaction_type = "BANK_OOB_OUT"
                        transaction_leg = "INTERNAL"

                    elif get_comm_account.account_number == to_acct_no:
                        trans_user = get_sender_account.user
                        transaction_type = "BANK_OOB_COMM"
                        transaction_leg = "INTERNAL"

                    elif get_comm_account.account_number == from_acct_no:
                        trans_user = get_ben_account_name.user
                        transaction_type = "BANK_OOB_COMM"
                        transaction_leg = "INTERNAL"

                    narration = transaction_type

                    if not escrow_id:
                        escrow_instance = Escrow.objects.create(
                            user=trans_user,
                            transfer_type=transaction_type,
                            narration=narration,
                            liberty_reference=liberty_reference,
                            pos_charge=0,
                            pos_charge_type="BANK"
                        )

                        escrow_id = escrow_instance.escrow_id

                    transaction_inst = Transaction.objects.create(
                        user=user,
                        account_provider=account_provider,
                        transaction_type=transaction_type,
                        amount=amount,
                        provider_fee=0,
                        liberty_reference=liberty_reference,
                        liberty_commission=0,
                        extra_fee=0,
                        user_trans_band=user.trans_band,
                        escrow_id=escrow_id,
                        beneficiary_account_name=get_ben_account_name.account_name,
                        beneficiary_nuban=get_ben_account_name.account_number,
                        beneficiary_bank_code=get_ben_account_name.bank_code,
                        source_nuban=get_sender_account.account_number,
                        source_account_name=get_sender_account.account_name,
                        narration=narration,
                        status="IN_PROGRESS",
                        transaction_leg=transaction_leg,
                    )

                if old_transaction_inst and liberty_reference and not transaction_inst:
                    transaction_inst = old_transaction_inst

                if old_liberty_reference:
                    get_trans = TransferVerificationObject.objects.filter(liberty_reference=old_liberty_reference).last()
                    if get_trans:
                        DeletedReference.objects.create(
                            transaction_instance=get_trans.transaction_instance,
                            liberty_reference=get_trans.transaction_instance.liberty_reference,
                            trans_type=get_trans.transaction_instance.transaction_type,
                        )

                        get_trans.liberty_reference = liberty_reference
                        get_trans.transaction_instance.liberty_reference = liberty_reference
                        get_trans.is_verified = False
                        get_trans.save()
                        get_trans.transaction_instance.save()

                handle_rev = AccountSystem.handle_out_of_books(
                    from_account=from_acct_no,
                    to_account=to_acct_no,
                    amount=amount,
                    escrow_id=escrow_id,
                    user_bvn_number=user.bvn_number,
                    transaction_instance=transaction_inst,
                    liberty_reference=liberty_reference
                )

                log_info(str(handle_rev))

                response = {
                    "status": "success",
                    "payload": handle_rev
                }

                return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ParallexDumpDataAPIView(APIView):

    def post(self, request):
        data = request.data

        trans_data = ParallexDumpData.objects.create(data_dump=json.dumps(data))

        if not isinstance(data, dict):
            try:
                data = eval(data)
            except:
                data = json.loads(data)

        trans_id = data["Transaction_reference"]
        trans_data.trans_ref = trans_id
        trans_data.save()

        response = {
            "status": True,
            "message": "received"
        }

        return Response(response, status=status.HTTP_200_OK)


class BulkResetPendingTransEscrow(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    # serializer_class = TransactionManualResolveSerializer
    serializer_class = RecreateTransactionVerfObjectSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():

            if settings.ENVIRONMENT == "development":
                is_test_trans = True
            elif settings.ENVIRONMENT == "production":
                is_test_trans = False

            trans_ids = serializer.validated_data["trans_ids"]

            results = []

            for data in trans_ids:

                transaction_verf = TransferVerificationObject.objects.filter(id=data).last()

                if transaction_verf:
                    if transaction_verf.transaction_ver_status in ["NOT_INITIATED", "PENDING"]:

                        escrow_id = transaction_verf.escrow_id

                        escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).last()
                        transaction_verf.is_finished_verification

                        if escrow_instance:
                            if transaction_verf.transaction_leg == "INTERNAL":
                                escrow_instance.internal_escrow = True
                                transaction_verf.first_leg_done = False

                            if transaction_verf.transaction_leg == "EXTERNAL":
                                escrow_instance.internal_escrow = True
                                transaction_verf.second_leg_done = False

                            if transaction_verf.transaction_leg == "EXTERNAL":
                                escrow_instance.internal_escrow = True
                                transaction_verf.commissions_leg_done = False

                            escrow_instance.save()
                        transaction_verf.save()

                        response = {
                            "message": "changed"
                        }

                        # return Response(response, status=status.HTTP_404_NOT_FOUND)
                    else:
                        response = {
                            "message": "Transaction has been initiated or is not pending"
                        }

                        # return Response(response, status=status.HTTP_406_NOT_ACCEPTABLE)

                    results.append({"id": data, "liberty_reference": transaction_verf.liberty_reference, "response": response})

                else:
                    response = {
                        "error": "323",
                        "message": "Transaction does not exist"
                    }

            return Response(results, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GeneratePromoCodeWinnersAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]

    def get(self, request):
        session_id = request.query_params.get("session_id", None)
        winner_nos = request.query_params.get("winner_nos", None)

        try:
            winner_nos = int(winner_nos)
        except:
            return Response({
                "error": True,
                "message": "Invalid Number Of Winners"
            },
                status=status.HTTP_400_BAD_REQUEST)

        if not session_id:
            return Response({
                "error": True,
                "message": "No session id entered"
            },
                status=status.HTTP_400_BAD_REQUEST)

        import random
        from django.db.models import Subquery

        promo_code_qs = PromoCodeData.objects.filter(session_id=session_id)
        if not promo_code_qs:
            return Response({
                "error": True,
                "message": "No Promo Code Data Exist"
            },
                status=status.HTTP_400_BAD_REQUEST)

        if PromoCodeDecider.objects.get(session_id=session_id).end_time >= timezone.now():
            return Response({
                "error": True,
                "message": "Promo Code Session is still running"
            },
                status=status.HTTP_400_BAD_REQUEST)

        winning_tids = promo_code_qs.first().winner_codes
        if not winning_tids:

            log_info("came heree")
            distince_promo_codes_qs = promo_code_qs.values_list("user", flat=True).distinct()

            # Annotate the queryset to get the count of unique users
            # unique_users_qs = promo_code_qs.values('user').annotate(unique_user_count=Count('user'))

            # # Filter the queryset to include only unique users
            # unique_users_qs = unique_users_qs.filter(unique_user_count=1)

            # Get a queryset of PromoCodeData instances associated with these unique users

            # random_promo_codes_qs = PromoCodeData.objects.filter(
            #     id__in=Subquery(
            #         promo_code_qs.filter(user__in=unique_users_qs.values('user'))
            #                     .order_by('user', '?')
            #                     .values('id')[:unique_users_qs.count()]
            #     )
            # )

            if distince_promo_codes_qs.count() < winner_nos:
                return Response({
                    "error": True,
                    "message": f"Expected winners are less than number of promo codes of unique users. Number of unique users are {distince_promo_codes_qs.count()}"
                },
                    status=status.HTTP_400_BAD_REQUEST)

            all_instance_ids = list(distince_promo_codes_qs.values_list('id', flat=True))

            random_instance_ids = random.sample(all_instance_ids, winner_nos)
            winner_instances = distince_promo_codes_qs.filter(id__in=random_instance_ids)

            winning_tids = list(winner_instances.values_list("transaction_id", flat=True))

            promo_code_qs.update(winner_codes=winning_tids)

        user_data = []

        for trans_id in winning_tids:
            trans = Transaction.objects.get(transaction_id=uuid.UUID(trans_id))

            user_data.append(
                {
                    "phone_number": trans.user.phone_number,
                    "first_name": trans.user.bvn_first_name,
                    "last_name": trans.user.bvn_last_name,
                    "narration": "promo code winning",
                    "trans_id": trans.transaction_id,
                    "trans_type": trans.transaction_type,
                    "amount": trans.amount,
                }
            )

        response = {
            "message": user_data
        }

        return Response(response, status=status.HTTP_200_OK)


class GetBVNConsentForCreateWalletAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC]

    def get(self, request):

        user: User = request.user

        bvn_number = request.query_params.get("bvn_number")
        if not bvn_number:
            return Response({
                "status": "error",
                "message": "No BVN Number Entered"
            },
                status=status.HTTP_400_BAD_REQUEST)

        # if OtherServiceDetail.

        if user.bvn_number != bvn_number:
            return Response({
                "status": "error",
                "message": "BVN does not match user's account Number Entered"
            },
                status=status.HTTP_400_BAD_REQUEST)

        get_consent, url = AccountSystem.handle_bvn_consent(bvn_number=bvn_number)

        if get_consent:
            return Response({
                "status": "success",
                "message": "BVN Consent Already Given"
            },
                status=status.HTTP_200_OK)

        elif get_consent == False:
            return Response({
                "status": "success",
                "message": "BVN Consent Not Given",
                "url": url
            },
                status=status.HTTP_201_CREATED)

        else:
            return Response({
                "status": "error",
                "message": "An error occured"
            },
                status=status.HTTP_400_BAD_REQUEST)


class CreateAjoUserAccountsAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, CheckIPAddresses(service_name="SAVINGS")]
    serializer_class = CreateAjoUserAccountNumberSerializer

    def post(self, request):
        user = self.request.user
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            unique_reference = serializer.validated_data["unique_reference"]
            account_name = serializer.validated_data["account_name"]
            ajo_collector = serializer.validated_data["ajo_collector"]
            bvn_details = serializer.validated_data.get("bvn_details")
            corporate_id = serializer.validated_data.get("corporate_id")

            if not corporate_id and not bvn_details:
                response = {
                    "error": "545",
                    "message": "Must Pass BVN details for non corporate accounts",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if corporate_id and bvn_details:
                response = {
                    "error": "546",
                    "message": "Cannot Pass BVN details for corporate accounts",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if corporate_id:
                if not account_name.startswith("LIBERTYPAY"):
                    response = {
                        "error": "546",
                        "message": "Cannot Find LIBERTYPAY in account name",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                create_acct = OtherServiceAccountSystem.arrange_corporate_detail(
                    user=user,
                    new_user=user,
                    corporate_id=corporate_id,
                    suffix=None,
                    get_location=None,
                    company_name=account_name,
                    ajo_user_details={
                        "unique_reference": unique_reference,
                        "ajo_collector": ajo_collector,
                    }
                )

            else:
                create_acct = OtherServiceAccountSystem.create_vfd_others_collection_acct(
                    user=user,
                    requested_by=user,
                    overide=False,
                    ajo_user_details={
                        "unique_reference": unique_reference,
                        "ajo_collector": ajo_collector,
                        "bvn_details": bvn_details,
                    }
                )

            if create_acct["status"] not in ["success", True]:
                response = {
                    "error": "848",
                    "message": f"{create_acct['message']}",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if not corporate_id:
                response = create_acct
            else:
                response = create_acct["data"]

            return Response(response, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AdminCreateAccountAPIView(APIView):
    permission_classes = [CheckDynamicAuthentication, AdminLockPermission]
    serializer_class = AdminCreateCorporateSerializer

    def post(self, request):
        user = self.request.user

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            super_user = serializer.validated_data["super_user"]
            new_user = serializer.validated_data["new_user"]
            corporate_id = serializer.validated_data["corporate_id"]
            company_name = serializer.validated_data["company_name"]
            suffix = serializer.validated_data.get("suffix")
            get_location = serializer.validated_data.get("get_location")

            create_corporate = OtherServiceAccountSystem.arrange_corporate_detail(user=super_user, new_user=new_user, corporate_id=corporate_id,
                                                                                  suffix=suffix, get_location=get_location, company_name=company_name)

            return Response(create_corporate, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class HandleNoTransFoundAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = HandleOOBSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():

            account_provider = serializer.validated_data["account_provider"]
            from_acct_no = serializer.validated_data["from_acct_no"]
            to_acct_no = serializer.validated_data["to_acct_no"]
            amount = serializer.validated_data["amount"]
            user_email = serializer.validated_data["user_email"]
            pin = serializer.validated_data["pin"]
            escrow_id = serializer.validated_data.get("escrow_id")
            liberty_reference = serializer.validated_data.get("liberty_reference")
            old_liberty_reference = serializer.validated_data.get("old_liberty_reference")

            user_instance = request.user

            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=user_instance, pincode=pin
            )

            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(user_instance)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)


            else:
                User.reset_transaction_pin_retries(user_instance)

                try:
                    user = User.objects.get(email=user_email)
                except Exception as err:
                    response = {
                        "error": "545",
                        "message": "User with email does not exist",
                    }

                    return Response(response, status=status.HTTP_200_OK)

                # Get Receiver Account
                get_ben_account_name = AccountSystem.objects.filter(user=user, account_number=to_acct_no).first()
                if not get_ben_account_name:
                    response = {
                        "error": "545",
                        "message": "Account Does Not Exist For User"
                    }

                    return Response(response, status=status.HTTP_200_OK)

                get_float_account = AccountSystem.get_float_account(from_wallet_type="FLOAT", from_provider_type=account_provider)
                get_comm_account = AccountSystem.get_float_account(from_wallet_type="COMMISSIONS", from_provider_type=account_provider)

                transaction_inst = Transaction.objects.filter(liberty_reference=liberty_reference).last()
                old_transaction_inst = Transaction.objects.filter(liberty_reference=old_liberty_reference).last() if old_liberty_reference else None

                if not liberty_reference and old_transaction_inst:
                    response = {
                        "error": "546",
                        "message": "You must provide new_trans_ref if providing old_trans_ref",
                    }

                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                if not transaction_inst and not old_transaction_inst:
                    # if (old_liberty_reference and not old_transaction_inst) or (not old_liberty_reference and not Transaction.objects.filter(liberty_reference=liberty_reference).exists()):
                    # Get Sender Account
                    get_sender_account = AccountSystem.objects.filter(account_number=from_acct_no).first()

                    if get_float_account.account_number == to_acct_no:
                        trans_user = get_sender_account.user
                        transaction_type = "BANK_OOB_IN"
                        transaction_leg = "INTERNAL"

                    elif get_float_account.account_number == from_acct_no:
                        trans_user = get_ben_account_name.user
                        transaction_type = "BANK_OOB_OUT"
                        transaction_leg = "INTERNAL"

                    elif get_comm_account.account_number == to_acct_no:
                        trans_user = get_sender_account.user
                        transaction_type = "BANK_OOB_COMM"
                        transaction_leg = "INTERNAL"

                    elif get_comm_account.account_number == from_acct_no:
                        trans_user = get_ben_account_name.user
                        transaction_type = "BANK_OOB_COMM"
                        transaction_leg = "INTERNAL"

                    narration = transaction_type

                    if not escrow_id:
                        escrow_instance = Escrow.objects.create(
                            user=trans_user,
                            transfer_type=transaction_type,
                            narration=narration,
                            liberty_reference=liberty_reference,
                            pos_charge=0,
                            pos_charge_type="BANK"
                        )

                        escrow_id = escrow_instance.escrow_id

                    transaction_inst = Transaction.objects.create(
                        user=user,
                        account_provider=account_provider,
                        transaction_type=transaction_type,
                        amount=amount,
                        provider_fee=0,
                        liberty_reference=liberty_reference,
                        liberty_commission=0,
                        extra_fee=0,
                        user_trans_band=user.trans_band,
                        escrow_id=escrow_id,
                        beneficiary_account_name=get_ben_account_name.account_name,
                        beneficiary_nuban=get_ben_account_name.account_number,
                        beneficiary_bank_code=get_ben_account_name.bank_code,
                        narration=narration,
                        status="IN_PROGRESS",
                        transaction_leg=transaction_leg,
                    )

                if old_transaction_inst and liberty_reference and not transaction_inst:
                    transaction_inst = old_transaction_inst

                handle_rev = AccountSystem.handle_out_of_books(
                    from_account=from_acct_no,
                    to_account=to_acct_no,
                    amount=amount,
                    escrow_id=escrow_id,
                    user_bvn_number=user.bvn_number,
                    transaction_instance=transaction_inst,
                    liberty_reference=liberty_reference
                )

                log_info(str(handle_rev))

                if old_liberty_reference:
                    get_trans = TransferVerificationObject.objects.filter(liberty_reference=old_liberty_reference).last()
                    if get_trans:
                        DeletedReference.objects.create(
                            transaction_instance=get_trans.transaction_instance,
                            liberty_reference=get_trans.transaction_instance.liberty_reference,
                            trans_type=get_trans.transaction_instance.transaction_type,
                        )

                        get_trans.liberty_reference = liberty_reference
                        get_trans.transaction_instance.liberty_reference = liberty_reference
                        get_trans.is_verified = False
                        get_trans.save()
                        get_trans.transaction_instance.save()

                response = {
                    "status": "success",
                    "payload": handle_rev
                }

                return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GetVFDAccountTransactions(APIView):
    permission_classes = [CheckDynamicAuthentication]

    # serializer_class = GetVFDAccountTransactionsSerializer

    def get(self, request):
        params = dict(request.query_params.items())

        # <QueryDict: {'fromDate': ['2023-02-03'], 'toDate': ['2023-10-30'], 'page': ['1'], 'size': ['1'], 'transactionType': ['bank'], 'accountNo': ['**********']}>

        # serializer = self.serializer_class(data=request.data)
        # serializer.is_valid(raise_exception=True)

        # page = serializer.validated_data["page"]
        # from_date = serializer.validated_data["from_date"].strftime("%Y-%m-%d")
        # to_date = serializer.validated_data["to_date"].strftime("%Y-%m-%d")
        # account_num = serializer.validated_data.get("account_number")
        # transaction_type = serializer.validated_data.get("transaction_type")

        # response = VFDBank.get_account_transactions(page=page, from_date=from_date, to_date=to_date, transaction_type=transaction_type,  account_num=account_num)

        response = VFDBank.get_account_transactions(params=params)
        return Response(response, status=status.HTTP_200_OK)


class SetUpTransactionsFor108ResolveAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]
    serializer_class = ManualSendMoneyAdminSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            user_instance = self.request.user
            trans_data = serializer.validated_data["trans_data"]
            pin = serializer.validated_data["pin"]

            use_task = serializer.validated_data.get("use_task")

            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=user_instance, pincode=pin
            )

            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(user_instance)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)


            else:
                User.reset_transaction_pin_retries(user_instance)

                float_account_num = AccountSystem.get_dynamic_float_account(from_wallet_type="FLOAT", from_provider_type="VFD").account_number
                float_comm_num = AccountSystem.get_dynamic_float_account(from_wallet_type="COMMISSIONS", from_provider_type="VFD").account_number

                results = []
                errors = []

                if use_task:
                    set_up_108_task.apply_async(
                        queue="processbulksheet",
                        kwargs={
                            "trans_data": [x.id for x in trans_data]
                        }
                    )

                else:

                    for trans_verf in trans_data:
                        error_message = []

                        trans_verf: TransferVerificationObject
                        transaction_inst = trans_verf.transaction_instance
                        liberty_reference = trans_verf.liberty_reference

                        serialized_trans = TransVerfSerializer(trans_verf)

                        if trans_verf.transaction_leg == "COMMISSIONS":
                            # Source Nuban
                            if not trans_verf.source_nuban:
                                if transaction_inst.transaction_sub_type == "MAIN_TRSF_COMM":
                                    trans_verf.source_nuban = Escrow.objects.get(escrow_id=transaction_inst.escrow_id).user_account_number
                                else:
                                    trans_verf.source_nuban = float_account_num

                            # Beneficiary Nuban
                            if not trans_verf.beneficiary_nuban:
                                trans_verf.beneficiary_nuban = float_comm_num

                        if trans_verf.transaction_leg == "INFLOW_TO_FLOAT":
                            if not trans_verf.source_nuban:
                                trans_verf.source_nuban = transaction_inst.source_nuban

                            if not trans_verf.beneficiary_nuban:
                                trans_verf.beneficiary_nuban = transaction_inst.beneficiary_nuban

                        if trans_verf.transaction_leg == "RE_INTERNAL":
                            if not trans_verf.source_nuban:
                                trans_verf.source_nuban = transaction_inst.source_nuban

                            if not trans_verf.beneficiary_nuban:
                                trans_verf.beneficiary_nuban = float_account_num

                        if not trans_verf.bank_code:
                            trans_verf.bank_code = transaction_inst.beneficiary_bank_code

                        if trans_verf.transaction_leg not in ["INFLOW_TO_FLOAT", "COMMISSIONS", "RE_INTERNAL"]:
                            response = {
                                "error": "545",
                                "message": "Transfer Leg is not in INFLOW_TO_FLOAT or COMMISSIONS"
                            }
                            error_message.append(response)

                        check_status = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference, fail=True)
                        check_status_code = check_status["status"]
                        # if check_status_code != "108":
                        #     response = {
                        #         "error": "545",
                        #         "message": "trans has been sent before now. already verified"
                        #     }
                        #     error_message.append(response)

                        if check_status_code == "108":
                            trans_verf.trans_status_code = check_status_code
                        else:
                            trans_verf.trans_status_code = check_status["data"]["transactionStatus"]

                        trans_verf.save()

                        if error_message:
                            errors.append({
                                "status": "error",
                                "data": serialized_trans.data,
                                "response": error_message
                            })
                            continue

                        results.append({
                            "status": "success",
                            "data": serialized_trans.data,
                        })

                new_resp = {
                    "good_data_ids": [x["data"]["id"] for x in results],
                    "good_data": results,
                    "bad_data_ids": [x["data"]["id"] for x in errors],
                    "bad_data": errors,
                }
                return Response(new_resp, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ManualSendMoneyAdminAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]
    serializer_class = ManualSendMoneyAdminSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            user_instance = self.request.user
            trans_data = serializer.validated_data["trans_data"]
            pin = serializer.validated_data["pin"]
            use_task = serializer.validated_data.get("use_task")

            status_codes = request.data.get("status_codes")

            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=user_instance, pincode=pin
            )

            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(user_instance)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)


            else:
                User.reset_transaction_pin_retries(user_instance)

                results = []
                errors = []

                if use_task:
                    resolve_108_task.apply_async(
                        queue="processbulksheet",
                        kwargs={
                            "trans_data": [x.id for x in trans_data],
                            "status_codes": status_codes
                        }
                    )

                else:

                    for trans_verf in trans_data:
                        error_message = []

                        trans_verf: TransferVerificationObject
                        from_acct_no = trans_verf.source_nuban
                        to_acct_no = trans_verf.beneficiary_nuban
                        amount = trans_verf.amount
                        bank_code = trans_verf.bank_code
                        escrow_id = trans_verf.escrow_id
                        liberty_reference = trans_verf.liberty_reference
                        transaction_inst = trans_verf.transaction_instance
                        trans_user = transaction_inst.user

                        serialized_trans = TransVerfSerializer(trans_verf)

                        if trans_verf.trans_status_code not in status_codes:
                            response = {
                                "error": "545",
                                "message": "Transfer status not status codes"
                            }

                            error_message.append(response)

                        if trans_verf.transaction_leg == "EXTERNAL":
                            response = {
                                "error": "545",
                                "message": "Transfer Leg is external"
                            }
                            error_message.append(response)

                        if not to_acct_no or not from_acct_no or not bank_code:
                            response = {
                                "error": "545",
                                "message": "either there is no source nuban or no beneficiary nuban or no bank code"
                            }
                            error_message.append(response)

                        check_status = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference, fail=True)
                        check_status_code = check_status["status"] if check_status["status"] == "108" else check_status["data"]["transactionStatus"]

                        if check_status_code == "00":
                            response = {
                                "error": "545",
                                "message": "trans has been sent before now. already verified"
                            }
                            error_message.append(response)

                        if error_message:
                            errors.append({
                                "status": "error",
                                "data": serialized_trans.data,
                                "response": error_message
                            })
                            continue

                        if check_status_code in ["51", "99"]:
                            if trans_verf.transaction_instance.transaction_leg == "INFLOW_TO_FLOAT":
                                liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDF")

                            elif trans_verf.transaction_instance.transaction_leg == "EXTERNAL":
                                liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDE")

                            elif trans_verf.transaction_instance.transaction_leg == "INTERNAL":
                                liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDI")

                            elif trans_verf.transaction_instance.transaction_leg == "REVERSAL":
                                liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFD-RVS")

                            elif trans_verf.transaction_instance.transaction_leg == "COMMISSIONS":
                                liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDC")

                            elif trans_verf.transaction_instance.transaction_leg == "RE_INTERNAL":
                                liberty_reference = Transaction.create_liberty_reference_with_old_reference(
                                    liberty_reference=trans_verf.liberty_reference, suffix="RVSL")
                            else:
                                liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFD")

                            DeletedReference.objects.create(
                                transaction_instance=trans_verf.transaction_instance,
                                liberty_reference=trans_verf.transaction_instance.liberty_reference,
                                trans_type=trans_verf.transaction_instance.transaction_type,
                            )

                            trans_verf.liberty_reference = liberty_reference
                            trans_verf.transaction_instance.liberty_reference = liberty_reference
                            trans_verf.is_verified = False
                            trans_verf.save()
                            trans_verf.transaction_instance.save()

                        response = AccountSystem.handle_out_of_books(
                            from_account=from_acct_no,
                            to_account=to_acct_no,
                            amount=amount,
                            escrow_id=escrow_id,
                            user_bvn_number=trans_user.bvn_number,
                            transaction_instance=transaction_inst,
                            liberty_reference=liberty_reference
                        )

                        results.append({
                            "status": "success",
                            "data": serialized_trans.data,
                            "response": response
                        })

                new_resp = {
                    "good_data": results,
                    "bad_data": errors
                }

                return Response(new_resp, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ChangeTransReferenceAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = ManualSendMoneyAdminSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            user_instance = self.request.user
            trans_data = serializer.validated_data["trans_data"]
            pin = serializer.validated_data["pin"]

            status_code = request.data.get("status_code")

            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=user_instance, pincode=pin
            )

            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(user_instance)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)


            else:
                User.reset_transaction_pin_retries(user_instance)

                results = []
                errors = []

                for trans_verf in trans_data:
                    error_message = []

                    trans_verf: TransferVerificationObject
                    transaction_inst = trans_verf.transaction_instance
                    liberty_reference = trans_verf.liberty_reference

                    serialized_trans = TransVerfSerializer(trans_verf)

                    if not trans_verf.source_nuban or not trans_verf.beneficiary_nuban or not trans_verf.bank_code:
                        response = {
                            "error": "545",
                            "message": "Transfer does not have either source or ben or bank code"
                        }
                        error_message.append(response)

                    check_status = VFDBank.vfd_transaction_verification_handler(reference=liberty_reference, fail=True)
                    check_status_code = check_status["status"] if check_status["status"] == "108" else check_status["data"]["transactionStatus"]

                    if check_status_code in ["00"] or check_status_code != status_code:
                        response = {
                            "error": "545",
                            "message": f"Transfer is already successful or not equal to status code {check_status_code}"
                        }
                        error_message.append(response)

                    else:
                        if trans_verf.transaction_leg == "INFLOW_TO_FLOAT":
                            new_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDF")
                        elif trans_verf.transaction_leg == "COMMISSION":
                            new_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDC")
                        elif trans_verf.transaction_leg == "INTERNAL":
                            new_liberty_reference = Transaction.create_liberty_reference(suffix="LGLP-VFDI")
                        elif trans_verf.transaction_leg == "RE_INTERNAL":
                            new_liberty_reference = Transaction.create_liberty_reference_with_old_reference(
                                liberty_reference=trans_verf.liberty_reference, suffix="RVSL")
                        else:
                            new_liberty_reference = None

                        if new_liberty_reference is None:
                            response = {
                                "error": "545",
                                "message": f"Cant detect transaction leg"
                            }
                            error_message.append(response)

                        else:
                            DeletedReference.objects.create(
                                transaction_instance=transaction_inst,
                                liberty_reference=transaction_inst.liberty_reference,
                                new_liberty_reference=new_liberty_reference,
                                trans_type=transaction_inst.transaction_type,
                            )

                            trans_verf.liberty_reference = new_liberty_reference
                            trans_verf.transaction_instance.liberty_reference = new_liberty_reference
                            trans_verf.is_verified = False
                            trans_verf.transaction_instance.save()

                    if error_message:
                        errors.append({
                            "status": "error",
                            "data": serialized_trans.data,
                            "response": error_message
                        })
                        continue

                    trans_verf.save()

                    results.append({
                        "status": "success",
                        "data": serialized_trans.data,
                    })

                new_resp = {
                    "good_data_ids": [x["data"]["id"] for x in results],
                    "good_data": results,
                    "bad_data_ids": [x["data"]["id"] for x in errors],
                    "bad_data": errors,
                }
                return Response(new_resp, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AdminTransferAPIView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated, AdminLockPermission]
    serializer_class = AdminTransferSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            user_instance = self.request.user
            from_account = serializer.validated_data["from_account"]
            to_account = serializer.validated_data["to_account"]
            amount = serializer.validated_data["amount"]
            pin = serializer.validated_data["pin"]

            # Check Transaction Pin
            chcek_pin = User.check_sender_transaction_pin(
                user=user_instance, pincode=pin
            )

            if chcek_pin == False:
                retries = User.count_down_transaction_pin_retries(user_instance)

                response = {
                    "error": "error",
                    "message": "Incorrect Pin",
                    "retry_count": retries["retry_count"],
                    "remaining_retries": retries["remaining_retries"],
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)


            else:
                User.reset_transaction_pin_retries(user_instance)

                float_data = AccountSystem.get_dynamic_float_account(from_wallet_type="FLOAT", from_provider_type="VFD")
                float_account_num = float_data.account_number

                from_account_data = AccountSystem.objects.filter(account_number=from_account).last()
                to_account_data = AccountSystem.objects.filter(account_number=to_account).last()

                if not from_account_data or not to_account_data:
                    response = {
                        "status": "error",
                        "message": "Either of the accounts not in system",
                        "extra_data": f"From Account: {not (not from_account_data)}. To Account: {not (not to_account_data)}"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                if from_account != float_account_num and to_account != float_account_num:
                    response = {
                        "status": "error",
                        "message": "Either of the accounts must be the float account number",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                if to_account != float_account_num:
                    user_bvn_number = to_account_data.user.bvn_number
                    oob_sum_qs = OutOfBookTransfer.objects.filter(date_created=datetime.now().date(), to_account=to_account).aggregate(Sum('amount'))
                    oob_sum = oob_sum_qs["amount__sum"] or float(0.00)

                    if oob_sum + amount >= ConstantTable.get_constant_table_instance().oob_max_sum:
                        response = {
                            "status": "error",
                            "message": "Amount exceeded OOB max sum",
                        }
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    user_bvn_number = from_account_data.user.bvn_number

                liberty_reference = Transaction.create_liberty_reference(suffix="MANUAL_TRSF")

                response = AccountSystem.handle_out_of_books(
                    from_account=from_account,
                    to_account=to_account,
                    amount=amount,
                    escrow_id=None,
                    user_bvn_number=user_bvn_number,
                    transaction_instance=None,
                    liberty_reference=liberty_reference
                )

                response = {
                    "status": "success",
                    "message": "successfully sent",
                    "extra_data": response
                }
                return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AdminGetAllTransactionsAPIView(generics.ListAPIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated]
    serializer_class = AllTransactionSerializer
    pagination_class = CustomPagination
    search_fields = ['liberty_reference', 'user__email', 'transaction_id', 'escrow_id', 'unique_reference']
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = TransactionDateFilter

    def get_queryset(self):
        return Transaction.objects.all().order_by("-date_created")

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        page_count = self.paginator.page.paginator.num_pages
        serializer = self.get_serializer(page, many=True)

        response.data = {
            'count': paginator.page.paginator.count,
            'next': paginator.get_next_link(),
            'previous': paginator.get_previous_link(),
            'page_count': page_count,
            'results': serializer.data
        }

        return response


class AdminGetAllTransferVerfAPIView(generics.ListAPIView):
    authentication_classes = [CustomTokenAuthentication, TechSupportPermission]
    permission_classes = [CustomIsAuthenticated]
    serializer_class = AllTransVerfSerializer
    pagination_class = CustomPagination
    search_fields = ['liberty_reference', 'user__email', 'transaction_id', 'escrow_id', 'unique_reference']
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = TransVerfDateFilter

    def get_queryset(self):
        return TransferVerificationObject.objects.all().order_by("-date_added")

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        page_count = self.paginator.page.paginator.num_pages
        serializer = self.get_serializer(page, many=True)

        response.data = {
            'count': paginator.page.paginator.count,
            'next': paginator.get_next_link(),
            'previous': paginator.get_previous_link(),
            'page_count': page_count,
            'results': serializer.data
        }

        return response


class AdminGetAllCommissionsAPIView(generics.ListAPIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated]
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]

    def get_queryset(self):
        if self.request.query_params.get("type") == "VAS":
            queryset = CommissionsRecord.objects.all().order_by("-date_created")
            filterset_class = CommissionsDateFilter
        else:
            queryset = OtherCommissionsRecord.objects.all().order_by("-date_created")
            filterset_class = OtherCommissionsRecordDateFilter

        # filtered using the chosen filterset_class
        filterset = filterset_class(self.request.query_params, queryset=queryset)
        queryset = filterset.qs

        return queryset

    def get_serializer_class(self):
        if self.request.query_params.get("type") == "VAS":
            return CommissionsRecordSerializer
        else:
            return OtherCommissionsRecordSerializer

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        page_count = self.paginator.page.paginator.num_pages
        serializer = self.get_serializer(page, many=True)

        response.data = {
            'count': paginator.page.paginator.count,
            'next': paginator.get_next_link(),
            'previous': paginator.get_previous_link(),
            'page_count': page_count,
            'results': serializer.data
        }

        return response


class GetAllVFDAccountData(APIView):
    authentication_classes = [CustomTokenAuthentication, AdminLockPermission]
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        account_nums = list(AccountSystem.get_all_vfd_accounts().values_list("account_number", flat=True))

        data = VFDBank.grequest_get_vfd_balances(account_number_list=account_nums)

        response = {
            "status": "success",
            "message": "successfully retrieved",
            "data": data
        }
        return Response(response, status=status.HTTP_200_OK)


class GetAllBulkTransferView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        bulk_id = request.query_params.get("bulk_id")
        trans_type = request.query_params.get("trans_type")

        user: User = request.user

        escrow_qs = Escrow.objects.filter(user=user, bulk_id__isnull=False)

        instances_by_bulk_id = {}

        escrow_data = escrow_qs.filter(transfer_type="SEND_BANK_TRANSFER")

        for instance in escrow_data:
            data = ReturnEscrowTransactions.get_trans_with_escrow(instance)

            bulk_id = instance.bulk_id
            if bulk_id in instances_by_bulk_id:
                instances_by_bulk_id[bulk_id].append(data)
            else:
                instances_by_bulk_id[bulk_id] = [data]

        response = {
            "status": "success",
            "message": "successfully retrieved",
            "data": instances_by_bulk_id
        }
        return Response(response, status=status.HTTP_200_OK)


class UpdateVFDAccountNINBVN(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]

    def get(self, request):

        hostname = socket.gethostname()
        IPAddr = socket.gethostbyname(hostname)

        nuban = request.query_params.get('nuban')
        nin = request.query_params.get('nin')
        utype = request.query_params.get('utype')

        bvn = None

        response = {}
        response["bvn"] = bvn
        response["ip_addr"] = request.META['REMOTE_ADDR']
        response["socket"] = IPAddr

        get_user_qs = AccountSystem.objects.filter(account_number=nuban)
        # get_user_qs = AccountSystem.objects.filter(account_number=nuban)
        if get_user_qs.count() > 1:
            check_coll = get_user_qs.filter(account_type="COLLECTION")
            if check_coll.count() == 1:
                get_user = check_coll.first()
            else:
                response.update({
                    "status": False,
                    "message": "Found more than 1 account",
                })
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        elif not get_user_qs.exists():
            response.update({
                "status": False,
                "message": "No Account Found",
            })
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        else:
            get_user = get_user_qs.first()

        user = get_user.user
        bvn = user.bvn_number
        dob = None

        if utype == VfdAccountUpdateType.RECOMPLY:
            dob = user.check_kyc.bvn_rel.bvn_birthdate

            output_date_format = "%d-%b-%Y"
            output_date = convert_any_date_format(dob, output_date_format)
            if not output_date:
                response = {
                    "status": False,
                    "message": "Invalid BVN Date of Birth",
                }
                return Response(response, status=status.HTTP_200_OK)

            dob = output_date

        # if nin:
        #     response = VFDBank.update_bvn_nin(account_number=nuban, bvn=bvn, nin=nin)
        if bvn:
            response.update(VFDBank.update_bvn_nin(account_number=nuban, bvn=bvn, utype=utype, dob=dob))
        else:
            response.update({
                "status": False,
                "message": "No BVN or NIN Found on account",
            })

        return Response(response, status=status.HTTP_200_OK)


class GetAccountWithBVN(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]

    def get(self, request):

        hostname = socket.gethostname()
        IPAddr = socket.gethostbyname(hostname)

        nuban = request.query_params.get('nuban')
        nin = request.query_params.get('nin')

        get_user_qs = AccountSystem.objects.filter(account_number=nuban, account_type="COLLECTION")
        if get_user_qs.count() > 1:
            response = {
                "status": False,
                "message": "Found more than 1 account",
            }
        elif not get_user_qs.exists():
            response = {
                "status": False,
                "message": "No Account Found",
            }
        else:
            get_user = get_user_qs.first()
            user = get_user.user
            bvn = user.bvn_number

            if bvn:
                response = VFDBank.get_account_with_bvn(bvn=bvn)
            else:
                response = {
                    "status": False,
                    "message": "No BVN or NIN Found on account",
                }

        response["ip_addr"] = request.META['REMOTE_ADDR']
        response["socket"] = IPAddr

        if response.get("main_data") and response.get("main_data").get("data") and response.get("main_data").get("data").get("dateOfBirth"):
            detail_resp = response["main_data"]["data"]
            detail_resp.pop("dateOfBirth", None)

            response["main_data"]["data"] = detail_resp
        return Response(response, status=status.HTTP_200_OK)


class RetriggerInflowSessionID(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]
    serializer_class = VFDSessionIDInflowRetrySerializer

    def post(self, request):
        hostname = socket.gethostname()
        IPAddr = socket.gethostbyname(hostname)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        session_id = serializer.validated_data["session_id"]

        response = VFDBank.trigger_inflow_repush(session_id=session_id)

        response["ip_addr"] = request.META['REMOTE_ADDR']
        response["socket"] = IPAddr
        return Response(response, status=status.HTTP_200_OK)


class AccountNameUpdateVFD(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]

    def get(self, request):

        hostname = socket.gethostname()
        IPAddr = socket.gethostbyname(hostname)

        nuban = request.query_params.get('nuban')
        bvn = None

        get_user_qs = AccountSystem.objects.filter(account_number=nuban, account_type="COLLECTION")
        if get_user_qs.count() > 1:
            response = {
                "status": False,
                "message": "Found more than 1 account",
            }
        elif not get_user_qs.exists():
            response = {
                "status": False,
                "message": "No Account Found",
            }
        else:
            get_user = get_user_qs.first()
            user = get_user.user
            bvn = user.bvn_number

            try:
                if bvn:
                    VFDBank.create_vfd_wallet(user, user.phone_number, 0, overide=True)

                    response = VFDBank.get_account_with_bvn(bvn=bvn)

                else:
                    response = {
                        "status": False,
                        "message": "No BVN Found on account",
                    }
            except Exception as err:
                log_error(f"VFD wallet creation has failed: {err}")
                pass

        response["bvn"] = bvn
        response["ip_addr"] = request.META['REMOTE_ADDR']
        response["socket"] = IPAddr

        if response.get("main_data") and response.get("main_data").get("data") and response.get("main_data").get("data").get("dateOfBirth"):
            detail_resp = response["main_data"]["data"]
            detail_resp.pop("dateOfBirth", None)

            response["main_data"]["data"] = detail_resp
        return Response(response, status=status.HTTP_200_OK)


class RetriggerCallbackForOtherServiceAccounts(APIView):
    permission_classes = [CustomIsAuthenticated, TechSupportPermission]
    serializer_class = RetriggerCallbackForOtherSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        trx_id = serializer.validated_data["identifier"]
        trx_id_type = serializer.validated_data["type"]

        trx_filters = {
            self.serializer_class.TRX_ID: {"transaction_id": trx_id},
            self.serializer_class.REFERENCE: {"liberty_reference": trx_id},
            self.serializer_class.ID: {"id": trx_id}
        }

        try:
            trx_filter = trx_filters.get(trx_id_type, {})
            trx = Transaction.objects.get(**trx_filter)
        except Transaction.DoesNotExist:
            error_message = "Transaction does not exist"
            return Response({"status": "error", "message": error_message}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            error_message = str(e)
            return Response({"status": "error", "message": error_message}, status=status.HTTP_400_BAD_REQUEST)

        send_callback = send_callback_out_for_send_to_lotto(None, trx)
        is_success = send_callback == "Done" or type(send_callback) == dict or send_callback is None

        response_data = {
            "status": "success" if is_success else "error",
            "message": "callback sent" if is_success else "Other Service Transaction does not exist",
            "call_back": send_callback,
            "liberty_reference": trx.liberty_reference,
        }
        status_code = status.HTTP_200_OK if is_success else status.HTTP_400_BAD_REQUEST

        return Response(response_data, status=status_code)


class GenerateWeeklyTransactionReport(APIView):
    permission_classes = []

    def get(self, request):
        from dateutil.rrule import rrule, DAILY
        current_date = datetime.now()

        exclude_trans = [
            "ELECTRONIC_TRANSFER_LEVY", "SEND_LIBERTY_COMMISSION", "REVERSAL_BANK_TRANSFER_IN", "REVERSAL_BANK_TRANSFER",
            "FUND_TRANSFER_FROM_COMMISSION", "AJO_LOAN_COMMISSIONS", "ASSURED_LOAN_COMMISSIONS", "FUND_TRANSFER_FROM_OTHER_COMMISSION",
            "BILLS_AND_PAYMENT_REVERSAL", "BANK_OOB_IN", "BANK_OOB_OUT", "BANK_OOB_COMM", "RE_FAILED_TRANSFER_IN"
        ]

        report_format = request.GET.get("format", "csv")
        json_response = list()
        weekly_list = list()
        monthly_list = list()
        json_data = dict()

        role = "ADMIN"
        if request.user and request.user.is_authenticated:
            if request.user.email in ["<EMAIL>", "<EMAIL>"]:
                role = "ADMIN"
            else:
                role = str(request.user.type_of_user).upper()

        first_trans_create_date = Transaction.objects.all().exclude(transaction_type__in=exclude_trans).order_by("date_created").first().date_created
        trans_age = sum(1 for dt in rrule(DAILY, dtstart=first_trans_create_date.date(), until=current_date.date()) if dt.weekday() not in [6])

        agent_query = Q(is_paybox_merchant=True, type_of_user="MERCHANT")

        # Get all active agents
        if role == "STAFF_AGENT":
            referral_code = request.user.referral_code
            agents = User.objects.filter(agent_query, referer_code=referral_code)
        elif role == "MERCHANT":
            agents = User.objects.filter(id=request.user.id)
        else:
            agents = User.objects.filter(agent_query)

        agent_count = agents.count()

        # Aggregate all-time transaction stats in one query
        all_time_data = Transaction.objects.filter(user__in=agents, status="SUCCESSFUL").exclude(transaction_type__in=exclude_trans).aggregate(
            total_count=Count("id"),
            total_amount=Sum("amount", output_field=FloatField()),
            commission=Sum("liberty_commission", output_field=FloatField())
        )
        all_time_count = all_time_data.get("total_count") or 0
        all_time_amount = all_time_data.get("total_amount") or 0
        all_commission = all_time_data.get("commission") or 0

        all_time_daily_tpv = all_time_amount / agent_count
        all_time_weekly_tpv = all_time_daily_tpv * 7
        all_time_monthly_tpv = all_time_weekly_tpv * 4
        all_time_total_volume = all_time_amount
        all_time_gross_net = all_commission
        all_time_net_agent_monthly = all_time_gross_net / agent_count
        all_time_net_agent_daily = all_time_net_agent_monthly / 30
        all_time_avg_transaction = all_time_count / agent_count
        all_time_count_per_day = all_time_daily_tpv / all_time_count
        all_time_percentage_gross_net = (all_time_total_volume * 0.25) / 100
        all_actual_per_transaction = agent_count / all_time_gross_net / trans_age

        week_start, week_end = get_week_start_and_end_datetime(current_date)
        month_start, month_end = get_month_start_and_end_datetime(current_date)
        new_week_date = [dt for dt in rrule(DAILY, dtstart=week_start.date(), until=current_date.date()) if dt.weekday() not in [6]]
        week_date_age = len(new_week_date)

        wb = ws2 = ws3 = None

        if report_format == "csv":
            wb = Workbook()
            ws1 = wb.active
            ws1.title = "All Time Data"
            ws1.append([
                "Agent Count", "Daily TPV", "Weekly TPV", "Monthly TPV", "Total Volume", "Gross Net", "Net/Agent/Month", "Net/Agent/Day",
                "Count of transactions/day", "Average transaction", "Percentage Gross Net", "Actual Per Transaction", "Activity Status", "Report Days"
            ])
            ws1.append([
                agent_count, all_time_daily_tpv, all_time_weekly_tpv, all_time_monthly_tpv, all_time_total_volume, all_time_gross_net,
                all_time_net_agent_monthly, all_time_net_agent_daily, all_time_count_per_day, all_time_avg_transaction, all_time_percentage_gross_net,
                all_actual_per_transaction, "Active", "All Time"
            ])
            ws2 = wb.create_sheet(title="Weekly Data")
            ws2.append([
                "Agent Name", "Daily TPV", "Weekly TPV", "Monthly TPV", "Total Volume", "Gross Net", "Net/Agent/Month", "Net/Agent/Day",
                "Count of transactions/day", "Average transaction", "Percentage Gross Net", "Actual Per Transaction", "Activity Status",
                "Report Days",
                "Merchant Age", "Transactions Age"
            ])
            ws3 = wb.create_sheet(title="Monthly Data")
            ws3.append(
                [
                    "Agent Name", "Daily TPV", "Weekly TPV", "Monthly TPV", "Total Volume", "Gross Net", "Net/Agent/Month", "Net/Agent/Day",
                    "Count of transactions/day", "Average transaction", "Percentage Gross Net", "Actual Per Transaction", "Activity Status",
                    "Report Days", "Merchant Age", "Transactions Age"
                ]
            )

        else:
            all_time_json = [
                {
                    "agent_count": agent_count,
                    "daily_tpv": all_time_daily_tpv,
                    "weekly_tpv": all_time_weekly_tpv,
                    "monthly_tpv": all_time_monthly_tpv,
                    "total_volume": all_time_total_volume,
                    "gross_net": all_time_gross_net,
                    "net_agent_month": all_time_net_agent_monthly,
                    "net_agent_day": all_time_net_agent_daily,
                    "count_trans_day": all_time_count_per_day,
                    "avg_trans": all_time_avg_transaction,
                    "percent_gross_net": all_time_percentage_gross_net,
                    "actual_per_trans": all_actual_per_transaction,
                    "activity_status": 'Active',
                    "report_days": 'All Time'
                }
            ]
            json_data["all_time_report"] = all_time_json

        weekly_data = Transaction.objects.filter(
            date_created__range=[week_start, week_end], user__in=agents, status="SUCCESSFUL"
        ).exclude(transaction_type__in=exclude_trans).values("user__id", "user__first_name", "user__last_name").annotate(
            total_count=Count("id"),
            total_amount=Sum("amount", output_field=FloatField()),
            commission=Sum("liberty_commission", output_field=FloatField())
        )

        for entry in weekly_data:
            agent = User.objects.get(id=entry["user__id"])
            query = Q(user=agent, status="SUCCESSFUL")
            entry_monthly_gross_net = Transaction.objects.filter(
                query, date_created__range=[month_start, month_end]).exclude(
                transaction_type__in=exclude_trans).aggregate(Sum("amount"))["amount__sum"] or 0
            first_trans = Transaction.objects.filter(query).exclude(transaction_type__in=exclude_trans).order_by("date_created")

            agent_first_trans_age = sum(1 for dt in rrule(
                DAILY, dtstart=first_trans.first().date_created.date(), until=current_date.date()) if dt.weekday() not in [6]) if first_trans else 0
            agent_date_joined_age = sum(
                1 for dt in rrule(DAILY, dtstart=agent.date_joined.date(), until=current_date.date()) if dt.weekday() not in [6]
            )

            weekly_amount = entry.get("total_amount") or 0
            weekly_count = entry.get("total_count") or 0
            weekly_commission = entry.get("commission") or 0

            agent_name = str(entry["user__first_name"]).upper() + ' ' + str(entry["user__last_name"]).upper()
            weekly_daily_tpv = weekly_amount / week_date_age
            weekly_weekly_tpv = weekly_daily_tpv
            weekly_monthly_tpv = weekly_daily_tpv * 4
            weekly_total_volume = weekly_amount
            weekly_gross_net = weekly_commission
            weekly_net_agent_monthly = entry_monthly_gross_net
            weekly_net_agent_daily = weekly_net_agent_monthly / week_date_age
            weekly_avg_transaction = weekly_daily_tpv / weekly_count
            weekly_count_per_day = weekly_count
            weekly_percentage_gross_net = weekly_total_volume * 0.25 / 100
            # Target of 40k in a montth, divided by 4 for weekly. (expect - actual /100)
            weekly_per_transaction = weekly_gross_net / week_date_age

            if report_format == "csv":
                ws2.append([
                    agent_name, weekly_daily_tpv, weekly_weekly_tpv, weekly_monthly_tpv, weekly_total_volume, weekly_gross_net,
                    weekly_net_agent_monthly, weekly_net_agent_daily, weekly_count_per_day, weekly_avg_transaction, weekly_percentage_gross_net,
                    weekly_per_transaction, "Active", week_date_age, agent_date_joined_age, agent_first_trans_age
                ])
            else:
                weekly_json = {
                    "agent_name": agent_name,
                    "daily_tpv": weekly_daily_tpv,
                    "weekly_tpv": weekly_weekly_tpv,
                    "monthly_tpv": weekly_monthly_tpv,
                    "total_volume": weekly_total_volume,
                    "gross_net": weekly_gross_net,
                    "net_agent_month": weekly_net_agent_monthly,
                    "net_agent_day": weekly_net_agent_daily,
                    "count_trans_day": weekly_count_per_day,
                    "avg_trans": weekly_avg_transaction,
                    "percent_gross_net": weekly_percentage_gross_net,
                    "actual_per_trans": weekly_per_transaction,
                    "activity_status": 'Active',
                    "report_days": week_date_age,
                    "merchant_age": agent_date_joined_age,
                    "trans_age": agent_first_trans_age
                }
                weekly_list.append(weekly_json)

        json_data["weekly_report"] = weekly_list

        # Get start and end dates for weekly and monthly transactions
        new_month_date = [dt for dt in rrule(DAILY, dtstart=month_start.date(), until=current_date.date()) if dt.weekday() not in [6]]
        month_date_age = len(new_month_date)

        monthly_data = Transaction.objects.filter(
            date_created__range=[month_start, month_end], user__in=agents, status="SUCCESSFUL"
        ).exclude(transaction_type__in=exclude_trans).values("user__id", "user__first_name", "user__last_name").annotate(
            total_count=Count("id"),
            total_amount=Sum("amount", output_field=FloatField()),
            commission=Sum("liberty_commission", output_field=FloatField())
        )

        for entry in monthly_data:
            agent = User.objects.get(id=entry["user__id"])
            query = Q(user=agent, status="SUCCESSFUL")
            first_trans = Transaction.objects.filter(query).exclude(transaction_type__in=exclude_trans).order_by("date_created")

            agent_first_trans_age = sum(1 for dt in rrule(
                DAILY, dtstart=first_trans.first().date_created.date(), until=current_date.date()) if dt.weekday() not in [6]) if first_trans else 0
            agent_date_joined_age = sum(
                1 for dt in rrule(DAILY, dtstart=agent.date_joined.date(), until=current_date.date()) if dt.weekday() not in [6]
            )

            monthly_amount = entry.get("total_amount") or 0
            monthly_count = entry.get("total_count") or 0
            monthly_commission = entry.get("commission") or 0

            agent_name = str(entry["user__first_name"]).upper() + ' ' + str(entry["user__last_name"]).upper()
            monthly_daily_tpv = monthly_amount / month_date_age
            monthly_weekly_tpv = monthly_daily_tpv * 7
            monthly_monthly_tpv = monthly_weekly_tpv * 4
            monthly_total_volume = monthly_amount
            monthly_gross_net = monthly_commission
            monthly_net_agent_monthly = monthly_gross_net
            monthly_net_agent_daily = monthly_net_agent_monthly / month_date_age
            monthly_avg_transaction = monthly_daily_tpv / monthly_count
            monthly_count_per_day = monthly_count
            monthly_percentage_gross_net = monthly_total_volume * 0.25 / 100
            monthly_per_transaction = monthly_gross_net / month_date_age

            if report_format == "csv":
                ws3.append([
                    agent_name, monthly_daily_tpv, monthly_weekly_tpv, monthly_monthly_tpv, monthly_total_volume, monthly_gross_net,
                    monthly_net_agent_monthly, monthly_net_agent_daily, monthly_count_per_day, monthly_avg_transaction, monthly_percentage_gross_net,
                    monthly_per_transaction, "Active", month_date_age, agent_date_joined_age, agent_first_trans_age
                ])
            else:
                monthly_json = {
                    "agent_name": agent_name,
                    "daily_tpv": monthly_daily_tpv,
                    "weekly_tpv": monthly_weekly_tpv,
                    "monthly_tpv": monthly_monthly_tpv,
                    "total_volume": monthly_total_volume,
                    "gross_net": monthly_gross_net,
                    "net_agent_month": monthly_net_agent_monthly,
                    "net_agent_day": monthly_net_agent_daily,
                    "count_trans_day": monthly_count_per_day,
                    "avg_trans": monthly_avg_transaction,
                    "percent_gross_net": monthly_percentage_gross_net,
                    "actual_per_trans": monthly_per_transaction,
                    "activity_status": 'Active',
                    "report_days": month_date_age,
                    "merchant_age": agent_date_joined_age,
                    "trans_age": agent_first_trans_age
                }
                monthly_list.append(monthly_json)

        json_data["monthly_report"] = monthly_list

        if report_format == "csv":
            # Save Excel file to memory
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            response = HttpResponse(output, content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            response["Content-Disposition"] = f'attachment; filename="Agent_Revenue_Monitoring_{current_date.date()}.xlsx"'
        else:
            json_response.append(json_data)
            response = Response(json_response)
        return response


class MerchantDashboardStatsAPIView(APIView):
    permission_classes = []

    def get(self, request):
        from django.db.models.functions import TruncMonth, TruncWeek, TruncDay
        # current_year = datetime.now()
        current_date = datetime.now()
        week_start, week_end = get_week_start_and_end_datetime(current_date)
        month_start, month_end = get_month_start_and_end_datetime(current_date)

        exclude_trans = [
            "ELECTRONIC_TRANSFER_LEVY", "SEND_LIBERTY_COMMISSION", "REVERSAL_BANK_TRANSFER_IN", "REVERSAL_BANK_TRANSFER",
            "FUND_TRANSFER_FROM_COMMISSION", "AJO_LOAN_COMMISSIONS", "ASSURED_LOAN_COMMISSIONS", "FUND_TRANSFER_FROM_OTHER_COMMISSION",
            "BILLS_AND_PAYMENT_REVERSAL", "BANK_OOB_IN", "BANK_OOB_OUT", "BANK_OOB_COMM", "RE_FAILED_TRANSFER_IN"
        ]

        role = "ADMIN"
        if request.user and request.user.is_authenticated:
            if request.user.email in ["<EMAIL>", "<EMAIL>"]:
                role = "ADMIN"
            else:
                role = str(request.user.type_of_user).upper()

        agent_query = Q(is_paybox_merchant=True, type_of_user="MERCHANT")

        if role == "STAFF_AGENT":
            referral_code = request.user.referral_code
            agents = User.objects.filter(agent_query, referer_code=referral_code)
        elif role == "MERCHANT":
            agents = User.objects.filter(id=request.user.id)
        else:
            agents = User.objects.filter(agent_query)

        revenue_by_year = (
            Transaction.objects.filter(user__in=agents, status="SUCCESSFUL").exclude(transaction_type__in=exclude_trans).annotate(
                month=TruncMonth("date_created")).values("month").annotate(total_revenue=Sum("amount")).annotate(total_count=Count("id")).order_by(
                "month")
        )

        revenue_by_month = (
            Transaction.objects.filter(
                user__in=agents, status="SUCCESSFUL", date_created__range=[month_start, month_end]).exclude(
                transaction_type__in=exclude_trans).annotate(week=TruncWeek("date_created")).values("week").annotate(
                total_revenue=Sum("amount")).annotate(total_count=Count("id")).order_by("week")
            )

        revenue_by_week = (
            Transaction.objects.filter(user__in=agents, status="SUCCESSFUL", date_created__range=[week_start, week_end]).exclude(
                transaction_type__in=exclude_trans).annotate(day=TruncDay("date_created")).values("day").annotate(
                total_revenue=Sum("amount")).annotate(total_count=Count("id")).order_by("day")
        )

        transactions_chart = list()

        this_year = [{
            "month": yearly.get("month").strftime("%b"),
            "count": yearly.get("total_count") or 0,
            "revenue": yearly.get("total_revenue") or 0
        } for yearly in revenue_by_year]

        this_month = [{
            "week": weekly.get("week").strftime("%d %b"),
            "count": weekly.get("total_count") or 0,
            "revenue": weekly.get("total_revenue") or 0
        } for weekly in revenue_by_month]

        this_week = [{
            "day": daily.get("day").strftime("%d"),
            "count": daily.get("total_count") or 0,
            "revenue": daily.get("total_revenue") or 0
        } for daily in revenue_by_week]

        transactions_chart.append({
            "this_year": this_year,
            "this_month": this_month,
            "this_week": this_week
        })

        # Top 5 Merchants by Volume & Count
        top_merchants = (
            Transaction.objects.filter(user__in=agents, status="SUCCESSFUL").exclude(transaction_type__in=exclude_trans).values("user_id").annotate(
                count=Count("id"), volume=Sum("amount")).order_by("-volume")[:5]
        )

        top_merchants_data = [
            {
                "name": User.objects.get(id=m["user_id"]).full_name,
                "count": m["count"],
                "volume": float(m['volume']),
                "loan_eligible": False if float(m['volume']) < 100000 else True,
                "eligible_amount": float(m['volume']) / 2,
            }
            for m in top_merchants
        ]

        return Response({
            "transactions_chart": transactions_chart,
            "top_merchants": top_merchants_data,
        })


class TransactionDisputeAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]

    @extend_schema(request=TransactionDisputeCreateSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = TransactionDisputeCreateSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class TransactionDisputeListAPIView(generics.ListAPIView):
    permission_classes = [CustomIsAuthenticated]
    pagination_class = CustomPagination
    serializer_class = TransactionDisputeSerializerOut

    def get_queryset(self):
        user = self.request.user
        return TransactionDispute.objects.filter(transaction__user=user)



N8N_WEBHOOK_URL = "https://libertyassured.app.n8n.cloud/webhook/transaction-dispute"


class SendDisputesToN8NView(APIView):
    """
    Endpoint that batches unresolved disputes and pushes them to n8n.
    To be called by an external cron job.
    """

    def post(self, request):
        disputes = TransactionDispute.objects.filter(resolved=False).order_by("-date_created")[:20]

        if not disputes.exists():
            return Response({"message": "No disputes to send"}, status=status.HTTP_200_OK)

        payload = []
        for dispute in disputes:
            payload.append({
                "id": dispute.id,
                "reason": dispute.reason,
                "customer_name": dispute.customer_name,
                "customer_email": dispute.user_email,
                "customer_account_no": dispute.customer_account_no,
                "customer_phone_no": dispute.customer_phone_no,
                "customer_bank": dispute.customer_bank.name if dispute.customer_bank else None,
                "transaction_id": dispute.transaction_id,
                "rrn": dispute.rrn,
                "resolved": dispute.resolved,
                "date_created": dispute.date_created.isoformat(),
            })

        try:
            res = requests.post(N8N_WEBHOOK_URL, json={"disputes": payload}, timeout=10)
            res.raise_for_status()
        except requests.RequestException as e:
            return Response({"error": f"Failed to send disputes batch: {e}"}, status=status.HTTP_502_BAD_GATEWAY)

        return Response({"message": f"Sent {len(payload)} disputes to n8n"}, status=status.HTTP_200_OK)