from django.conf import settings
from django.db.models import Q, Sum


from main.models import ConstantTable, AvailableBalan<PERSON>, UserFlag, BVNWatchlist, User

from accounts.helpers.vfdbank_manager import VFDBank
from accounts.helpers.helper_func import notify_admin_on_customer_trans_count_limit, notify_admin_group
from accounts.models import Transaction, WalletSystem, sending_list

from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta
from pytz import utc


import json
import requests


#################################################################################


def select_active_account_from_pool_of_single_provider(queryset):
    """
    This function takes in a set_all() of a related manager and returns the active account of a particular provider
    """

    for account in queryset:
        if account.is_active == True:
            active_account = account
            return active_account
        else:
            return None


def choose_account_provider_on_save(self, provider_related_object):
    """
    This function takes in a user instance ande a set_all() of a related manager and save the user instance with a selected active account provider details
    """
    get_provider_account = select_active_account_from_pool_of_single_provider(
        provider_related_object
    )
    if get_provider_account:
        self.accountNumber = get_provider_account.accountNumber
        self.accountName = get_provider_account.accountName
        self.bankName = get_provider_account.bankName
    else:
        self.accountNumber = None
        self.accountName = None
        self.bankName = None


def fetch_account_name(account_number, bank_code):
    """ "
    This function triggers a provider(Paystack) to fetch accunt name
    """

    url = f"https://api.paystack.co/bank/resolve?account_number={account_number}&bank_code={bank_code}"

    headers = {"Authorization": f"Bearer {settings.PAYSTACK_API_SECRET}"}

    try:
        response = requests.request("GET", url=url, headers=headers, timeout=5)
        return json.loads(response.text)

    except:
        return {
            "status": False,
            "message": "could not fetch account name",
            "data": []
        }



def fetch_account_name_second_option(account_number, bank_code):
    """ "
    This function calls VFD functionroi get triggers a provider to fetch accunt name
    """

    if settings.ENVIRONMENT == "production":

        if bank_code == "999999":
            transfer_type = "intra"
        else:
            transfer_type = "inter"

        vfd_instance = VFDBank.vfd_get_transfer_recipient(transfer_type, account_number, bank_code)
        if vfd_instance.get("status") != "00":
            result = False
            message = "Could not resolve account name. Check parameters or try again."
            data = None
        else:
            result = True
            message = "Account number resolved",
            data = {
                "account_name": vfd_instance["data"]["name"],
                "account_number": vfd_instance["data"]["account"]["number"]
            }

        response = {
            "status": result,
            "message": message,
            "data": data
        }

    else:
        response = {
            "status": True,
            "message":"Account number resolved",
            "data": {
                "account_name": "NWAOMA CHUKS",
                "account_number": "**********"
            }
        }


    return response


def fetch_lotto_agent_user(phone_number):
    """
    """

    if settings.ENVIRONMENT == "production":
        url = f"https://libertydraw.com/agent/api/agent_enquiry/{phone_number}/"


        try:
            response = requests.request("GET", url=url)
            resp = response.json()

            if resp["status"] == "success":
                new_resp = {
                    "status": True,
                    "message": "Agent Found",
                    "data": {
                        "account_name": resp.get("data").get("name"),
                        "phone_number": resp.get("data").get("phone"),
                    }

                }

            else:
                new_resp = {
                    "status": False,
                    "message": "could not fetch account name",
                    "data": []
                }

        except requests.exceptions.RequestException as e:
            new_resp = {
                "status": False,
                "message": "could not fetch account name",
                "data": []
            }

    else:
        new_resp = {
            "status": True,
            "message": "Agent Found",
            "data": {
                "account_name": "NWAOMA CHUKS",
                "phone_number": "**********"
            }
        }

    # print(new_resp)
    return new_resp



def detect_duplicate_transactions(user, transaction_type, amount, beneficiary=None):
    if transaction_type == "SEND_BANK_TRANSFER":
        last_transaction = user.escrows.filter(transfer_type=transaction_type, to_nuban=beneficiary, amount=amount).last()
    elif transaction_type == "SEND_BUDDY":
        last_transaction = user.escrows.filter(transfer_type=transaction_type, to_nuban=beneficiary, amount=amount).last()
    elif transaction_type in ["AIRTIME_PIN", "BILLS_AND_PAYMENT"]:
        last_transaction = Transaction.objects.filter(user=user, transaction_type=transaction_type, amount=amount).last()
    else:
        last_transaction = user.escrows.filter(transfer_type=transaction_type, to_nuban=beneficiary, amount=amount).last()


    if last_transaction:
        last_transaction_time = last_transaction.date_created.replace(tzinfo=utc) + timedelta(hours=1)
        current_time = datetime.now().replace(tzinfo=utc)

        time_check_range = timedelta(minutes=5)

        if (current_time - last_transaction_time) < time_check_range:
            return True
        else:
            return False
    else:
        return False


def calculate_transaction_limit(user, request_data=None, amount_to_be_removed=None):
    # required_transaction_types = sending_list
    to_be_removed = ["BILLS_AND_PAYMENT", "AIRTIME_PIN"]
    constant_instance = ConstantTable.get_constant_table_instance()

    required_transaction_types = [item for item in sending_list if item not in to_be_removed]

    today = datetime.today()  # date representing today's date

    from accounts.models import TransactionLimitLog

    # Check user account age
    account_age_days = TransactionLimitLog.check_user_account_age(user)
    if user.bypass_new_user_limit or user.type_of_user in ["MERCHANT", "AGENT", "LOTTO_AGENT", "STAFF_AGENT"]:
        account_age_days = 60

    new_user_7_days_limit = constant_instance.new_user_seven_days_limit
    new_user_40_days_limit = constant_instance.new_user_forty_days_limit

    # Get transactions for today
    qs = Transaction.objects.filter(
        user=user, date_created__date=today, transaction_type__in=required_transaction_types, transaction_leg="EXTERNAL", status="SUCCESSFUL",
        is_reversed=False
    )

    if qs:
        amount_transfered_today = sum(abs(item.amount) for item in qs)
    else:
        amount_transfered_today = 0.00

    exclude_enforce_global_limit = constant_instance.exclude_enforce_global_limit
    daily_trans_count = constant_instance.kyc_one_daily_trans_count
    daily_limit = constant_instance.kyc_one_daily_transfer_limit

    if constant_instance.enforce_global_limit and user.email not in exclude_enforce_global_limit:
        added_trans_limit = 0
    else:
        added_trans_limit = user.added_trans_limit

    # Determine daily limit based on account age first, then KYC level
    if account_age_days <= 7:
        daily_limit = new_user_7_days_limit
        limit_reason = 'NEW_USER_7DAYS'
    elif account_age_days <= 40:
        daily_limit = new_user_40_days_limit
        limit_reason = 'NEW_USER_40DAYS'
    else:
        # After 40 days: normal KYC-based limits
        limit_reason = 'NORMAL_LIMIT'
        if user.kyc_level == 1:
            daily_limit = constant_instance.kyc_one_daily_transfer_limit
            daily_trans_count = constant_instance.kyc_one_daily_trans_count
        if user.kyc_level == 2:
            daily_limit = constant_instance.kyc_two_daily_transfer_limit + added_trans_limit
            daily_trans_count = constant_instance.kyc_two_daily_trans_count + user.added_daily_trans_count
        if user.kyc_level == 3:
            daily_limit = constant_instance.kyc_three_daily_transfer_limit + added_trans_limit
            daily_trans_count = constant_instance.kyc_three_daily_trans_count + user.added_daily_trans_count

    last_hour = datetime.now() - timedelta(hours=1)
    last_hour_trans = qs.filter(date_created__gte=last_hour)
    last_hour_trans_count = last_hour_trans.count()

    amount_left_to_transfer = daily_limit - amount_transfered_today

    request_amount_to_be_transfered = 0
    if request_data is not None:
        if request_data.get("data") is not None:
            request_amount_to_be_transfered = sum(abs(item["amount"]) for item in request_data.get("data"))
        else:
            request_amount_to_be_transfered = abs(request_data.get("total_pin_amount")) if request_data.get("total_pin_amount") is not None else abs(request_data.get("amount"))

        if request_amount_to_be_transfered > amount_left_to_transfer:
            allow_status = False
        else:
            allow_status = True

    elif amount_to_be_removed is not None:
        request_amount_to_be_transfered = amount_to_be_removed
        if amount_to_be_removed > amount_left_to_transfer:
            allow_status = False
        else:
            allow_status = True
    else:
        allow_status = False

    if amount_left_to_transfer <= 0:
        limit_reached = True
    else:
        limit_reached = False

    if last_hour_trans_count > daily_trans_count:
        count_reached = True
    else:
        count_reached = False

    if user.email in exclude_enforce_global_limit:
        limit_reached = False
        allow_status = True
        count_reached = False

    status = 'ALLOWED' if allow_status and not limit_reached and not count_reached else 'BLOCKED'

    # Log the transaction limit check
    if request_amount_to_be_transfered > 0 and status != "ALLOWED":
        TransactionLimitLog.log_transaction_limit_check(
            user=user,
            amount=request_amount_to_be_transfered,
            action_type='LIMIT_CHECK',
            reason=limit_reason,
            status=status,
            account_age_days=account_age_days,
            details=f"Daily limit: {daily_limit}, Amount transferred today: {amount_transfered_today}, Amount left: {amount_left_to_transfer}"
        )

    # Generate response based on checks
    if limit_reached:
        api_response = {
            "status": "error",
            "error": "350",
            "message": f"You have reached your transfer limit for today.",
        }

    elif allow_status is False:
        api_response = {
            "status": "error",
            "error": "309",
            "message": f"Your transfer limit for today is {amount_left_to_transfer}",
        }

    elif count_reached is True:
        details = f"ALERTTTTTTTTTTT!!\n\nALERTTTTTTTTTTT!!\nUser with email: {user.email} has reached their hourly transfer count. Please reach out to them."

        if settings.ENVIRONMENT == "development":
            admin_list = ["*************"]
            for num in admin_list:
                notify_admin_on_customer_trans_count_limit(user=user, phone_number=num)
        else:
            notify_admin_group(user=user, details=details)

        api_response = {
            "status": "error",
            "error": "319",
            "message": f"You have reached your transfer count for the hour. Please contact support to increase your limit",
        }

    elif user.bvn_number and BVNWatchlist.is_bvn_watchlisted(user.bvn_number):
        User.suspend_user(user, "BVN Number is watchlisted")
        TransactionLimitLog.log_transaction_limit_check(
            user=user,
            amount=request_amount_to_be_transfered,
            action_type='LIMIT_CHECK',
            reason=limit_reason,
            status="BLOCKED",
            account_age_days=account_age_days,
            details=f"BVN number is on the watchlist"
        )

        api_response = {
            "status": "error",
            "error": "320",
            "message": f"BVN number is on the watchlist, please contact admin",
        }

    else:
        api_response = None

    return api_response


def get_false_float_balance_function(get_balance, last_balance, exclude_users_list):

    ngv_amt_to_look_out = ******** # Overdraft Amount

    if get_balance is None:
        new_balance = ngv_amt_to_look_out - abs(last_balance.float_balance) if last_balance else 0
        watch_balance = new_balance

        get_balance = last_balance.float_balance if last_balance else 0

    elif get_balance < 0:
        new_balance = ngv_amt_to_look_out - abs(get_balance)

        get_exclude_users = WalletSystem.objects.filter(wallet_type="COLLECTION") \
            .filter(Q(user__type_of_user="LOTTO_AGENT") | Q(user__email__in=exclude_users_list))

        get_exclude_users_balance = get_exclude_users.aggregate(Sum("available_balance"))["available_balance__sum"]

        watch_balance = new_balance - (get_exclude_users_balance + 500000)

        print("get_exclude_users_balance", get_exclude_users_balance)

    else:
        new_balance = get_balance
        watch_balance = new_balance





    print(":::::::::::::::::::::")
    print("watch_balance", watch_balance)
    print(":::::::::::::::::::::")

    print("original_balance", get_balance)
    print("new_balance", new_balance)

    return watch_balance, get_balance


def v2_get_false_float_balance_function(float_balance: float, exclude_users_list):
    overdraft_boundary = ConstantTable.get_constant_table_instance().overdraft_bench
    safe_amount = 50000
    get_exclude_users = WalletSystem.objects.filter(wallet_type="COLLECTION") \
        .filter(Q(user__type_of_user="LOTTO_AGENT") | Q(user__email__in=exclude_users_list))
    get_exclude_users_balance = get_exclude_users.aggregate(Sum("available_balance"))["available_balance__sum"] or 0

    print("overdraft_boundary", overdraft_boundary)
    print("get_exclude_users_balance", get_exclude_users_balance)
    new_balance = (overdraft_boundary + abs(float_balance)) - (get_exclude_users_balance + safe_amount)

    print("new_balance", new_balance)
    return new_balance
