import time
import re

import requests
from django_celery_results.models import TaskResult
from django.utils import timezone
from celery import shared_task
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.template import Template, Context
from main.helper.logging_utils import log_info, log_error, log_warning
from main.helper.send_emails import send_email
from main.models import User, Users_Otp, ConstantTable
from main.helper.helper_function import freshworks_register_contact, add_agents_whatsapp_group_func
from accounts.models import Transaction
from kyc_app.models import KYCTable, BVNDetail, DocumentFaceMatchKYC2Detail
from singlelogin.models import BlackListedJWT




@shared_task
def confirm_passcode_mail(email, passcode):
    send_email(email, passcode)
    return f"confirm email passcode sent to {email}"



@shared_task
def create_kyc_object_on_user_create_task(user_id):
    user = User.objects.filter(id=user_id).first()
    user_kyc, created = KYCTable.objects.get_or_create(user=user)
    bvn_rel, created = BVNDetail.objects.get_or_create(kyc=user_kyc)
    docs_rel, created = DocumentFaceMatchKYC2Detail.objects.get_or_create(kyc=user_kyc)

    # kyc_instance = KYCTable.objects.create(user=user)
    # .objects.create(kyc=kyc)
    # .objects.create(kyc=kyc)
    return f"KYC Object has been created for user with id {user_id}"


@shared_task
def send_user_data_to_freshworks_task(user_id):
    instance = User.objects.filter(id=user_id).last()

    register_contact_task = freshworks_register_contact(instance)

    return f"Data sent to freshworks"


# @shared_task
def send_whisper_otp_to_email_task(user_id, otp):
    user = User.objects.filter(id=user_id).first()
    send_email(email=user.email, passcode=otp)
    return f"otp sent to email {user.email}"


@shared_task
def send_bvn_otp_task(formatted_bvn_phone_number, app_name):
    Users_Otp.send_new_otp(phone_number=formatted_bvn_phone_number, app_name=app_name)
    return f"otp sent to bvn number {formatted_bvn_phone_number}"



@shared_task
def daily_terminal_login_count_reset():
    User.objects.all().update(daily_terminal_login_count=0)

@shared_task
def weekly_terminal_login_count_reset_and_activity_check():
    pass
    # users = User.objects.filter(terminal_id__isnull=False)
    # if len(users) > 0:
    #     for user in users:
    #         if user.weekly_terminal_login_count < 4:
    #             # suspend terminal
    #             user.terminal_suspended = True
    #             stock = Stock.objects.get(terminal_id=user.terminal_id)
    #             stock.status = 'INACTIVE'
    #             stock.save()
    #         else:
    #             d_now = datetime.now()
    #             d_prev = d_now - timedelta(days=7)
    #             now = self.timezone.localize(d_now)
    #             prev = self.timezone.localize(d_prev)

    #             transaction_count = Transaction.objects.filter(terminal_id=user.terminal_id).filter(date_created__lte=now).filter(
    #                 date_created__gte=prev).count()
    #             if transaction_count < 13:
    #                 user.terminal_suspended = True
    #                 stock = Stock.objects.get(terminal_id=user.terminal_id)
    #                 stock.status = 'INACTIVE'
    #                 stock.save()

    # User.objects.filter(terminal_id__isnull=False).update(weekly_terminal_login_count=0)


@shared_task
def monthly_terminal_login_count_reset():
    User.objects.all().update(monthly_terminal_login_count=0)


# celery.conf.beat_schedule = {
#     'daily terminal login count reset': {
#         'task': 'main.tasks.daily_terminal_login_count_reset',
#         'schedule': crontab(hour=0, minute=0),
#     },
# }

# celery.conf.beat_schedule = {
#     'weekly terminal login count reset and activity check': {
#         'task': 'main.tasks.weekly_terminal_login_count_reset_and_activity_check',
#         'schedule': crontab(hour=7, minute=30, day_of_week='sunday'),
#     },
# }

# celery.conf.beat_schedule = {
#     'monthly terminal login count reset': {
#         'task': 'main.tasks.monthly_terminal_login_count_reset',
#         'schedule': crontab(0, 0, day_of_month='3'),
#     },
# }


@shared_task
def add_agents_whatsapp_group_task(phone_number):
    
    add_agents_whatsapp_group_func(phone_number)
    return "DONE"


@shared_task
def delete_old_celery_tasks():

    cutoff_date = timezone.now() - timezone.timedelta(days=100)

    old_instances = TaskResult.objects.filter(date_done__lt=cutoff_date).order_by("id")[:3500]
    
    for old_instance in old_instances:
        old_instance.delete()

    return "DONE"


@shared_task
def delete_old_logins():
    
    cutoff_date = timezone.now() - timezone.timedelta(days=100)

    old_instances = BlackListedJWT.objects.filter(date_created__lt=cutoff_date).order_by("id")[:500]
    
    for old_instance in old_instances:
        old_instance.delete()

    return "DONE"


# @shared_task
# def start_trans_band_regulator(status):
#     try:
#         stat_num = int(status)
#     except:
#         stat_num = None

#     if stat_num == 1:
#         const = ConstantTable.get_constant_table_instance()
#         const.trans_band_regulator = True
#         const.save()

#         for user in User.objects.all():
#             user.save()

#     elif stat_num == 0:
#         for user in User.objects.all():
#             user.trans_band = 1
#             user.save()

#     return "DONE"


@shared_task(bind=True, max_retries=3)
def send_bulk_email_task(self, campaign_id):
    """
    Celery task to send bulk emails for a campaign.

    Args:
        campaign_id: ID of the BulkEmailCampaign to process

    Returns:
        dict: Summary of the email sending results
    """
    from main.models import BulkEmailCampaign, BulkEmailDelivery

    try:
        # Get the campaign
        campaign = BulkEmailCampaign.objects.get(id=campaign_id)

        log_info(
            f"Starting bulk email campaign: {campaign.subject}",
            "BULK_EMAIL_START",
            {
                'campaign_id': campaign_id,
                'sender': campaign.sender,
                'selection_type': campaign.selection_type,
                'user_type_filter': campaign.user_type_filter
            }
        )

        # Mark campaign as sending
        campaign.mark_as_sending()

        # Get target recipients
        recipients = campaign.get_target_recipients()
        campaign.total_recipients = recipients.count()
        campaign.save(update_fields=['total_recipients'])

        log_info(
            f"Found {campaign.total_recipients} recipients for campaign",
            "BULK_EMAIL_RECIPIENTS",
            {'campaign_id': campaign_id, 'recipient_count': campaign.total_recipients}
        )

        # Process each recipient
        sent_count = 0
        failed_count = 0

        for recipient in recipients:
            try:
                # Create or get delivery record
                delivery, created = BulkEmailDelivery.objects.get_or_create(
                    campaign=campaign,
                    recipient=recipient,
                    defaults={
                        'recipient_email': recipient.email,
                        'status': BulkEmailDelivery.PENDING
                    }
                )

                # Skip if already sent
                if delivery.status == BulkEmailDelivery.SENT:
                    sent_count += 1
                    continue

                # Personalize email content
                personalized_subject, personalized_content = _personalize_email_content(
                    campaign.subject, campaign.content, recipient
                )

                # Send the email
                success = _send_individual_email(
                    recipient.email,
                    personalized_subject,
                    personalized_content,
                    campaign.sender
                )

                if success:
                    delivery.status = BulkEmailDelivery.SENT
                    delivery.sent_at = timezone.now()
                    delivery.personalized_subject = personalized_subject
                    delivery.personalized_content = personalized_content
                    sent_count += 1

                    log_info(
                        f"Email sent successfully to {recipient.email}",
                        "BULK_EMAIL_SENT",
                        {'campaign_id': campaign_id, 'recipient_email': recipient.email}
                    )
                else:
                    delivery.status = BulkEmailDelivery.FAILED
                    delivery.error_message = "Failed to send email"
                    failed_count += 1

                    log_warning(
                        f"Failed to send email to {recipient.email}",
                        "BULK_EMAIL_FAILED",
                        {'campaign_id': campaign_id, 'recipient_email': recipient.email}
                    )

                delivery.save()

                # Update campaign counters periodically
                if (sent_count + failed_count) % 10 == 0:
                    campaign.sent_count = sent_count
                    campaign.failed_count = failed_count
                    campaign.save(update_fields=['sent_count', 'failed_count'])

                # Rate limiting - small delay between emails
                time.sleep(0.1)

            except Exception as e:
                failed_count += 1
                log_error(
                    f"Error processing recipient {recipient.email}: {str(e)}",
                    "BULK_EMAIL_RECIPIENT_ERROR",
                    {
                        'campaign_id': campaign_id,
                        'recipient_email': recipient.email,
                        'error': str(e)
                    },
                    include_traceback=True
                )

                # Create failed delivery record
                BulkEmailDelivery.objects.update_or_create(
                    campaign=campaign,
                    recipient=recipient,
                    defaults={
                        'recipient_email': recipient.email,
                        'status': BulkEmailDelivery.FAILED,
                        'error_message': str(e)
                    }
                )

        # Update final campaign status
        campaign.sent_count = sent_count
        campaign.failed_count = failed_count

        if failed_count == 0:
            campaign.mark_as_completed()
        elif sent_count == 0:
            campaign.mark_as_failed("All emails failed to send")
        else:
            campaign.mark_as_completed()  # Partial success is still completion

        result = {
            'campaign_id': campaign_id,
            'total_recipients': campaign.total_recipients,
            'sent_count': sent_count,
            'failed_count': failed_count,
            'success_rate': campaign.success_rate
        }

        log_info(
            f"Bulk email campaign completed: {sent_count}/{campaign.total_recipients} sent",
            "BULK_EMAIL_COMPLETE",
            result
        )

        return result

    except BulkEmailCampaign.DoesNotExist:
        error_msg = f"Campaign with ID {campaign_id} not found"
        log_error(error_msg, "BULK_EMAIL_CAMPAIGN_NOT_FOUND")
        raise Exception(error_msg)

    except Exception as e:
        log_error(
            f"Bulk email campaign failed: {str(e)}",
            "BULK_EMAIL_TASK_ERROR",
            {'campaign_id': campaign_id},
            include_traceback=True
        )

        # Mark campaign as failed if it exists
        try:
            campaign = BulkEmailCampaign.objects.get(id=campaign_id)
            campaign.mark_as_failed(str(e))
        except:
            pass

        # Retry the task if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            log_info(
                f"Retrying bulk email task (attempt {self.request.retries + 1})",
                "BULK_EMAIL_RETRY",
                {'campaign_id': campaign_id}
            )
            raise self.retry(countdown=60 * (self.request.retries + 1))

        raise e


def _personalize_email_content(subject_template, content_template, user):
    """
    Personalize email content with user-specific data.

    Args:
        subject_template: Email subject template with merge fields
        content_template: Email content template with merge fields
        user: User object for personalization

    Returns:
        tuple: (personalized_subject, personalized_content)
    """
    try:
        # Create context with user data
        context = Context({
            'user': user,
            'first_name': user.first_name or 'Valued Customer',
            'last_name': user.last_name or '',
            'full_name': user.full_name if hasattr(user, 'full_name') else f"{user.first_name} {user.last_name}",
            'email': user.email,
            'phone_number': getattr(user, 'phone_number', ''),
            'type_of_user': getattr(user, 'type_of_user', ''),
        })

        # Personalize subject
        subject_template_obj = Template(subject_template)
        personalized_subject = subject_template_obj.render(context)

        # Personalize content
        content_template_obj = Template(content_template)
        personalized_content = content_template_obj.render(context)

        return personalized_subject, personalized_content

    except Exception as e:
        log_error(
            f"Error personalizing email content for user {user.email}: {str(e)}",
            "EMAIL_PERSONALIZATION_ERROR",
            include_traceback=True
        )
        # Return original content if personalization fails
        return subject_template, content_template


def _send_individual_email(recipient_email, subject, content, sender_email):
    """
    Send an individual email.

    Args:
        recipient_email: Recipient's email address
        subject: Email subject
        content: Email content (HTML)
        sender_email: Sender's email address

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    try:
        # Validate email address format
        if not _is_valid_email(recipient_email):
            log_warning(
                f"Invalid email address: {recipient_email}",
                "INVALID_EMAIL_ADDRESS"
            )
            return False

        # Send email using Django's email backend
        send_mail(
            subject=subject,
            message=content,  # Plain text version
            html_message=content,  # HTML version
            from_email=sender_email,
            recipient_list=[recipient_email],
            fail_silently=False
        )

        mail_gun_url = "https://api.mailgun.net/v3/libertypayng.com/messages"
        mail_gun_auth = ("api", settings.MAILGUN_API_KEY)
        mail_gun_data = {
            "from": sender_email,
            "to": recipient_email,
            "subject": subject,
            "html": content
        }
        try:
            res = requests.post(mail_gun_url, auth=mail_gun_auth, data=mail_gun_data)
            log_info(f"Sending Campaign email to: {recipient_email}\n{res.text}", "EMAIL_SEND_SUCCESS")
        except Exception as e:
            log_error(f"Failed to send email to {recipient_email}: {str(e)}", "EMAIL_SEND_ERROR")
            pass

        return True

    except Exception as e:
        log_error(
            f"Failed to send email to {recipient_email}: {str(e)}",
            "EMAIL_SEND_ERROR",
            {'recipient': recipient_email, 'error': str(e)},
            include_traceback=True
        )
        return False


def _is_valid_email(email):
    """
    Validate email address format.

    Args:
        email: Email address to validate

    Returns:
        bool: True if email is valid, False otherwise
    """
    if not email:
        return False

    # Basic email validation regex
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(email_pattern, email) is not None


@shared_task(bind=True, max_retries=3)
def evaluate_merchant_rewards_daily_task(self):
    """
    Celery task to evaluate merchant daily rewards.
    This task should be scheduled to run daily at 12:01 AM.

    Returns:
        dict: Summary of the evaluation results
    """
    from django.core.management import call_command
    from django.utils import timezone
    from io import StringIO

    try:
        evaluation_date = timezone.now().date() - timezone.timedelta(days=1)

        log_info(
            f"Starting daily merchant reward evaluation for {evaluation_date}",
            "MERCHANT_REWARD_DAILY_TASK_START",
            {'evaluation_date': str(evaluation_date)}
        )

        # Capture command output
        output = StringIO()

        # Run the management command
        call_command(
            'evaluate_merchant_rewards_task',
            stdout=output,
            stderr=output
        )

        command_output = output.getvalue()

        log_info(
            f"Daily merchant reward evaluation completed successfully",
            "MERCHANT_REWARD_DAILY_TASK_COMPLETE",
            {
                'evaluation_date': str(evaluation_date),
                'command_output': command_output[:1000]  # Truncate long output
            }
        )

        return {
            'status': 'success',
            'evaluation_date': str(evaluation_date),
            'output': command_output
        }

    except Exception as e:
        log_error(
            f"Daily merchant reward evaluation failed: {str(e)}",
            "MERCHANT_REWARD_DAILY_TASK_ERROR",
            {'evaluation_date': str(evaluation_date) if 'evaluation_date' in locals() else 'unknown'},
            include_traceback=True
        )

        # Retry the task if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            log_info(
                f"Retrying merchant reward evaluation (attempt {self.request.retries + 1})",
                "MERCHANT_REWARD_DAILY_TASK_RETRY"
            )
            raise self.retry(countdown=300)  # Retry after 5 minutes

        return {
            'status': 'failed',
            'error': str(e),
            'evaluation_date': str(evaluation_date) if 'evaluation_date' in locals() else 'unknown'
        }


@shared_task
def expire_merchant_free_transactions_task():
    """
    Celery task to expire unused merchant free transactions.
    This should run daily to clean up expired rewards.

    Returns:
        dict: Summary of expired transactions
    """
    from main.models import MerchantDailyReward
    from django.utils import timezone

    try:
        log_info(
            "Starting merchant free transaction expiry task",
            "MERCHANT_REWARD_EXPIRY_TASK_START"
        )

        # Get all rewards with remaining free transactions
        rewards_with_remaining = MerchantDailyReward.objects.filter(
            free_transactions_remaining__gt=0
        ).select_related('merchant', 'merchant__reward_system')

        total_expired = 0
        merchants_affected = 0

        for reward in rewards_with_remaining:
            expired_count = reward.expire_unused_transactions()
            if expired_count > 0:
                total_expired += expired_count
                merchants_affected += 1

        result = {
            'status': 'success',
            'total_expired': total_expired,
            'merchants_affected': merchants_affected,
            'date': str(timezone.now().date())
        }

        log_info(
            f"Merchant free transaction expiry completed: {total_expired} transactions expired for {merchants_affected} merchants",
            "MERCHANT_REWARD_EXPIRY_TASK_COMPLETE",
            result
        )

        return result

    except Exception as e:
        log_error(
            f"Merchant free transaction expiry task failed: {str(e)}",
            "MERCHANT_REWARD_EXPIRY_TASK_ERROR",
            include_traceback=True
        )

        return {
            'status': 'failed',
            'error': str(e),
            'date': str(timezone.now().date())
        }
