import pandas as pd
from django.contrib import admin, messages
from django.contrib.admin.widgets import FilteredSelectMultiple
from django_celery_results.models import TaskResult
from django import forms

from import_export import resources
from import_export.admin import ImportExportMixin, ImportExportModelAdmin

from main.models import *
from main.tasks import send_bulk_email_task
from django.contrib.admin.models import LogEntry, DELETION
from django.utils.html import escape
from django.urls import reverse
from django.utils.safestring import mark_safe
from rest_framework.authtoken.models import Token
from .models import IssueLog
from django.http import HttpResponseRedirect
from django.shortcuts import render
from django.urls import path
from main.helper.logging_utils import log_info


class TaskResultAdmin(admin.ModelAdmin):
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        search_kwargs = request.GET.get('task_kwargs', None)
        if search_kwargs:
            queryset = queryset.filter(task_kwargs__contains=search_kwargs)
        return queryset

# admin.site.register(TaskResult, TaskResultAdmin)

@admin.register(LogEntry)
class LogEntryAdmin(admin.ModelAdmin):
    date_hierarchy = 'action_time'

    list_filter = [
        # 'user',
        'content_type',
        'action_flag'
    ]

    search_fields = [
        'user__email',
        'object_repr',
        'change_message'
    ]

    list_display = [
        'action_time',
        'user',
        'change_message',
        'content_type',
        # 'object_link',
        'action_flag',
    ]


    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser

    # def object_link(self, obj):
    #     if obj.action_flag == DELETION:
    #         link = escape(obj.object_repr)
    #     else:
    #         # ct = obj.content_type
    #         # link = '<a href="%s">%s</a>' % (
    #         #     reverse('admin:%s_%s_change' % (ct.app_label, ct.model), args=[obj.object_id]),
    #         #     escape(obj.object_repr),
    #         # )
    #         pass
    #     return mark_safe(link)
    # object_link.admin_order_field = "object_repr"
    # object_link.short_description = "object"



###############################################################################
# RESOURCES

class RegistrationDataResource(resources.ModelResource):
    class Meta:
        model = RegistrationData

class UserResource(resources.ModelResource):
    class Meta:
        model = User

class ConstantResource(resources.ModelResource):
    class Meta:
        model = ConstantTable


class ChargeBandResource(resources.ModelResource):
    class Meta:
        model = ChargeBand


class AgentProfileResource(resources.ModelResource):
    class Meta:
        model = AgentProfile


class UnregisteredPhoneNumbersResource(resources.ModelResource):
    class Meta:
        model = UnregisteredPhoneNumber

class WhitelistResource(resources.ModelResource):
    class Meta:
        model = Whitelist

class ResetPinStorageResource(resources.ModelResource):
    class Meta:
        model = ResetPinStorage

class SMSRecordResource(resources.ModelResource):
    class Meta:
        model = SMSRecord

class UnsentSMSResource(resources.ModelResource):
    class Meta:
        model = UnsentSMS

class OtherServiceDetailResource(resources.ModelResource):
    class Meta:
        model = OtherServiceDetail

class TrackUserClickResource(resources.ModelResource):
    class Meta:
        model = TrackUserClick

class CallbackSystemResource(resources.ModelResource):
    class Meta:
        model = CallbackSystem

class SuperAgentProfileResource(resources.ModelResource):
    class Meta:
        model = SuperAgentProfile

class UserFormattedResource(resources.ModelResource):
    class Meta:
        model = UserFormatted

class AvailableBalanceResource(resources.ModelResource):
    class Meta:
        model = AvailableBalance

class DeliveryAddressDataResource(resources.ModelResource):
    class Meta:
        model = DeliveryAddressData

class UserFlagResource(resources.ModelResource):
    class Meta:
        model = UserFlag

class BlacklistResource(resources.ModelResource):
    class Meta:
        model = Blacklist

class UserWhitelistResource(resources.ModelResource):
    class Meta:
        model = UserWhitelist

class AgentSupervisorResource(resources.ModelResource):
    class Meta:
        model = AgentSupervisor

class UserOtherAccountResource(resources.ModelResource):
    class Meta:
        model = UserOtherAccount

class CorporateAccountResource(resources.ModelResource):
    class Meta:
        model = CorporateAccount

class UserTempTokenResource(resources.ModelResource):
    class Meta:
        model = UserTempToken

class DisplayBannerResource(resources.ModelResource):
    class Meta:
        model = DisplayBanner


class DataDeletionResource(resources.ModelResource):
    class Meta:
        model = UserDataDeletionRequest

class AjoAgentFormResource(resources.ModelResource):
    class Meta:
        model = AjoAgentForm

class TerminalRetrievalRequestResource(resources.ModelResource):
    class Meta:
        model = TerminalRetrievalRequest



class NewLocationListResource(resources.ModelResource):
    class Meta:
        model = NewLocationList

class BranchTeamResource(resources.ModelResource):
    class Meta:
        model = BranchTeam

class UserTeamResource(resources.ModelResource):
    class Meta:
        model = UserTeam

#######################################################################
# ADMINS



class RegistrationDataResourceAdmin(ImportExportModelAdmin):
    resource_class = RegistrationDataResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserAdmin(ImportExportModelAdmin):

    def suspend_terminals(self, request, queryset):
        for user in queryset:
            User.suspend_user(
                user=user,
                reason="Suspended By Super Admin. Do not unsuspend without instruction"
            )


        self.message_user(request, "Successfully Suspended Terminals")

    suspend_terminals.short_description = "Suspend terminals"


    def save_model(self, request, obj, form, change):
        custom_request = request
        obj.save(custom_request=custom_request)



    def get_readonly_fields(self, request, obj=None):
        if obj and obj.is_fraud: # editing an existing object
            return self.readonly_fields + ['is_fraud', 'send_money_status', 'is_suspended', 'terminal_suspended', 'mobile_suspended', 'mobile_disabled', 'terminal_disabled']
        return self.readonly_fields

    readonly_fields = [
        "terminal_provider",  "unique_id", "customer_id", "password", "lotto_win_toggle",
        "registration_email_otp", "date_of_consent", "date_assigned", "sales_rep_full_name", "sales_rep_comm_balance_daily",
        "sales_rep_comm_balance", "bills_pay_comm_balance_daily", "bills_pay_comm_balance", "other_comm_balance_daily",
        "other_comm_balance", "kyc_level", "kyc_one_progress", "kyc_two_progress", "wallet_balance"
    ]

    exclude = ('registration_email_otp', 'password')

    resource_class = UserResource
    search_fields = ['email', 'phone_number', 'terminal_id', "first_name", "last_name", "bvn_number", "sales_rep_upline_code", "username"]
    list_filter = (
        ('date_joined', "type_of_user", "kyc_two_progress", "send_money_status", "sms_subscription", "kyc_level", "has_transaction_pin", "terminal_suspended", "is_suspended", "is_fraud", "reactivation_request")
    )
    date_hierarchy = 'date_joined'
    actions = [suspend_terminals]


    def get_list_display(self, request):
        return [field.name if field.name not in ["bvn_number"] else "phone_number" for field in self.model._meta.concrete_fields]


    # def save_model(self, request, obj, form, change):
    #     if change:
    #         obj.save(update_fields=form.changed_data)
    #     else:
    #         super().save_model(request, obj, form, change)


class ConstantResourceAdmin(ImportExportModelAdmin):
    resource_class = ConstantResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ChargeBandResourceAdmin(ImportExportModelAdmin):
    resource_class = ChargeBandResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentProfileResourceAdmin(ImportExportModelAdmin):
    search_fields = ['email', 'phone_number']
    resource_class = AgentProfileResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UnregisteredPhoneNumbersResourceAdmin(ImportExportModelAdmin):
    resource_class = UnregisteredPhoneNumbersResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class WhitelistResourceAdmin(ImportExportModelAdmin):
    resource_class = WhitelistResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class ResetPinStorageResourceAdmin(ImportExportModelAdmin):
    resource_class = ResetPinStorageResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class SMSRecordResourceAdmin(ImportExportModelAdmin):
    resource_class = SMSRecordResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UnsentSMSResourceAdmin(ImportExportModelAdmin):
    resource_class = UnsentSMSResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class OtherServiceDetailResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = OtherServiceDetailResource
    search_fields = ['user__email', 'service_name', 'wallet_id']


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class TrackUserClickResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = TrackUserClickResource
    search_fields = ['user__email', 'view_or_screen']
    list_filter = (
        ('date_added', "view_or_screen")
    )


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class CallbackSystemResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = CallbackSystemResource
    search_fields = ['user__email', 'url']
    list_filter = (
        ('date_added', "transaction_type", "other_transaction_type")
    )


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class SuperAgentProfileResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['agent', 'super_agent', 'supervisor', 'team_lead']
    resource_class = SuperAgentProfileResource
    actions = ["resave_super_agent_profile"]
    search_fields = ['team_lead__email', 'supervisor__email', 'agent__email', 'super_agent__email']
    # list_filter = (
    #     ('date_added', "transaction_type", "other_transaction_type")
    # )

    def resave_super_agent_profile(self, request, queryset):
        for agent in queryset:
            agent.save()
            self.message_user(request, f"SuperAgentProfile saved {agent.id}", level=messages.SUCCESS)

    resave_super_agent_profile.short_description = "RE-SAVE SuperAgentProfile FOR SELECTED ITEM(S)"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserFormattedResourceAdmin(ImportExportModelAdmin):
    readonly_fields = ["type_of_user"]
    resource_class = UserResource
    search_fields = ['email', 'phone_number', "first_name", "last_name"]
    list_filter = (
        ('date_joined', "type_of_user", "kyc_level")
    )
    date_hierarchy = 'date_joined'


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AvailableBalanceResourceAdmin(ImportExportModelAdmin):
    resource_class = AvailableBalanceResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class DeliveryAddressDataResourceAdmin(ImportExportModelAdmin):
    resource_class = DeliveryAddressDataResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserFlagResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = UserFlagResource
    search_fields = ['user__email']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


    def get_search_results(self, request, queryset, search_term):
        search_list = [term.strip() for term in search_term.split(",")]

        if len(search_list) == 1 and not search_list[0].strip():
            return queryset, True
        filtered_qs = queryset.filter(
                        user__email__in=search_list,
                        suspended=True,
                        ).distinct("user__email")
        log_info(str(len(search_list)))

        return filtered_qs, True


    def mass_unsuspend_terminals(self, request, queryset):
        for query in queryset:
            UserFlag.unsuspend_user(
                user=query.user,
                reason="Unsuspended by admin",
                request=request
            )
        self.message_user(request, "Suspension successfully removed from terminals")

    mass_unsuspend_terminals.add_description = "Manually unsuspend multiple terminals"
    actions = [mass_unsuspend_terminals]


class BlacklistResourceAdmin(ImportExportModelAdmin):
    resource_class = BlacklistResource
    search_fields = ['content']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserWhitelistResourceAdmin(ImportExportModelAdmin):
    resource_class = UserWhitelistResource
    search_fields = ['content']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class AgentSupervisorResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentSupervisorResource
    autocomplete_fields = ['supervisor', 'agents']
    search_fields = ['supervisor__email', 'agents__email']
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserOtherAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = UserOtherAccountResource
    autocomplete_fields = ['owner', 'other_account']
    search_fields = ['owner__email', 'other_account__email']
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CorporateAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = CorporateAccountResource
    autocomplete_fields = ['user', 'other_users']
    search_fields = ['user__email', 'rc_number', 'corporate_id']
    list_filter = (
        ('date_created', 'raw_incorp_date')
    )
    date_hierarchy = 'date_created'
    readonly_fields = ["corporate_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class TokenAdmin(admin.ModelAdmin):
    autocomplete_fields = ["user"]
    search_fields = ('user__email', 'key')

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserTempTokenResourceAdmin(admin.ModelAdmin):
    autocomplete_fields = ["user"]
    search_fields = ('token',)
    list_filter = (
        ('date_created', 'exp', 'pin_type')
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class DisplayBannerResourceAdmin(ImportExportModelAdmin):
    resource_class = DisplayBannerResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserDataDeletionRequestResourceAdmin(ImportExportModelAdmin):
    resource_class = DisplayBannerResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class AjoAgentFormResourceAdmin(ImportExportModelAdmin):
    search_fields = ["phone_number"]
    resource_class = AjoAgentFormResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class TerminalRetrievalRequestResourceAdmin(ImportExportModelAdmin):
    search_fields = ["phone_number", "user__email"]
    resource_class = TerminalRetrievalRequestResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NewLocationListResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["supervisor"]
    resource_class = NewLocationListResource
    search_fields = ["location", "sub_location"]

    list_filter = (
        ('date_created', 'location')
    )
    readonly_fields = ["created_by"]

    def save_model(self, request, obj, form, change):
        custom_request = request
        obj.save(custom_request=custom_request)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BranchTeamResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["supervisor", "branch"]
    search_fields = ["branch__location", "supervisor__email"]
    resource_class = BranchTeamResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserTeamResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["team", "users"]
    search_fields = ["team__branch__location", "users__email", "team__supervisor__email"]
    resource_class = UserTeamResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class IssueLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'description', 'date_created', 'date_updated')
    list_filter = ('date_created', 'date_updated')
    search_fields = ('user__email', 'user__username', 'description')
    readonly_fields = ('date_created', 'date_updated')
    raw_id_fields = ('user',)

    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Issue Details', {
            'fields': ('description',)
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


class MerchantAcquisitionOfficerForm(forms.ModelForm):
    class Meta:
        model = MerchantAcquisitionOfficer
        fields = '__all__'
        widgets = {
            'merchants': FilteredSelectMultiple("Merchants", is_stacked=False),
        }


class MerchantAcquisitionOfficerAdmin(admin.ModelAdmin):
    form = MerchantAcquisitionOfficerForm
    list_display = ['user_email', 'get_merchant_emails', 'date_created', 'date_updated']
    search_fields = ['user__email', 'merchants__email']
    filter_horizontal = ['merchants']
    autocomplete_fields = ['user']

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'Acquisition Officer'

    def get_merchant_emails(self, obj):
        return ", ".join([m.email for m in obj.merchants.all()])
    get_merchant_emails.short_description = 'Mapped Merchants'


class BVNWatchlistUploadForm(forms.Form):
    # Form for uploading BVN watchlist CSV/XLSX files

    file = forms.FileField(
        help_text="Upload CSV or XLSX file with BVN watchlist data. Required columns: BVN, REQUESTING BANK, FIRST NAME, MIDDLE NAME, SURNAME, CATEGORY, WATCHLIST DATE"
    )

    def clean_file(self):
        file = self.cleaned_data['file']

        if not file.name.lower().endswith(('.csv', '.xlsx', '.xls')):
            raise forms.ValidationError("Please upload a CSV or XLSX file.")

        if file.size > 10 * 1024 * 1024:
            raise forms.ValidationError("File size must be less than 10MB.")

        return file


class UserVersionTrackerAdmin(admin.ModelAdmin):
    list_display = ('email', 'user_version', 'backend_version', 'version_match_display', 'device_type', 'date_created', 'date_updated')
    list_filter = ('user_version', 'backend_version', 'device_type', 'date_updated')
    search_fields = ('email', 'user_version', 'backend_version', 'serial_no')
    readonly_fields = ('date_created', 'date_updated', 'version_match_display', 'is_outdated_display')
    date_hierarchy = 'date_updated'

    fieldsets = (
        ('User Information', {
            'fields': ('email', 'device_type', 'serial_no')
        }),
        ('Version Information', {
            'fields': ('user_version', 'backend_version', 'version_match_display', 'is_outdated_display')
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        })
    )

    def version_match_display(self, obj):
        return obj.version_match
    version_match_display.short_description = 'Version Match'
    version_match_display.boolean = True

    def is_outdated_display(self, obj):
        return obj.is_outdated
    is_outdated_display.short_description = 'User Version Outdated'
    is_outdated_display.boolean = True


class BVNWatchlistAdmin(admin.ModelAdmin):
    list_display = ('bvn', 'full_name_display', 'category', 'requesting_bank', 'date_watchlisted', 'date_created')
    list_filter = ('category', 'date_watchlisted', 'date_created', 'requesting_bank')
    search_fields = ('bvn', 'first_name', 'surname', 'other_name', 'requesting_bank')
    readonly_fields = ('date_created', 'date_updated')
    date_hierarchy = 'date_watchlisted'

    fieldsets = (
        ('BVN Information', {
            'fields': ('bvn', 'first_name', 'other_name', 'surname')
        }),
        ('Watchlist Details', {
            'fields': ('category', 'date_watchlisted', 'requesting_bank')
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        })
    )

    actions = ['upload_bvn_watchlist']

    def full_name_display(self, obj):
        return obj.full_name
    full_name_display.short_description = 'Full Name'
    full_name_display.admin_order_field = 'first_name'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('upload-bvn-watchlist/', self.admin_site.admin_view(self.upload_bvn_watchlist_view), name='upload_bvn_watchlist'),
        ]
        return custom_urls + urls

    def upload_bvn_watchlist(self, request, queryset):
        return HttpResponseRedirect(reverse('admin:upload_bvn_watchlist'))
    upload_bvn_watchlist.short_description = "Upload BVN Watchlist from CSV/XLSX"

    def upload_bvn_watchlist_view(self, request):
        if request.method == 'POST':
            form = BVNWatchlistUploadForm(request.POST, request.FILES)
            if form.is_valid():
                try:
                    file = form.cleaned_data['file']
                    result = self.process_uploaded_file(file)

                    messages.success(
                        request,
                        f"Successfully processed {result['total']} records. "
                        f"Created: {result['created']}, Updated: {result['updated']}, "
                        f"Errors: {result['errors']}"
                    )

                    if result['error_details']:
                        for error in result['error_details'][:10]:  # Show first 10 errors
                            messages.warning(request, f"Row {error['row']}: {error['message']}")

                        if len(result['error_details']) > 10:
                            messages.warning(request, f"... and {len(result['error_details']) - 10} more errors")

                    return HttpResponseRedirect(reverse('admin:main_bvnwatchlist_changelist'))

                except Exception as e:
                    messages.error(request, f"Error processing file: {str(e)}")
        else:
            form = BVNWatchlistUploadForm()

        context = {
            'form': form,
            'title': 'Upload BVN Watchlist',
            'opts': self.model._meta,
            'has_view_permission': self.has_view_permission(request),
        }

        return render(request, 'admin/bvn_watchlist_upload.html', context)

    def process_uploaded_file(self, file):
        result = {
            'total': 0,
            'created': 0,
            'updated': 0,
            'errors': 0,
            'error_details': []
        }

        try:
            # Read file based on extension
            if file.name.lower().endswith('.csv'):
                df = pd.read_csv(file)
            else:
                # For Excel files, try to read from different sheets
                try:
                    # First, try to read the first sheet
                    df = pd.read_excel(file, sheet_name=0)
                except Exception:
                    # If that fails, try to find a sheet with data
                    excel_file = pd.ExcelFile(file)
                    sheet_names = excel_file.sheet_names

                    df = None
                    for sheet_name in sheet_names:
                        try:
                            temp_df = pd.read_excel(file, sheet_name=sheet_name)
                            if not temp_df.empty and len(temp_df.columns) >= 6:  # At least 6 columns expected
                                df = temp_df
                                break
                        except Exception:
                            continue

                    if df is None:
                        raise ValueError("Could not find a valid sheet with data in the Excel file")

            # Check if dataframe is empty
            if df.empty:
                raise ValueError("The uploaded file appears to be empty")

            # Normalize column names (remove spaces, convert to uppercase)
            df.columns = df.columns.str.strip().str.upper().str.replace(' ', '_')

            # Also try common variations of column names
            column_variations = {
                'BVN': ['BVN', 'BVN_NUMBER', 'BANK_VERIFICATION_NUMBER'],
                'REQUESTING_BANK': ['REQUESTING_BANK', 'BANK', 'BANK_NAME', 'REQUESTING_BANK_NAME'],
                'FIRST_NAME': ['FIRST_NAME', 'FIRSTNAME', 'FNAME'],
                'MIDDLE_NAME': ['MIDDLE_NAME', 'MIDDLENAME', 'OTHER_NAME', 'OTHERNAME', 'MNAME'],
                'SURNAME': ['SURNAME', 'LAST_NAME', 'LASTNAME', 'LNAME'],
                'CATEGORY': ['CATEGORY', 'WATCHLIST_CATEGORY', 'TYPE'],
                'WATCHLIST_DATE': ['WATCHLIST_DATE', 'DATE_WATCHLISTED', 'DATE', 'WATCHLIST_DATE']
            }

            # Map actual columns to expected columns
            column_mapping = {}
            for expected_col, variations in column_variations.items():
                found = False
                for variation in variations:
                    if variation in df.columns:
                        column_mapping[variation] = expected_col
                        found = True
                        break
                if not found:
                    column_mapping[expected_col] = expected_col  # Use original if not found

            # Rename columns to standardized names
            reverse_mapping = {v: k for k, v in column_mapping.items()}
            df = df.rename(columns=reverse_mapping)

            # Final column mapping for processing
            final_column_mapping = {
                'BVN': 'bvn',
                'REQUESTING_BANK': 'requesting_bank',
                'FIRST_NAME': 'first_name',
                'MIDDLE_NAME': 'other_name',
                'SURNAME': 'surname',
                'CATEGORY': 'category',
                'WATCHLIST_DATE': 'date_watchlisted'
            }

            # Check if all required columns exist
            missing_columns = [col for col in final_column_mapping.keys() if col not in df.columns]
            if missing_columns:
                available_columns = ', '.join(df.columns.tolist())
                raise ValueError(
                    f"Missing required columns: {', '.join(missing_columns)}. "
                    f"Available columns in file: {available_columns}. "
                    f"Please ensure your file has the correct column headers."
                )

            result['total'] = len(df)

            for index, row in df.iterrows():
                try:
                    # Extract and clean data
                    bvn = str(row['BVN']).strip()
                    if len(bvn) != 11 or not bvn.isdigit():
                        result['errors'] += 1
                        result['error_details'].append({
                            'row': index + 2,
                            'message': f"Invalid BVN format: {bvn}"
                        })
                        continue

                    # Parse date
                    date_watchlisted = pd.to_datetime(row['WATCHLIST_DATE']).date()

                    # Map category
                    category = str(row['CATEGORY'])
                    category_choices = dict(BVNWatchlist.CATEGORY_CHOICES)
                    if category not in category_choices:
                        category = '1'

                    watchlist_entry, created = BVNWatchlist.objects.get_or_create(
                        bvn=bvn,
                        defaults={
                            'first_name': str(row['FIRST_NAME']).strip(),
                            'other_name': str(row['MIDDLE_NAME']).strip() if pd.notna(row['MIDDLE_NAME']) else '',
                            'surname': str(row['SURNAME']).strip(),
                            'category': category,
                            'date_watchlisted': date_watchlisted,
                            'requesting_bank': str(row['REQUESTING_BANK']).strip() if pd.notna(row['REQUESTING_BANK']) else '',
                        }
                    )

                    if created:
                        result['created'] += 1
                    else:
                        watchlist_entry.first_name = str(row['FIRST_NAME']).strip()
                        watchlist_entry.other_name = str(row['MIDDLE_NAME']).strip() if pd.notna(row['MIDDLE_NAME']) else ''
                        watchlist_entry.surname = str(row['SURNAME']).strip()
                        watchlist_entry.category = category
                        watchlist_entry.date_watchlisted = date_watchlisted
                        watchlist_entry.requesting_bank = str(row['REQUESTING_BANK']).strip() if pd.notna(row['REQUESTING_BANK']) else ''
                        watchlist_entry.save()
                        result['updated'] += 1

                except Exception as e:
                    result['errors'] += 1
                    result['error_details'].append({
                        'row': index + 2,
                        'message': str(e)
                    })

        except Exception as e:
            raise ValueError(f"Error reading file: {str(e)}")

        return result


admin.site.register(RegistrationData, RegistrationDataResourceAdmin)
admin.site.register(User, UserAdmin)
admin.site.register(IssueLog, IssueLogAdmin)
admin.site.register(ConstantTable, ConstantResourceAdmin)
admin.site.register(ChargeBand, ChargeBandResourceAdmin)
admin.site.register(AgentProfile, AgentProfileResourceAdmin)
admin.site.register(UnregisteredPhoneNumber, UnregisteredPhoneNumbersResourceAdmin)
admin.site.register(Whitelist, WhitelistResourceAdmin)
admin.site.register(ResetPinStorage, ResetPinStorageResourceAdmin)
admin.site.register(SMSRecord, SMSRecordResourceAdmin)
admin.site.register(UnsentSMS, UnsentSMSResourceAdmin)
admin.site.register(OtherServiceDetail, OtherServiceDetailResourceAdmin)
admin.site.register(TrackUserClick, TrackUserClickResourceAdmin)
admin.site.register(CallbackSystem, CallbackSystemResourceAdmin)
admin.site.register(SuperAgentProfile, SuperAgentProfileResourceAdmin)
admin.site.register(UserFormatted, UserFormattedResourceAdmin)
admin.site.register(AvailableBalance, AvailableBalanceResourceAdmin)
admin.site.register(DeliveryAddressData, DeliveryAddressDataResourceAdmin)
admin.site.register(UserFlag, UserFlagResourceAdmin)
admin.site.register(Blacklist, BlacklistResourceAdmin)
admin.site.register(UserWhitelist, UserWhitelistResourceAdmin)
admin.site.register(AgentSupervisor, AgentSupervisorResourceAdmin)
admin.site.register(UserOtherAccount, UserOtherAccountResourceAdmin)
admin.site.register(CorporateAccount, CorporateAccountResourceAdmin)
admin.site.register(Token, TokenAdmin)
admin.site.register(UserTempToken, UserTempTokenResourceAdmin)
admin.site.register(DisplayBanner, DisplayBannerResourceAdmin)
admin.site.register(UserDataDeletionRequest, UserDataDeletionRequestResourceAdmin)
admin.site.register(AjoAgentForm, AjoAgentFormResourceAdmin)
admin.site.register(TerminalRetrievalRequest, TerminalRetrievalRequestResourceAdmin)
admin.site.register(NewLocationList, NewLocationListResourceAdmin)
admin.site.register(BranchTeam, BranchTeamResourceAdmin)
admin.site.register(UserTeam, UserTeamResourceAdmin)
admin.site.register(MerchantAcquisitionOfficer, MerchantAcquisitionOfficerAdmin)
admin.site.register(ApplicationBanner)
admin.site.register(ApplicationBannerClick)
admin.site.register(BVNWatchlist, BVNWatchlistAdmin)
admin.site.register(UserVersionTracker, UserVersionTrackerAdmin)


# Bulk Email Campaign Admin Classes

class BulkEmailCampaignForm(forms.ModelForm):
    """Custom form for BulkEmailCampaign with enhanced validation and widgets."""

    class Meta:
        model = BulkEmailCampaign
        fields = '__all__'
        widgets = {
            'target_users': FilteredSelectMultiple("Target Users", is_stacked=False),
            'content': forms.Textarea(attrs={'rows': 10, 'cols': 80}),
            'subject': forms.TextInput(attrs={'size': 80}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add help text for content field
        self.fields['content'].help_text = (
            "HTML content supported. Use merge fields like {{user.first_name}}, "
            "{{user.email}}, {{user.full_name}} for personalization."
        )

        # Filter target_users to only show active users
        self.fields['target_users'].queryset = User.objects.filter(is_active=True).order_by('email')

    def clean_subject(self):
        subject = self.cleaned_data.get('subject')
        if not subject or not subject.strip():
            raise forms.ValidationError("Subject cannot be empty.")
        return subject.strip()

    def clean_content(self):
        content = self.cleaned_data.get('content')
        if not content or not content.strip():
            raise forms.ValidationError("Content cannot be empty.")
        return content.strip()

    def clean(self):
        cleaned_data = super().clean()
        selection_type = cleaned_data.get('selection_type')
        target_users = cleaned_data.get('target_users')
        user_type_filter = cleaned_data.get('user_type_filter')

        # Validate that either target_users or user_type_filter is provided for appropriate selection types
        if selection_type == BulkEmailCampaign.SELECTED_USERS:
            if not target_users or target_users.count() == 0:
                raise forms.ValidationError(
                    "You must select at least one target user when using 'Selected Users' option."
                )

        return cleaned_data


class BulkEmailDeliveryInline(admin.TabularInline):
    """Inline admin for viewing email deliveries within a campaign."""
    model = BulkEmailDelivery
    extra = 0
    readonly_fields = ['recipient', 'recipient_email', 'status', 'sent_at', 'error_message', 'retry_count']
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False


class BulkEmailCampaignAdmin(admin.ModelAdmin):
    """Admin interface for managing bulk email campaigns."""

    form = BulkEmailCampaignForm
    inlines = [BulkEmailDeliveryInline]

    list_display = [
        'subject', 'sender', 'status', 'selection_type', 'user_type_filter',
        'total_recipients', 'sent_count', 'failed_count', 'success_rate_display',
        'created_at', 'started_at', 'completed_at'
    ]

    list_filter = [
        'status', 'selection_type', 'user_type_filter', 'exclude_test_accounts',
        'created_at', 'started_at', 'completed_at'
    ]

    search_fields = ['subject', 'sender__email', 'sender__first_name', 'sender__last_name']

    readonly_fields = [
        'total_recipients', 'sent_count', 'failed_count', 'success_rate_display',
        'started_at', 'completed_at', 'created_at', 'updated_at'
    ]

    filter_horizontal = ['target_users']

    fieldsets = (
        ('Campaign Details', {
            'fields': ('subject', 'content', 'sender')
        }),
        ('Recipient Selection', {
            'fields': ('selection_type', 'user_type_filter', 'target_users', 'exclude_test_accounts'),
            'description': 'Choose how to select recipients for this campaign.'
        }),
        ('Scheduling', {
            'fields': ('scheduled_at',),
            'classes': ('collapse',),
            'description': 'Leave blank to send immediately when triggered.'
        }),
        ('Campaign Status', {
            'fields': ('status', 'total_recipients', 'sent_count', 'failed_count', 'success_rate_display'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'started_at', 'completed_at'),
            'classes': ('collapse',)
        })
    )

    actions = ['send_bulk_email', 'update_recipient_count', 'preview_recipients', 'reset_campaign']

    def get_queryset(self, request):
        return super().get_queryset(request)

    def success_rate_display(self, obj):
        """Display success rate as a percentage."""
        return f"{obj.success_rate:.1f}%"
    success_rate_display.short_description = 'Success Rate'
    success_rate_display.admin_order_field = 'sent_count'

    def get_readonly_fields(self, request, obj=None):
        """Make certain fields readonly based on campaign status."""
        readonly_fields = list(self.readonly_fields)

        if obj and not obj.is_editable:
            # Campaign is not editable, make most fields readonly
            readonly_fields.extend([
                'subject', 'content', 'selection_type', 'user_type_filter',
                'target_users', 'exclude_test_accounts', 'scheduled_at'
            ])

        return readonly_fields

    def send_bulk_email(self, request, queryset):
        """Admin action to send bulk emails."""
        sent_count = 0
        error_count = 0

        for campaign in queryset:
            if not campaign.can_be_sent:
                messages.error(
                    request,
                    f"Campaign '{campaign.subject}' cannot be sent. "
                    f"Status: {campaign.get_status_display()}"
                )
                error_count += 1
                continue

            try:
                # Update recipient count before sending
                campaign.update_recipient_count()

                if campaign.total_recipients == 0:
                    messages.warning(
                        request,
                        f"Campaign '{campaign.subject}' has no recipients. Please check your filters."
                    )
                    continue

                # Trigger the Celery task
                # send_bulk_email_task.delay(campaign.id)
                send_bulk_email_task.apply_async(
                    queue="send_campaign_email_task",
                    kwargs={
                        "campaign_id": str(campaign.id)
                    }
                )

                messages.success(
                    request,
                    f"Bulk email task started for campaign '{campaign.subject}' "
                    f"with {campaign.total_recipients} recipients."
                )

                log_info(
                    f"Bulk email campaign triggered from admin: {campaign.subject}",
                    "BULK_EMAIL_ADMIN_TRIGGER",
                    {
                        'campaign_id': campaign.id,
                        'admin_user': request.user.email,
                        'recipient_count': campaign.total_recipients
                    }
                )

                sent_count += 1

            except Exception as e:
                messages.error(
                    request,
                    f"Error starting campaign '{campaign.subject}': {str(e)}"
                )
                error_count += 1

        if sent_count > 0:
            messages.success(
                request,
                f"Successfully started {sent_count} email campaign(s)."
            )

    send_bulk_email.short_description = "Send selected bulk email campaigns"

    def update_recipient_count(self, request, queryset):
        """Admin action to update recipient counts."""
        for campaign in queryset:
            old_count = campaign.total_recipients
            campaign.update_recipient_count()

            messages.info(
                request,
                f"Campaign '{campaign.subject}': Recipients updated from {old_count} to {campaign.total_recipients}"
            )

    update_recipient_count.short_description = "Update recipient count for selected campaigns"

    def preview_recipients(self, request, queryset):
        """Admin action to preview recipients."""
        if queryset.count() != 1:
            messages.error(request, "Please select exactly one campaign to preview recipients.")
            return

        campaign = queryset.first()
        recipients = campaign.get_target_recipients()

        if recipients.count() == 0:
            messages.warning(request, f"Campaign '{campaign.subject}' has no recipients.")
        else:
            recipient_emails = list(recipients.values_list('email', flat=True)[:10])
            preview_text = ", ".join(recipient_emails)

            if recipients.count() > 10:
                preview_text += f" ... and {recipients.count() - 10} more"

            messages.info(
                request,
                f"Campaign '{campaign.subject}' will be sent to {recipients.count()} recipients: {preview_text}"
            )

    preview_recipients.short_description = "Preview recipients for selected campaign"

    def reset_campaign(self, request, queryset):
        """Admin action to reset campaign counters."""
        for campaign in queryset:
            if campaign.status in [BulkEmailCampaign.COMPLETED, BulkEmailCampaign.FAILED]:
                campaign.reset_counters()
                campaign.status = BulkEmailCampaign.DRAFT
                campaign.save(update_fields=['status'])

                messages.success(
                    request,
                    f"Campaign '{campaign.subject}' has been reset to draft status."
                )
            else:
                messages.warning(
                    request,
                    f"Campaign '{campaign.subject}' cannot be reset (current status: {campaign.get_status_display()})."
                )

    reset_campaign.short_description = "Reset selected campaigns to draft status"

    def save_model(self, request, obj, form, change):
        """Set the sender to the current user if not already set."""
        if not change:  # New object
            obj.sender = request.user

        super().save_model(request, obj, form, change)

        # Update recipient count after saving
        if obj.pk:
            obj.update_recipient_count()


class BulkEmailDeliveryAdmin(admin.ModelAdmin):
    """Admin interface for viewing email delivery details."""

    list_display = [
        'campaign_subject', 'recipient_email', 'status', 'sent_at',
        'retry_count', 'created_at'
    ]

    list_filter = [
        'status', 'sent_at', 'created_at', 'campaign__status'
    ]

    search_fields = [
        'recipient__email', 'recipient_email', 'campaign__subject',
        'error_message'
    ]

    readonly_fields = [
        'campaign', 'recipient', 'recipient_email', 'status', 'sent_at',
        'error_message', 'retry_count', 'personalized_subject',
        'personalized_content', 'created_at', 'updated_at'
    ]

    fieldsets = (
        ('Campaign Information', {
            'fields': ('campaign', 'recipient', 'recipient_email')
        }),
        ('Delivery Status', {
            'fields': ('status', 'sent_at', 'retry_count', 'error_message')
        }),
        ('Personalized Content', {
            'fields': ('personalized_subject', 'personalized_content'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def campaign_subject(self, obj):
        """Display the campaign subject."""
        return obj.campaign.subject
    campaign_subject.short_description = 'Campaign'
    campaign_subject.admin_order_field = 'campaign__subject'

    def has_add_permission(self, request):
        """Prevent manual creation of delivery records."""
        return False

    def has_delete_permission(self, request, obj=None):
        """Prevent deletion of delivery records for audit purposes."""
        return False


# Register the models
admin.site.register(BulkEmailCampaign, BulkEmailCampaignAdmin)
admin.site.register(BulkEmailDelivery, BulkEmailDeliveryAdmin)
