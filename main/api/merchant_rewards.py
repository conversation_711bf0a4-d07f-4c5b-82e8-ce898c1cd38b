"""
API endpoints for merchant reward system.
Provides endpoints for merchants to view their reward status and transaction targets.
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone
from datetime import timed<PERSON>ta

from main.helper.merchant_reward_utils import get_merchant_reward_status
from main.helper.logging_utils import log_info, log_warning, log_error
from main.models import MerchantRewardSystem, MerchantDailyReward


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def merchant_reward_status(request):
    """
    Get the current reward status for the authenticated merchant.
    
    Returns:
        - Reward system configuration
        - Current day performance
        - Available free transactions
        - Recent reward history
    """
    try:
        user = request.user
        
        # Check if user is a merchant
        if user.type_of_user != 'MERCHANT':
            return Response({
                'success': False,
                'message': 'This endpoint is only available for merchants',
                'error_code': 'NOT_MERCHANT'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Get comprehensive reward status
        reward_status = get_merchant_reward_status(user)
        
        log_info(
            f"Merchant reward status requested by {user.email}",
            "MERCHANT_REWARD_STATUS_API",
            {'merchant_email': user.email}
        )
        
        return Response({
            'success': True,
            'data': reward_status
        }, status=status.HTTP_200_OK)
    
    except Exception as e:
        log_error(
            f"Error getting merchant reward status for {request.user.email}: {str(e)}",
            "MERCHANT_REWARD_STATUS_API_ERROR",
            {'merchant_email': request.user.email},
            include_traceback=True
        )
        
        return Response({
            'success': False,
            'message': 'An error occurred while fetching reward status',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def merchant_reward_history(request):
    """
    Get detailed reward history for the authenticated merchant.
    
    Query Parameters:
        - days: Number of days to look back (default: 30, max: 90)
        - page: Page number for pagination (default: 1)
        - page_size: Number of records per page (default: 20, max: 100)
    """
    try:
        user = request.user
        
        # Check if user is a merchant
        if user.type_of_user != 'MERCHANT':
            return Response({
                'success': False,
                'message': 'This endpoint is only available for merchants',
                'error_code': 'NOT_MERCHANT'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Get query parameters
        days = min(int(request.GET.get('days', 30)), 90)
        page = max(int(request.GET.get('page', 1)), 1)
        page_size = min(max(int(request.GET.get('page_size', 20)), 1), 100)
        
        # Calculate date range
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # Get reward history
        rewards_queryset = MerchantDailyReward.objects.filter(
            merchant=user,
            evaluation_date__gte=start_date,
            evaluation_date__lte=end_date
        ).order_by('-evaluation_date')
        
        # Pagination
        total_records = rewards_queryset.count()
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        rewards = rewards_queryset[start_index:end_index]
        
        # Format reward data
        reward_data = []
        for reward in rewards:
            reward_data.append({
                'evaluation_date': reward.evaluation_date,
                'transactions_completed': reward.transactions_completed,
                'target_transactions': reward.target_transactions,
                'target_met': reward.target_met,
                'success_rate': reward.success_rate,
                'free_transactions_awarded': reward.free_transactions_awarded,
                'free_transactions_remaining': reward.free_transactions_remaining,
                'total_transaction_amount': reward.total_transaction_amount,
                'average_transaction_amount': reward.average_transaction_amount,
                'minimum_amount': reward.minimum_amount,
                'evaluation_completed_at': reward.evaluation_completed_at,
                'is_expired': reward.is_expired
            })
        
        # Calculate summary statistics
        summary_stats = {
            'total_days_evaluated': total_records,
            'targets_met': rewards_queryset.filter(target_met=True).count(),
            'total_free_transactions_earned': sum(r.free_transactions_awarded for r in rewards_queryset),
            'total_free_transactions_remaining': sum(r.free_transactions_remaining for r in rewards_queryset),
            'average_success_rate': 0,
            'best_day': None,
            'current_streak': 0
        }
        
        if total_records > 0:
            success_rates = [r.success_rate for r in rewards_queryset]
            summary_stats['average_success_rate'] = sum(success_rates) / len(success_rates)
            
            # Find best performing day
            best_reward = max(rewards_queryset, key=lambda r: r.success_rate)
            summary_stats['best_day'] = {
                'date': best_reward.evaluation_date,
                'transactions_completed': best_reward.transactions_completed,
                'success_rate': best_reward.success_rate
            }
            
            # Calculate current streak of meeting targets
            current_streak = 0
            for reward in rewards_queryset.order_by('-evaluation_date'):
                if reward.target_met:
                    current_streak += 1
                else:
                    break
            summary_stats['current_streak'] = current_streak
        
        # Pagination info
        pagination = {
            'current_page': page,
            'page_size': page_size,
            'total_records': total_records,
            'total_pages': (total_records + page_size - 1) // page_size,
            'has_next': end_index < total_records,
            'has_previous': page > 1
        }
        
        log_info(
            f"Merchant reward history requested by {user.email}",
            "MERCHANT_REWARD_HISTORY_API",
            {
                'merchant_email': user.email,
                'days': days,
                'page': page,
                'total_records': total_records
            }
        )
        
        return Response({
            'success': True,
            'data': {
                'rewards': reward_data,
                'summary': summary_stats,
                'pagination': pagination,
                'date_range': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'days': days
                }
            }
        }, status=status.HTTP_200_OK)
    
    except ValueError as e:
        return Response({
            'success': False,
            'message': 'Invalid query parameters',
            'error_code': 'INVALID_PARAMETERS'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    except Exception as e:
        log_error(
            f"Error getting merchant reward history for {request.user.email}: {str(e)}",
            "MERCHANT_REWARD_HISTORY_API_ERROR",
            {'merchant_email': request.user.email},
            include_traceback=True
        )
        
        return Response({
            'success': False,
            'message': 'An error occurred while fetching reward history',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def merchant_current_performance(request):
    """
    Get real-time performance data for the current day.
    
    Returns:
        - Today's transaction count
        - Target progress
        - Estimated time to reach target
        - Available free transactions
    """
    try:
        user = request.user
        
        # Check if user is a merchant
        if user.type_of_user != 'MERCHANT':
            return Response({
                'success': False,
                'message': 'This endpoint is only available for merchants',
                'error_code': 'NOT_MERCHANT'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Check if merchant has reward system
        try:
            reward_system = user.reward_system
        except MerchantRewardSystem.DoesNotExist:
            return Response({
                'success': False,
                'message': 'No reward system configured for this merchant',
                'error_code': 'NO_REWARD_SYSTEM'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get current performance
        performance = reward_system.current_performance
        
        # Calculate additional metrics
        now = timezone.now()
        hours_elapsed = now.hour + (now.minute / 60.0)
        hours_remaining = 24 - hours_elapsed
        
        # Estimate transactions per hour needed to reach target
        transactions_needed = max(0, performance['target'] - performance['transactions_today'])
        estimated_hourly_rate = transactions_needed / hours_remaining if hours_remaining > 0 else 0
        
        # Get available free transactions
        available_free = reward_system.available_free_transactions
        
        response_data = {
            'current_performance': performance,
            'reward_system': {
                'target_transactions_per_day': reward_system.target_transactions_per_day,
                'reward_free_transactions': reward_system.reward_free_transactions,
                'minimum_transaction_amount': reward_system.minimum_transaction_amount,
                'is_active': reward_system.is_active
            },
            'available_free_transactions': available_free,
            'time_analysis': {
                'hours_elapsed_today': round(hours_elapsed, 2),
                'hours_remaining_today': round(hours_remaining, 2),
                'transactions_needed': transactions_needed,
                'estimated_hourly_rate_needed': round(estimated_hourly_rate, 2)
            },
            'status_message': self._get_performance_message(performance, transactions_needed, hours_remaining)
        }
        
        return Response({
            'success': True,
            'data': response_data
        }, status=status.HTTP_200_OK)
    
    except Exception as e:
        log_error(
            f"Error getting current performance for {request.user.email}: {str(e)}",
            "MERCHANT_CURRENT_PERFORMANCE_API_ERROR",
            {'merchant_email': request.user.email},
            include_traceback=True
        )
        
        return Response({
            'success': False,
            'message': 'An error occurred while fetching current performance',
            'error_code': 'INTERNAL_ERROR'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def _get_performance_message(performance, transactions_needed, hours_remaining):
    """Generate a helpful status message based on current performance."""
    if performance['target_met']:
        return "🎉 Congratulations! You've met your daily target and earned free transactions!"
    
    if transactions_needed == 0:
        return "🎯 Target achieved! Great work today!"
    
    if hours_remaining <= 1:
        return f"⏰ Less than 1 hour remaining. You need {transactions_needed} more transactions to reach your target."
    
    hourly_rate = transactions_needed / hours_remaining
    if hourly_rate <= 1:
        return f"📈 You're on track! Need {transactions_needed} more transactions in {hours_remaining:.1f} hours."
    elif hourly_rate <= 3:
        return f"⚡ Pick up the pace! Need about {hourly_rate:.1f} transactions per hour to reach your target."
    else:
        return f"🚀 Challenge mode! Need {hourly_rate:.1f} transactions per hour. You can do it!"
