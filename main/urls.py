from django.urls import path, include
from main import views

# URLCOnf

sign_up_urls = [
    path("user/get_user_types/", views.GetTypeOfUser.as_view()),
    path("user/filter_pos_user/", views.GetUserWithPhoneNumber.as_view()),
    path("user/create_user_detail/", views.CreateUserDetailAPIView.as_view()),
    path(
        "user/confirm_registration_email/",
        views.ConfirmEmailPinOnRegistration.as_view(),
    ),
    path("user/update_email_on_sign_up/", views.UpdateEmailAPIView.as_view()),
    path("user/verify_otp_update_email/",views.VerifyOTPUpdateEmail.as_view()),
    path("user/create_login_passcode/", views.CreateLoginPasscodeAPIView.as_view()),
    path("user/create_login_passcode_and_activate_email/", views.CreateLoginPasscodeAndActivateEmailAPIView.as_view()),
    path("user/get_user_with_phone/", views.CheckPhoneNumberAPIView.as_view()),
]

cloud_messaging = [
    path("user/push/", views.CloudNotification.as_view()),
    path("user/firebase/", views.GetUserTokenFirebase.as_view()),
    path("admin/notify_app/", views.SetNotifyAppTokenFBKAPIView.as_view()),
    path("admin/ping_devices/", views.SetupPingDevicesAPIView.as_view()),
]

resend_urls = [
    path("user/resend_registration_pin/", views.ResendRegistrationPinAPIView.as_view()),
    path("user/reset_transactional_pin/", views.ResetTransactionPinAPIView.as_view()),
    path("user/first_reset_login_pin/", views.FirstResetPasswordAPIView.as_view()),
    path("user/second_reset_confirm_login_pin/", views.ConfirmLoginOTPResetPinAPIView.as_view()),
    path("user/third_reset_create_login_pin/", views.ResetLoginPasscodeAPIView.as_view()),
    path("user/reset_login_pin_in_app/", views.InAppResetLoginPasscodeInAppAPIView.as_view()),
]

user_urls = [
    path("user/get_user_details/", views.UserDetailAPIView.as_view()),
    path("user/update-profile/", views.UpdateUserProfileAPIView.as_view()),
    path("user/verify_password/", views.VerifyPassword.as_view()),
    path("user/unsubscribe_sms_notification/", views.UnsubscribeSMS.as_view()),
    path("user/update/username", views.UpdateUsernameView.as_view()),
    path("delivery_address_details/", views.AllDeliveryAddressAPIView.as_view()),
    path("user/create_user_other_accounts/", views.CreateOtherAccountAPIView.as_view()),
    path("user/update_profile_pic/", views.UpdateUserProfilePictureAPIView.as_view()),
    # path("user/update-unique-id", views.ReturnUniqueIDForLotto.as_view()),
    path("user/verify-lotto-agent", views.AgentSupervisorVerificationAPIView.as_view()),
    path("user/lotto-agent-supervisors", views.ListLottoSupervisorsAPIView.as_view()),
]

transaction_pin_urls = [
    path("user/create_transaction_pin/", views.CreateTransactionPINAPIView.as_view()),
    path(
        "user/verify_transaction_pin/", views.SimpleCheckTransactionPinAPIView.as_view()
    ),
    path("user/update_transaction_pin", views.TransactionPinUpdateView.as_view()),
]

constant_table_ulrs = [
    path("view_constant_variable/", views.ConstantsAPIView.as_view()),
    path("get_security_questions/", views.SecurityQuestionsAPIView.as_view()),
    path("get_security_questions/v2/", views.SecurityQuestionsAPIViewV2.as_view()),
]

kyc_urls = [
    path("user/check_kyc/", views.CheckKYCLevelView.as_view()),
]

loans_urls = [
    path("loans/all_user_details/", views.GetAgentsWithTransactionsAPIView.as_view()),
    path("loans/all_transactions_list/", views.GetLoansTransactionHistoryAPIView.as_view()),
]

merchant_pin = [
    path("user/merchant_pin/first_request/", views.MerchantPinVerifyOTPAPIView.as_view()),
    path("user/merchant_pin/confirm_otp/", views.MerchantPinConfirmOTPAPIView.as_view()),
    path("user/merchant_pin/create_pin/", views.MerchantPinCreateAPIView.as_view()),
    path("user/merchant_pin/toggle/", views.RemoveMerchantPin.as_view()),
    path("user/lotto_toggle_win/", views.ToggleLottoWinAPIView.as_view()),
]

others = [
    path("check_lotto_available/", views.LottoCheckAvailabilityAPIView.as_view()),
    path("add_num_to_whatsapp_group/", views.AddMultipleToWhatsappGroup.as_view()),
    path("get_all_phone_numbers/", views.GetAllPhoneNumbersAPIView.as_view()),
    path("get_type_of_user/", views.LottoGetUserTypeOfUserAPIView.as_view()),
    path("savings_get_user_pass/", views.SavingsGetUserHashedPass.as_view()),
    path("generate_super_token/", views.CreateSuperTokenAPIView.as_view()),
    path("onboard_corporate/", views.OnboardCorporateDetailAPIView.as_view()),
    path("get_user_branch_team/", views.GetUserSupervisorsandBranchAPIView.as_view()),
    path("agent-remap", views.RemapRegionBranchAgentAPIView.as_view()),
    path("acquisition-officers", views.GetMerchantAcquisitionOfficersAPIView.as_view()),
    path("check-watchlisted-bvn", views.CheckWatchListedBVNAPIView.as_view()),
    path("get-user-referral-code", views.GetUserByReferralCodeAPIView.as_view()),
    path("agent/request-supervisor-verification", views.ResendAgentVerificationTokenAPIView.as_view()),
    path("banners", views.ApplicationBannerListAPIView.as_view()),
    path("banners/<str:pk>", views.ApplicationBannerListAPIView.as_view()),
    path("agent-location", views.NewLocationListAPIView.as_view()),
    path("additional-limit-increase/", views.UserAdditionalLimitIncreaseAPIView.as_view()),
    path("check-transaction-limit/", views.CheckTransactionLimitAPIView.as_view()),
]

lotto_urls = [
    path("other_service_verify_trans_pin/", views.OtherServiceVerifyPinAPIView.as_view()),
    path("get_user_superagent/", views.LottoGetUserSuperAgentDetailsAPIView.as_view()),
    path("others_suspend_user/", views.LottoServiceSuspendUserAPIView.as_view()),
]

display_banner_urls = [
    path("create_display_banner", views.CreateDisplayBannerView.as_view()),
    path("display_banner_list", views.DisplayBannerListView.as_view()),
    path("viewed_display_banner_list/<banner_id>", views.ViewedDisplayBannersView.as_view())
]

settings_urls = [
    path("get_answer_user_security_questions/", views.GetAndAnswerSecurityQuestionsAPIView.as_view()),
    path("forgot_transaction_pin/", views.ForgotTransactionPinAPIView.as_view()),
    path("delete-user-data-request/", views.UserDataDeletionRequestView.as_view())
]

custom_paybox_onboard = [
    path("check_email_exist/", views.CheckUserEmailAPIView.as_view()),
    path("check_phone_number_exist/", views.CheckUserPhoneAPIView.as_view()),
    path("fetch_user_via_email/", views.FetchUserByEmailAPIView.as_view()),
    path("fetch_user_via_phone_number/", views.FetchUserByPhoneAPIView.as_view()),
    path("get_onboarding_status/", views.GetOnboardingStatusAPIView.as_view()),
    path("user/create_user_phone_detail/", views.CreateUserPhoneDetailAPIView.as_view()),
    path("user/phone_resend_registration_pin/", views.ResendRegistrationPhonePinAPIView.as_view()),
    path("user/confirm_registration_phone/", views.ConfirmPhonePinOnRegistration.as_view()),
]

profile_settings = [
    path("user/update_preference/", views.UpdatePreferenceAPIView.as_view()),
    path("user/update_personal_info/", views.UpdatePersonalInfoAPIView.as_view()),
    path("user/update_password/", views.UpdateUserPasswordAPIView.as_view()),
]

urlpatterns = [
    ##################################################################################
    # DARE ADDED THIS
    path(
        "user/user_update/<str:phone>/", views.add_user_personal_email_and_referral_code
    ),
    path("user/user_personal_info/<str:phone>/", views.update_user_personal_info),
    path("user/send_otp/", views.send_otp),
    path("user/recovery_phone/", views.validate_recovery_phone_no_to_passcode_reset),
    # END OF DARE
    #################################################################################

    path("whisper_call_back/", views.WhisperOTPCallBack.as_view()),
    path("agent_give_consent/", views.AgentGiveConsentAPIView.as_view()),
    path("user/verify_referal_code/", views.VerifyRepReferalCode.as_view()),
    path("user/get_ajo_otp/", views.GetUSSDOTPAPIView.as_view()),
    path("user/submit-ajo-form/", views.SendAjoFormAPIView.as_view()),
    path("savings-user-details/", views.GetSavingsUserDetailsView.as_view()),
    path("savings-supervisor-details/", views.GetSavingSupervisorDetails.as_view()),


    path("check-bvn-otp/", views.OTPCheckAPIView.as_view()),

    *sign_up_urls,
    *resend_urls,
    *user_urls,
    *transaction_pin_urls,
    *constant_table_ulrs,
    *kyc_urls,
    *cloud_messaging,
    *loans_urls,
    *merchant_pin,
    *lotto_urls,
    *others,
    *settings_urls,
    *display_banner_urls,
    *custom_paybox_onboard,
    *profile_settings,
]
