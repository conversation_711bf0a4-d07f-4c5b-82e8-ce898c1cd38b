import uuid
import random

from django.db.models import Q
from django.contrib.auth.models import AbstractUser
from django.contrib.auth.hashers import make_password, check_password
from django.core.validators import <PERSON>V<PERSON>ueValidator, MaxValueValidator
from django.core.cache import cache

# from django.utils import timezone


from main.model_choices import *
from main.helper.helper_function import *
from main.helper.utils import BaseModel, CustomMediaStorage
from main.helper.api import ThirdPartyApis
from main.helper.helper_function import passcode_generator, trigger_callback_send_out
from main.helper.send_emails import send_email

from datetime import datetime, timedelta
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical

#


# from send_money.helpers.helper_functions import (
#     select_active_account_from_pool_of_single_provider,
#     choose_account_provider_on_save,
# )

TRANS_NOTIFICATION_CHOICES = [
    ("CREDIT", "CREDIT"),
    ("DEBIT", "DEBIT"),
    ("ALL", "ALL"),
]

NOTIFICATION_REMINDER = [
    ("NO_REMINDER", "NO_REMINDER"),
    ("IMPORTANT_REMINDER", "IMPORTANT_REMINDER"),
    ("ALL_REMINDER", "ALL_REMINDER"),
]


def get_uplaod_file_name(instance, filename):
    """
    Create/Upload ID to folder
    """
    return "user/identity/%s/%s_%s" % (
        str(instance.user.id),
        str(datetime.now().replace(".", "_"), filename),
    )


def create_unique_id_for_customer_old():
    """
    Create Unique ID
    """
    gen_uuid = str(uuid.uuid4()).replace('-', '').upper()

    generated_id = ''.join(random.choice(gen_uuid) for i in range(6))

    if User.objects.filter(unique_id=generated_id).exists():
        return create_unique_id_for_customer_old()
    else:
        return generated_id
    # return generated_id


def create_unique_id_for_customer():
    """
    Create Unique ID
    """
    while True:
        gen_uuid = str(uuid.uuid4()).replace('-', '').upper()
        generated_id = ''.join(random.choice(gen_uuid) for i in range(6))

        if not User.objects.filter(unique_id=generated_id).exists():
            return generated_id


def create_corporate_id():
    """
    Create COrporate ID
    """
    gen_uuid = str(uuid.uuid4()).replace('-', '').upper()

    generated_id = ''.join(random.choice(gen_uuid) for i in range(6))

    if CorporateAccount.objects.filter(corporate_id=generated_id).exists():
        return create_unique_id_for_customer()
    else:
        return generated_id
    # return generated_id


MERCHANT = "MERCHANT"
AGENT = "AGENT"
PERSONAL = "PERSONAL"
SALES_REP = "SALES_REP"
ADMIN = "ADMIN"
LOTTO_AGENT = "LOTTO_AGENT"
LIBERTY_RETAIL = "LIBERTY_RETAIL"
AJO_AGENT = "AJO_AGENT"
MERCHANT_AGENT = "MERCHANT_AGENT"
STAFF_AGENT = "STAFF_AGENT"
PROSPER_AGENT = "PROSPER_AGENT"
DMO_AGENT = "DMO_AGENT"
MICRO_SAVER = "MICRO_SAVER"
PHARMACIST = "PHARMACIST"
# STAFF_ADMIN = "STAFF_ADMIN"
# CUSTOMER_ADMIN = "CUSTOMER_ADMIN"
# SUPER_ADMIN = "SUPER_ADMIN"


TYPE_OF_TERMINAL_USER_CHOICE = [
    (MERCHANT, "MERCHANT"),
    (AGENT, "AGENT"),
    (PERSONAL, "PERSONAL"),
    (LOTTO_AGENT, "LOTTO_AGENT"),
    (LIBERTY_RETAIL, "LIBERTY_RETAIL"),
    (AJO_AGENT, "AJO_AGENT"),
    (MERCHANT_AGENT, "MERCHANT_AGENT"),
    (STAFF_AGENT, "STAFF_AGENT"),
    (PROSPER_AGENT, "PROSPER_AGENT"),
    (DMO_AGENT, "DMO_AGENT"),
    (MICRO_SAVER, "MICRO_SAVER"),
    (PHARMACIST, "PHARMACIST"),
]

ALL_TERMINAL_SERIAL_CHOICES = TYPE_OF_TERMINAL_USER_CHOICE + [
    (SALES_REP, "SALES_REP"),
    (ADMIN, "ADMIN"),

    # (STAFF_ADMIN, "STAFF_ADMIN"),
    # (CUSTOMER_ADMIN, "CUSTOMER_ADMIN"),
    # (SUPER_ADMIN, "SUPER_ADMIN"),
]

# DMO = DEPOSIT MOBILIZATION OFFICER (It is for Ajo)
list_of_allowed_terminal_users = ["MERCHANT", "AGENT", "LOTTO_AGENT", "LIBERTY_RETAIL", "AJO_AGENT", "STAFF_AGENT", "PROSPER_AGENT", "DMO_AGENT", "MERCHANT_AGENT"]

DEVICE_TYPE_CHOICES = (
    ("TERMINAL", "TERMINAL"),
    ("MOBILE", "MOBILE"),
    ("WEB", "WEB")
)

ONBOARDING_STATUS = (
    ("REGISTRATION", "REGISTRATION"),
    ("EMAIL_VERIFICATION", "EMAIL_VERIFICATION"),
    ("CREATE_PASSCODE", "CREATE_PASSCODE"),
    ("SECURITY_QUESTION", "SECURITY_QUESTION"),
    ("BVN_COLLECTION", "BVN_COLLECTION"),
    ("BVN_VERIFICATION", "BVN_VERIFICATION"),
    ("COMPLETED", "COMPLETED")
)


def profile_picture_upload_path(instance, filename):
    """
    Construct a dynamic path for profile picture
    """
    user_id = instance.id
    base_filename, file_extension = os.path.splitext(filename)

    return f'media/profile_pic/{user_id}/{base_filename}{file_extension}'


def validate_file_size(value):
    """
    Get the maximum file size from settings (in bytes)
    """
    max_size = getattr(settings, 'MAX_FILE_SIZE', 2 * 1024 * 1024)  # 2MB by default

    if value.size > max_size:
        raise ValidationError(f"The file size must not exceed {max_size} bytes.")


class RegistrationData(models.Model):
    data = models.TextField()
    date_created = models.DateTimeField(auto_now_add=True)


class User(AbstractUser, BaseModel):
    NONE_PROVIDER = "NULL"
    PROVIDUS_PROVIDER = "PROVIDUS"
    WOVEN_PROVIDER = "WOVEN"
    ACCOUNT_PROVIDERS = [
        (NONE_PROVIDER, "NULL"),
        (PROVIDUS_PROVIDER, "PROVIDUS"),
        (WOVEN_PROVIDER, "WOVEN"),
    ]

    GENDER_CHOICES = [
        ("MALE", "MALE"),
        ("FEMALE", "FEMALE")
    ]

    MARITAL_STATUS_CHOICES = [
        ("MARRIED", "MARRIED"),
        ("SINGLE", "SINGLE"),
        ("DIVORCED", "DIVORCED")
    ]

    TERMINAL_STATUS_CHOICES = [
        ("ACTIVE", "ACTIVE"),
        ("INACTIVE", "INACTIVE"),
        ("PARTIALLY_INACTIVE", "PARTIALLY_INACTIVE"),
        ("DORMANT", "DORMANT")
    ]

    # BranchSelection = tuple((str(b.BRAN), b.name) for b in Branch.objects.all())
    can_bypass_user_image_verification = models.BooleanField(default=True)
    password = models.CharField(max_length=100, null=True, blank=True, editable=False)
    tag = models.CharField(max_length=200, blank=True, default="INACTIVE")
    first_name = models.CharField(max_length=225)
    last_name = models.CharField(max_length=225)
    username = models.CharField(max_length=225, unique=True)
    phone_number = models.CharField(max_length=25, unique=True)
    email = models.EmailField(unique=True)
    customer_id = models.UUIDField(default=uuid.uuid4, editable=False)

    unique_id = models.CharField(max_length=10, default=create_unique_id_for_customer)
    terminal_id = models.CharField(max_length=500, null=True, blank=True, unique=True)
    terminal_serial = models.CharField(max_length=350, null=True, blank=True, unique=True)
    terminal_provider = models.CharField(max_length=300, null=True, blank=True, editable=False)
    date_assigned = models.DateTimeField(null=True, blank=True)

    custom_account_provider = models.CharField(max_length=300, null=True, blank=True)
    type_of_user = models.CharField(max_length=500, default="PERSONAL", choices=ALL_TERMINAL_SERIAL_CHOICES)
    is_paybox_merchant = models.BooleanField(default=False)
    is_pos_agent = models.BooleanField(default=False)
    update_email = models.EmailField(null=True, blank=True)
    email_updated = models.BooleanField(default=False)
    registration_email_otp = models.CharField(max_length=500, null=True, blank=True)
    registration_email_verified = models.BooleanField(default=False)
    referral_code = models.CharField(blank=True, null=True, max_length=300)
    referer_code = models.CharField(blank=True, null=True, max_length=300)
    state = models.CharField(max_length=170, blank=True, null=True)
    city = models.CharField(max_length=170, blank=True, null=True)
    lga = models.CharField(max_length=200, blank=True, null=True)
    nearest_landmark = models.CharField(null=True, blank=True, max_length=200)
    # branch = models.CharField(max_length=200, blank=True, null=True, default=)
    street = models.CharField(max_length=500, blank=True, null=True)
    has_login_pin = models.BooleanField(default=False)
    transaction_pin = models.CharField(max_length=500, null=True, blank=True)
    merchant_pin = models.CharField(max_length=500, null=True, blank=True)
    has_transaction_pin = models.BooleanField(default=False)
    has_merchant_pin = models.BooleanField(default=False)
    passcode_retries = models.PositiveIntegerField(default=0, null=True, blank=True)
    passcode_remaining_retries = models.IntegerField(default=10, null=True, blank=True)

    pin_retries = models.PositiveIntegerField(default=0, null=True, blank=True)
    pin_remaining_retries = models.IntegerField(default=6, null=True, blank=True)

    sec_que_retries = models.PositiveIntegerField(default=0, null=True, blank=True)
    sec_que_remaining_retries = models.IntegerField(default=5, null=True, blank=True)

    kyc_one_image_url = models.CharField(max_length=5000, null=True, blank=True)
    kyc_two_image_url = models.CharField(max_length=5000, null=True, blank=True)

    kyc_level = models.PositiveIntegerField(default=0)
    kyc_one_progress = models.CharField(max_length=200, default="NOT_VERIFIED")
    kyc_two_progress = models.CharField(max_length=200, default="NOT_VERIFIED")
    kyc_three_progress = models.CharField(max_length=200, default="NOT_VERIFIED")

    email_subscription = models.BooleanField(default=True)
    sms_subscription = models.BooleanField(default=True)
    send_money_status = models.BooleanField(default=True)
    block_on_funding = models.BooleanField(default=False)
    bypass_duplicate_trans = models.BooleanField(default=False)
    bypass_new_user_limit = models.BooleanField(default=False)

    master_bvn = models.BooleanField(default=True)
    bvn_number = models.CharField(max_length=100, null=True, blank=True)
    bvn_first_name = models.CharField(max_length=200, null=True, blank=True)
    bvn_last_name = models.CharField(max_length=200, null=True, blank=True)
    first_security_question = models.CharField(max_length=500, null=True, blank=True)
    first_security_answer = models.CharField(max_length=500, null=True, blank=True)
    second_security_question = models.CharField(max_length=500, null=True, blank=True)
    second_security_answer = models.CharField(max_length=500, null=True, blank=True)
    sales_rep_upline_code = models.CharField(max_length=200, null=True, blank=True)
    sales_rep_full_name = models.CharField(max_length=200, null=True, blank=True)
    initial_handler = models.CharField(max_length=250, null=True, blank=True)
    has_sales_rep = models.BooleanField(default=False)
    business_name = models.CharField(max_length=300, null=True, blank=True)
    business_local_govt = models.CharField(max_length=300, null=True, blank=True)
    gender = models.CharField(max_length=150, choices=GENDER_CHOICES, blank=True, null=True)
    profile_picture = models.ImageField(upload_to=profile_picture_upload_path, validators=[validate_file_size], blank=True, null=True)

    sales_rep_comm_balance_daily = models.FloatField(default=0.00)
    sales_rep_comm_balance = models.FloatField(default=0.00)

    bills_pay_comm_balance_daily = models.FloatField(default=0.00)
    bills_pay_comm_balance = models.FloatField(default=0.00)
    wallet_balance = models.FloatField(default=0.00)

    other_comm_balance_daily = models.FloatField(default=0.00)
    other_comm_balance = models.FloatField(default=0.00)

    firebase_key = models.CharField(max_length=2300, default="FBK")
    notify_app_token = models.CharField(max_length=500, null=True, blank=True)

    login_count = models.PositiveIntegerField(default=0)
    terminal_login_count = models.PositiveIntegerField(default=0)
    mobile_login_count = models.PositiveIntegerField(default=0)
    web_login_count = models.PositiveIntegerField(default=0)

    daily_terminal_login_count = models.PositiveIntegerField(default=0)
    weekly_terminal_login_count = models.PositiveIntegerField(default=0)
    monthly_terminal_login_count = models.PositiveIntegerField(default=0)

    first_login = models.DateTimeField(null=True, blank=True)
    terminal_last_login = models.DateTimeField(null=True, blank=True)
    mobile_last_login = models.DateTimeField(null=True, blank=True)
    web_last_login = models.DateTimeField(null=True, blank=True)

    is_fraud = models.BooleanField(default=False)
    # fraud_reason = models.CharField(max_length=350, null=True, blank=True)
    lotto_suspended = models.BooleanField(default=False)
    is_suspended = models.BooleanField(default=False)
    suspension_reason = models.TextField(blank=True, null=True)

    terminal_suspended = models.BooleanField(default=False)
    mobile_suspended = models.BooleanField(default=False)

    terminal_disabled = models.BooleanField(default=False)
    mobile_disabled = models.BooleanField(default=False)

    terminal_disable_count_sum = models.PositiveIntegerField(default=0)
    mobile_disable_count_sum = models.PositiveIntegerField(default=0)

    terminal_disable_count = models.PositiveIntegerField(default=0)
    mobile_disable_count = models.PositiveIntegerField(default=0)

    role = models.CharField(max_length=150, null=True, blank=True)
    agent_consent = models.BooleanField(default=False)
    date_of_consent = models.DateTimeField(null=True, blank=True)

    marital_status = models.CharField(max_length=150, choices=MARITAL_STATUS_CHOICES, blank=True, null=True)
    vfd_bvn_acct_num_count = models.PositiveIntegerField(default=0)

    terminal_status = models.CharField(max_length=255, choices=TERMINAL_STATUS_CHOICES, default="ACTIVE")
    terminal_truly_active = models.BooleanField(default=False, blank=True, null=True)
    inactive_count = models.IntegerField(default=0)
    terminal_last_inactive = models.DateTimeField(blank=True, null=True)

    lotto_win_toggle = models.BooleanField(default=False)
    added_trans_limit = models.FloatField(default=0.00)
    added_daily_trans_count = models.PositiveIntegerField(default=0)
    can_add_trans_limit = models.BooleanField(default=True)

    trans_band = models.PositiveIntegerField(default=1, validators=[MinValueValidator(1), MaxValueValidator(3)])
    exclude_trans_band = models.BooleanField(default=False)
    reactivation_request = models.BooleanField(default=False)

    change_pass_hash = models.CharField(max_length=200, null=True, blank=True)
    change_pass_hash_time = models.DateTimeField(null=True, blank=True)

    ussd_active = models.BooleanField(default=False)
    ussd_active_admin_lock = models.BooleanField(default=False)
    num_of_other_accounts = models.PositiveIntegerField(default=0)
    source = models.CharField(max_length=200, null=True, blank=True)
    user_branch = models.ForeignKey("NewLocationList", related_name="user_branch", on_delete=models.SET_NULL, null=True, blank=True)

    onboarding_status = models.CharField(max_length=255, null=True, blank=True, choices=ONBOARDING_STATUS, default="REGISTRATION")
    fake_email = models.BooleanField(default=False)

    # notification checker
    news_and_update = models.BooleanField(default=False)
    tips_and_tutorial = models.BooleanField(default=False)
    user_research = models.BooleanField(default=False)
    transaction_notification = models.CharField(max_length=50, null=True, blank=True, choices=TRANS_NOTIFICATION_CHOICES, default="ALL")

    # reminders
    reminder_update = models.CharField(max_length=50, null=True, blank=True, choices=NOTIFICATION_REMINDER, default="ALL_REMINDER")

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["username", "firebase_key"]

    def clean(self):
        if self.pk is not None and self.is_suspended != self.__class__.objects.get(pk=self.pk).is_suspended:
            # Check if the suspension reason is provided when changing the is_suspended field
            if not self.suspension_reason:
                raise ValidationError('Suspension reason is required when changing is_suspended')

    def save(self, custom_request=None, *args, **kwargs):

        # Check if email is lower
        if not self.email.islower():
            self.email = self.email.lower()

        log_debug("-----------------------------------")
        log_debug("-----------------------------------")
        log_debug("-----------------------------------")

        # print(kwargs)
        # print(kwargs["update_fields"])

        # # update_fields = kwargs["update_fields"]
        # # for i in update_fields:
        # #     if i == "vfd_bvn_acct_num_count":
        # #         get_bvn = self.check_kyc.bvn_rel.bvn_number

        # #         for User.objects.filter()

        # print("-----------------------------------")
        # print("-----------------------------------")
        # print("-----------------------------------")

        self.bills_pay_comm_balance = round(self.bills_pay_comm_balance, 2)
        self.bills_pay_comm_balance_daily = round(self.bills_pay_comm_balance_daily, 2)

        self.other_comm_balance = round(self.other_comm_balance, 2)
        self.other_comm_balance_daily = round(self.other_comm_balance_daily, 2)

        self.sales_rep_comm_balance = round(self.sales_rep_comm_balance, 2)
        self.sales_rep_comm_balance_daily = round(self.sales_rep_comm_balance_daily, 2)

        # if self.kyc_level < 1:
        #     self.send_money_status = False

        if self.kyc_level > 1 and self.block_on_funding == False:
            self.send_money_status = True

        # Fraud
        if self.is_fraud == True:
            self.active = False
            self.send_money_status = False
            self.is_suspended = True
            self.suspension_reason = "Suspended For Fraud"
            self.terminal_suspended = True
            self.mobile_suspended = True
            self.terminal_disabled = True
            self.mobile_disabled = True

        # Terminal
        if self.terminal_disabled:
            self.terminal_disable_count += 1
            self.terminal_disable_count_sum += 1

        if self.terminal_disabled is False:
            self.terminal_disable_count = 0

        if self.terminal_disable_count > 2:
            self.terminal_suspended = True

        # Mobile
        if self.mobile_disabled:
            self.mobile_disable_count += 1
            self.mobile_disable_count_sum += 1

        if self.mobile_disabled is False:
            self.mobile_disable_count = 0

        if self.mobile_disable_count > 2:
            self.mobile_suspended = True

        if self.password is not None:
            self.has_login_pin = True

        if self.sales_rep_upline_code is not None:
            self.has_sales_rep = True

            if self.sales_rep_full_name is None:
                from admin_dashboard.models import SalesRep
                sales_rep_full_name = SalesRep.objects.filter(sales_rep_code=self.sales_rep_upline_code).last()
                if sales_rep_full_name:
                    self.sales_rep_full_name = sales_rep_full_name.sales_rep.bvn_full_name
                else:
                    self.sales_rep_upline_code = None
                    self.has_sales_rep = False

        elif self.sales_rep_upline_code is None:
            self.has_sales_rep = False
            self.sales_rep_full_name = None

        if not self.is_suspended and self.has_login_pin:
            if "anonymous" in self.email:
                self.is_suspended = True
                self.suspension_reason = "Suspended for having anonymous in email"

        if self.pk is not None and self.is_suspended != self.__class__.objects.get(pk=self.pk).is_suspended:
            log_info("Came to suspension")
            suspended_by = custom_request.user if custom_request is not None else None

            user_flag = UserFlag.create_suspension_instance(
                user=self, reason=self.suspension_reason,
                is_suspended=self.is_suspended, suspended_by=suspended_by
            )
            # Wipe the suspension reason after saving the object
            self.suspension_reason = ''

        if self.first_security_answer is None:
            self.first_security_question = None

        if ConstantTable.get_constant_table_instance().trans_band_regulator == True:
            if self.exclude_trans_band == False:
                if self.terminal_status == "ACTIVE":
                    self.trans_band = 1

                elif self.terminal_status == "PARTIALLY_INACTIVE":
                    self.trans_band = 2

                elif self.terminal_status in ["INACTIVE", "DORMANT"]:
                    self.trans_band = 3

        # check_kyc, created = KYCTable.objects.get_or_create(user=self)
        # bvn_rel, created = BVNDetail.objects.get_or_create(kyc=check_kyc)

        # self.bvn_first_name = bvn_rel.bvn_first_name
        # self.bvn_last_name = bvn_rel.bvn_last_name

        # # if self.check_kyc:
        # #     if self.check_kyc.bvn_rel:
        # #         self.bvn_first_name = self.check_kyc.bvn_rel.bvn_first_name
        # #         self.bvn_last_name = self.check_kyc.bvn_rel.bvn_last_name
        # #     else:
        # #         pass
        # # else:
        # #     pass

        super(User, self).save(*args, **kwargs)

    def __str__(self) -> str:
        return self.email

    @property
    def get_kyc_level(self):
        from kyc_app.models import KYCTable

        return KYCTable.check_user_kyc_level(user=self)

    @property
    def full_name(self):
        return self.first_name + " " + self.last_name

    @property
    def bvn_full_name(self):
        return f"{self.bvn_first_name} {self.bvn_last_name}"

    @property
    def get_full_address(self):
        return f"{self.street} {self.lga} {self.state}"

    @classmethod
    def get_float_user(cls):
        return cls.objects.filter(email=f"{settings.FLOAT_USER_EMAIL}").last()

    @staticmethod
    def format_number_from_back_add_234(phone_number: str) -> str:
        phone_number = phone_number.replace("+", "")
        if phone_number.startswith("235") and len(phone_number) == 13:
            return phone_number

        formatted_num = phone_number[-10:]

        if len(formatted_num) != 10 or formatted_num[0] == "0":
            return None
        else:
            return "234" + formatted_num

    @staticmethod
    def format_number_from_back_add_zero(phone_number) -> str:

        formatted_num = phone_number[-10:]

        if len(formatted_num) != 10 or formatted_num[0] == "0":
            return None
        else:
            return "0" + formatted_num

    @staticmethod
    def check_user_login_pin(user) -> bool:
        """
        Check if user has login pin on regd pin is correct
        """
        return user.has_login_pin

    @staticmethod
    def check_if_user_login_pin_match(user, pincode) -> bool:
        """""
        Check if enetered pin is correct
        """
        return check_password(pincode, user.password)

    @staticmethod
    def create_registration_email_otp(user, passcode) -> bool:
        """
        Collect pin and hash it
        """
        # if not user.registration_email_verified and not user.has_login_pin:
        if not user.registration_email_verified:
            hashed_pin = make_password(passcode)
            user.registration_email_otp = hashed_pin
            user.save()
            return True
        else:
            return False

    @staticmethod
    def generate_micro_saver_access_token(user_id):

        expiration_time = datetime.utcnow() + timedelta(days=1)
        jwt_id = str(uuid.uuid4())
        payload = {
            "token_type": "access",
            'exp': expiration_time,
            'jti': jwt_id,
            'user_id': user_id,
        }
        return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')

    @classmethod
    def create_change_passcode_hash(cls, user, passcode) -> bool:
        """
        Collect pin and hash it
        """
        # if not user.registration_email_verified and not user.has_login_pin:
        hashed_pin = make_password(passcode)
        user.change_pass_hash = hashed_pin
        user.change_pass_hash_time = datetime.now()
        user.save()
        return True

    @staticmethod
    def custom_hash_values(passcode) -> str:
        """
        Hash any pin
        """
        hashed_pin = make_password(passcode)
        return hashed_pin

    @staticmethod
    def custom_unhash_values(passcode, hashed_counterpart) -> str:
        """
        UnHash any pin
        """
        return check_password(passcode, hashed_counterpart)

    @staticmethod
    def check_change_passcode_otp(user, passcode) -> bool:
        """
        Check if enetered pin is correct
        """
        if user.change_pass_hash_time:
            now = datetime.now()
            time_difference = now - user.change_pass_hash_time.replace(tzinfo=None)

            log_info(str(time_difference))
            log_info(str(time_difference.total_seconds()))

            real_time_diff = time_difference.total_seconds() // 60 > 500
            log_info(str(real_time_diff))

            if real_time_diff:
                return False
            else:
                return check_password(passcode, user.change_pass_hash)
        else:
            return False

    @staticmethod
    def check_registration_email_otp(user, passcode) -> bool:
        """
        Check if enetered pin is correct
        """
        return check_password(passcode, user.registration_email_otp)

    @staticmethod
    def create_transaction_pin(user: 'User', pincode) -> bool:
        """
        Collect pin and hash it
        """
        if not user.transaction_pin:
            hashed_pin = make_password(pincode)
            user.transaction_pin = hashed_pin
            user.pin_retries = 0
            user.pin_remaining_retries = 6
            user.save()

            return True
        else:
            return False

    @staticmethod
    def create_merchant_pin(user, pincode) -> bool:
        """
        Collect pin and hash it
        """
        if not user.merchant_pin:
            # encoded_pin = pincode.encode('utf-8')
            # hashed_pin = hashlib.sha256(encoded_pin).hexdigest()

            hashed_pin = make_password(pincode)
            user.merchant_pin = hashed_pin
            user.has_merchant_pin = True
            user.save()
            return True
        else:
            return False

    @staticmethod
    def check_merchant_pin(user, pincode) -> bool:
        """
        Check if enetered merchant pin is correct
        """
        return check_password(pincode, user.merchant_pin)

    @staticmethod
    def check_user_passcode_pin(user: "User", passcode) -> bool:
        """
        Check if enetered pin is correct
        """
        return check_password(passcode, user.password)

    @staticmethod
    def count_down_passcode_retries(user: "User") -> dict:
        user.passcode_retries += 1
        user.passcode_remaining_retries -= 1
        user.save(update_fields=["passcode_retries", "passcode_remaining_retries"])

        if settings.ENVIRONMENT == "production" and user.passcode_retries >= 10:
            User.suspend_user(user, "Exceeded Passcode Retries")

        return {
            "retry_count": user.passcode_retries,
            "remaining_retries": user.passcode_remaining_retries,
        }

    # @staticmethod
    # def reset_passcode_retries(user: "User"):
    #     user.passcode_retries = 0
    #     user.passcode_remaining_retries = 10
    #     user.save(update_fields=["passcode_retries", "passcode_remaining_retries"])

    @staticmethod
    def check_sender_transaction_pin(user: "User", pincode) -> bool:
        """
        Check if enetered pin is correct
        """
        return check_password(pincode, user.transaction_pin)

    @staticmethod
    def count_down_transaction_pin_retries(user: "User") -> dict:
        user.pin_retries += 1
        user.pin_remaining_retries -= 1
        user.save(update_fields=["pin_retries", "pin_remaining_retries"])

        return {
            "retry_count": user.pin_retries,
            "remaining_retries": user.pin_remaining_retries,
        }

    @staticmethod
    def reset_transaction_pin_retries(user: "User"):
        user.pin_retries = 0
        user.pin_remaining_retries = 6
        user.save(update_fields=["pin_retries", "pin_remaining_retries"])

    @staticmethod
    def count_down_sec_ques_pin_retries(user) -> dict:
        user.sec_que_retries += 1
        user.sec_que_remaining_retries -= 1
        user.save(update_fields=["sec_que_retries", "sec_que_remaining_retries"])

        return {
            "retry_count": user.sec_que_retries,
            "remaining_retries": user.sec_que_remaining_retries,
        }

    @staticmethod
    def reset_security_question_retries(user):
        user.sec_que_retries = 0
        user.sec_que_remaining_retries = 5
        user.save(update_fields=["sec_que_retries", "sec_que_remaining_retries"])

    @staticmethod
    def check_sender_balance(user, amount_with_charge, account_obj) -> bool:
        """
        Check if sender has enough balance for transaction for a particular account
        """

        get_account = account_obj.objects.get(user=user)
        if get_account.availableBalance > amount_with_charge:
            return True
        else:
            return False

    @staticmethod
    def suspend_user(user: 'User', reason, request=None):
        """
        This Method is generally used to suspend users
        """

        user.is_suspended = True
        user.suspension_reason = reason
        user.save(custom_request=request)

        return True

    @staticmethod
    def create_agent_profile_and_user(data, type_of_user, business_lga, supervisor_id):

        # print("I am here")
        # print(data)
        phone_number = data["phone_number"]

        if data.get("sales_rep_code"):
            sales_rep_upline = data.get("sales_rep_code")
        else:
            sales_rep_upline = None

        if data["form_username"]:
            username = data["form_username"]
        else:
            gen_uuid = str(uuid.uuid4()).replace('-', '').upper()
            username = f"{data['first_name'][-3:]}-{''.join(random.choice(gen_uuid) for i in range(5))}"

        if data["email"]:
            email = data["email"]
        else:
            email = f"{phone_number}@libertyng.com"

        first_name = data["first_name"]
        last_name = data["last_name"]

        if User.objects.filter(username=username).exists():
            gen_uuid = str(uuid.uuid4()).replace('-', '').upper()
            new_user_name = ''.join(random.choice(gen_uuid) for i in range(6))

        else:
            new_user_name = username

        user_check = User.objects.filter(Q(phone_number=phone_number) | Q(email=email) | Q(username=username)).first()

        if user_check:
            user_instance = user_check

        else:

            user_instance = User.objects.create(
                phone_number=phone_number,
                email=email,
                username=new_user_name,
                first_name=first_name,
                last_name=last_name,
                state=data["state_province"],
                street=data["street_address_line_1"],
                sales_rep_upline_code=sales_rep_upline,
                type_of_user=type_of_user,
                business_local_govt=business_lga,
            )

            passcode = str(passcode_generator())
            User.create_registration_email_otp(
                user=user_instance, passcode=passcode
            )
            send_email(email=email, passcode=passcode)

            if type_of_user == "LOTTO_AGENT":
                send_verification_to_lotto_supervisor(user_instance, supervisor_id)

        return user_instance


###################################################################################
class UserFormatted(models.Model):
    user_instance = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    user_id = models.PositiveIntegerField()
    first_name = models.CharField(max_length=225)
    last_name = models.CharField(max_length=225)
    username = models.CharField(max_length=225)
    phone_number = models.CharField(max_length=25)
    email = models.EmailField(db_index=True)
    type_of_user = models.CharField(max_length=500, default="PERSONAL", choices=ALL_TERMINAL_SERIAL_CHOICES)
    state = models.CharField(max_length=170, blank=True, null=True)
    lga = models.CharField(max_length=200, blank=True, null=True)
    nearest_landmark = models.CharField(null=True, blank=True, max_length=200)
    street = models.CharField(max_length=500, blank=True, null=True)
    business_name = models.CharField(max_length=300, null=True, blank=True)
    date_joined = models.DateTimeField()
    kyc_level = models.PositiveIntegerField(default=0)
    terminal_serial = models.CharField(max_length=350, null=True, blank=True, unique=True)
    date_assigned = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


###################################################################################


class SuperAgentProfile(models.Model):
    LOTTO_SUPERVISOR = "LOTTO_SUPERVISOR"
    SAVINGS_SUPERVISOR = "SAVINGS_SUPERVISOR"

    LOTTO_LEAD = "LOTTO_LEAD"
    SAVINGS_LEAD = "SAVINGS_LEAD"

    SUPERVISOR_TYPE_CHOICES = [
        (LOTTO_SUPERVISOR, "LOTTO_SUPERVISOR"),
        (SAVINGS_SUPERVISOR, "SAVINGS_SUPERVISOR"),
    ]
    VERTICAL_TYPE_CHOICES = [
        (LOTTO_LEAD, "LOTTO_LEAD"),
        (SAVINGS_LEAD, "SAVINGS_LEAD"),
    ]
    team_lead = models.ForeignKey(User, related_name="team_lead", on_delete=models.SET_NULL, blank=True, null=True)
    team_lead_type = models.CharField(max_length=100, choices=VERTICAL_TYPE_CHOICES, default=LOTTO_LEAD)
    super_agent = models.ForeignKey(User, related_name="super_agent", on_delete=models.SET_NULL, null=True, blank=True)
    agent = models.OneToOneField(User, related_name="sub_agent", on_delete=models.CASCADE)
    supervisor = models.ForeignKey(User, related_name="agent_supervisor", on_delete=models.SET_NULL, null=True, blank=True)
    supervisor_type = models.CharField(max_length=100, choices=SUPERVISOR_TYPE_CHOICES, default=LOTTO_SUPERVISOR)
    verification_id = models.CharField(max_length=200, blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    is_supervisor = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    # def clean(self):
    #     get_super_agent = SuperAgentProfile.objects.filter(super_agent=self.agent).last()
    #     if get_super_agent:
    #         raise ValidationError('Error. Agent already exists as a super agent')


class AgentProfile(BaseModel):
    user = models.ForeignKey(
        User,
        related_name="agent_profile",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    unique_id = models.CharField(blank=True, null=True, max_length=50)
    bvn_num = models.CharField(blank=True, null=True, max_length=50)
    phone_number = models.CharField(max_length=50, unique=True)
    email = models.CharField(max_length=50, null=True, blank=True, unique=True)
    username = models.CharField(max_length=50, null=True, blank=True)
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(blank=True, null=True, max_length=50)
    city = models.CharField(max_length=70, null=True, blank=True)
    type_of_user = models.CharField(max_length=200, null=True, blank=True)
    form_username = models.CharField(max_length=50, null=True, blank=True)
    membership = models.CharField(max_length=50, null=True, blank=True)
    next_of_kin = models.CharField(max_length=50, null=True, blank=True)
    next_of_kin_phone_number = models.CharField(max_length=50, null=True, blank=True)
    postal_zipcode = models.CharField(max_length=30, null=True, blank=True)
    state_province = models.CharField(max_length=50, null=True, blank=True)
    street_address_line_1 = models.TextField(null=True, blank=True)
    street_address_line_2 = models.TextField(null=True, blank=True)
    picture = models.TextField(blank=True, null=True)
    submission_date = models.CharField(max_length=50, null=True, blank=True)
    state = models.CharField(blank=True, null=True, max_length=50)
    lga = models.CharField(blank=True, null=True, max_length=50)
    nearestlandmark = models.CharField(blank=True, null=True, max_length=150)
    street = models.CharField(blank=True, null=True, max_length=150)
    sent_to_horizon = models.BooleanField(default=False)
    form_user = models.BooleanField(default=True)
    terminal_id = models.CharField(max_length=500, unique=True, null=True, blank=True)
    sales_rep_upline_code = models.CharField(max_length=200, null=True, blank=True)
    business_name = models.CharField(max_length=300, null=True, blank=True)
    gender = models.CharField(max_length=80, null=True, blank=True)
    horizon_pay_payload = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name_plural = "Agent Profile"

    def __str__(self):
        return "{}".format(self.email)

    @staticmethod
    def create_agent_profile_and_user(data, type_of_user):

        # print("I am here")
        # print(data)
        phone_number = data["phone_number"]

        if data.get("sales_rep_code"):
            sales_rep_upline = data.get("sales_rep_code")
        else:
            sales_rep_upline = None

        if data["form_username"]:
            username = data["form_username"]
        else:
            username = f"{data['first_name'][:3]}-{data['unique_id']}"

        if data["email"]:
            email = data["email"]
        else:
            email = f"{phone_number}@libertyng.com"

        first_name = data["first_name"]
        last_name = data["last_name"]

        if User.objects.filter(username=username).exists():
            new_user_name = f"{username}-{str(uuid.uuid4())[:5]}"
        else:
            new_user_name = username

        user_check = User.objects.filter(Q(phone_number=phone_number) | Q(email=email) | Q(username=username)).first()

        if user_check:
            user_instance = user_check

        else:

            user_instance = User.objects.create(
                phone_number=phone_number,
                email=email,
                username=new_user_name,
                first_name=first_name,
                last_name=last_name,
                state=data["state_province"],
                street=data["street_address_line_1"],
                sales_rep_upline_code=sales_rep_upline,
                type_of_user=type_of_user
            )

            passcode = str(passcode_generator())
            User.create_registration_email_otp(
                user=user_instance, passcode=passcode
            )
            send_email(email=email, passcode=passcode)

        return user_instance

    @staticmethod
    def set_user_password(user):
        passcode = passcode_generator()
        user.set_password(passcode)
        user.save()
        email = user.email
        send_email(email=email, passcode=passcode)

    @staticmethod
    def create_new_user(data):
        phone_number = data["phone_number"]

        if data["form_username"]:
            username = data["form_username"]
        else:
            username = f"{data['phone_number'][:3]}-{data['unique_id']}"

        if data["email"]:
            email = data["email"]
        else:
            email = f"{phone_number}@libertyng.com"

        user_instance = User.objects.create(
            phone_number=phone_number, email=email, username=username
        )

        return user_instance

    @staticmethod
    def send_data_to_horizon_pay(user_id):

        """
        Initiate posting data to horizon servers for only users who are pos agents."""
        user = User.objects.filter(id=user_id).last()

        agent_instance = user.agent_profile.last()

        response = ThirdPartyApis.horizon_pay_registration(
            name=user.username,
            email=user.email,
            customer_id=str(user.customer_id),
            phone=user.phone_number,
            address=user.street,
            state=user.state,
        )

        if response.get("error"):  # If error field is true from response then sending would have failed
            agent_instance.horizon_pay_payload = response
            agent_instance.save()

            return {"data": "could not send data to horizon pay"}
        else:
            agent_instance.sent_to_horizon = True
            agent_instance.horizon_pay_payload = response
            agent_instance.save()

            return {"data": "sent data to horizon pay"}


class LoginStatus(models.Model):
    LOGIN_STATUS = (
        ("Online", "Online"),
        ("Offline", "Offline"),
    )
    login_status = models.CharField(
        choices=LOGIN_STATUS, default=None, null=True, max_length=50
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, max_length=50)
    last_login_date = models.DateTimeField(auto_now_add=True)


class Users_Otp(models.Model):
    otp_id = models.CharField(max_length=200, unique=True)
    otp_type = models.CharField(max_length=200)
    route = models.CharField(max_length=200)
    phone_number = models.CharField(max_length=50)
    app_name = models.CharField(max_length=100, choices=settings.APP_NAME, default=None)
    receiver = models.CharField(max_length=50)
    otp_sent_data = models.CharField(max_length=100)
    hotp_count = models.IntegerField()
    notified = models.CharField(max_length=50)
    utilized = models.CharField(max_length=50)
    template_id = models.CharField(max_length=200)
    application_id = models.CharField(max_length=200)
    isOtpdelivered = models.BooleanField(default=False)
    isOtpVerified = models.BooleanField(default=False)
    otp = models.CharField(max_length=6)

    @staticmethod
    def send_new_otp(phone_number, app_name, no_callback=False):

        response = ThirdPartyApis.send_phone_otp(phone_number, no_callback)
        try:
            Users_Otp.objects.create(
                otp_id=str(response["id"]),
                otp_type=str(response["type"]),
                route=str(response["route"]),
                app_name=str(app_name),
                phone_number=str(phone_number),
                receiver=str(response["receiver"]),
                hotp_count=str(response["hotp_count"]),
                notified=str(response["notified"]),
                utilized=str(response["utilized"]),
                template_id=str(response["template"]),
                application_id=str(response["application"]),
                isOtpdelivered=True
            )
            response_data = {
                "Delivered to": response["receiver"],
                "method of delivery": response["type"],
                "Delivery Status": "Sent",
            }
        except Exception as e:
            response_data = {
                "error": f"{e}",
                "message": "Something went wrong, Please Try again later."
            }
        return response_data

    @staticmethod
    def send_voice_otp_function(phone_number, app_name):
        response = ThirdPartyApis.send_voice_otp(phone_number)
        try:
            Users_Otp.objects.create(
                otp_id=str(response["id"]),
                otp_type=str(response["type"]),
                route=str(response["route"]),
                app_name=str(app_name),
                phone_number=str(phone_number),
                receiver=str(response["receiver"]),
                hotp_count=str(response["hotp_count"]),
                notified=str(response["notified"]),
                utilized=str(response["utilized"]),
                template_id=str(response["template"]),
                application_id=str(response["application"]),
                isOtpdelivered=True
            )
            response_data = {
                "Delivered to": response["receiver"],
                "method of delivery": response["type"],
                "Delivery Status": "Sent",
            }
        except Exception as e:
            response_data = {
                "error": f"{e}",
                "message": "Something went wrong, Please Try again later."
            }
        return response_data

    @staticmethod
    def verify_new_otp(phone_no, otp_value, app_name):

        response = ThirdPartyApis.verify_otp(phone_no, otp_value)

        if response.get("receiver") is not None:
            receiver_phone_number = response["receiver"]
            otp = response["otp"]
            verification_status = response["verified"]
            Users_Otp.objects.filter(
                receiver=receiver_phone_number, app_name=app_name
            ).update(otp=otp, isOtpVerified=verification_status)
        else:
            pass

        return response


##########################################################################################
# CONSTANTS


class ConstantTable(models.Model):
    is_active = models.BooleanField(
        default=True,
        help_text="Must remain active."
    )
    false_float_low_balance_trigger = models.BooleanField(
        default=True,
        help_text="When true, accounts like savings and lotto will have thier balances preserved and never run out"
    )
    block_on_funding_amount = models.FloatField(
        default=5000.0,
        help_text="Specify the inflow amount to block a user when they are on watch list."
    )

    agent_terminal_sales_commission = models.FloatField(default=3000)
    supervisor_terminal_sales_commission = models.FloatField(default=1000)
    manager_terminal_sales_commission = models.FloatField(default=1000)
    manager_terminal_sales_email = models.EmailField(blank=True, null=True)

    agent_max_daily_limit = models.FloatField(default=0.0)
    customer_max_daily_limit = models.FloatField(default=0.0)
    global_cashout_transaction_limit = models.FloatField(default=20000.00)

    kyc_zero_transfer_limit = models.FloatField(default=0.0)

    kyc_one_daily_trans_count = models.PositiveIntegerField(default=5)
    kyc_one_transfer_limit = models.FloatField(default=50000.00)
    kyc_one_daily_transfer_limit = models.FloatField(default=300000.00)
    kyc_one_receivable_balance_limit = models.FloatField(default=100000.00)

    kyc_two_daily_trans_count = models.PositiveIntegerField(default=5)
    kyc_two_transfer_limit = models.FloatField(default=500000.00)
    kyc_two_daily_transfer_limit = models.FloatField(default=500000.00)
    kyc_two_receivable_balance_limit = models.FloatField(default=250000.00)

    new_user_inflow_limit = models.FloatField(default=50000.00)
    new_user_seven_days_limit = models.FloatField(default=20000.00)
    new_user_forty_days_limit = models.FloatField(default=100000.00)

    kyc_three_daily_trans_count = models.PositiveIntegerField(default=5)
    kyc_three_transfer_limit = models.FloatField(default=500000.00)
    kyc_three_daily_transfer_limit = models.FloatField(default=500000.00)
    kyc_three_receivable_balance_limit = models.FloatField(default=250000.00)
    max_add_trans_limit = models.FloatField(default=1000000.00)
    enforce_global_limit = models.BooleanField(
        default=False,
        help_text=(
            "Note that customers can have added transfer limit on request"
            " So When this field is true, everyone is forced to have a transfer limit of their KYC level"
        )
    )
    exclude_enforce_global_limit = models.JSONField(
        default=list, null=True, blank=True,
        help_text="Include emails to be excluded from the global limit here"
    )

    account_provider = models.CharField(
        max_length=300, choices=AccountProviders.choices, default=AccountProviders.VFD_PROVIDER,
        help_text="Select an transfer provider here"
    )
    account_creation_provider = models.CharField(
        max_length=300, choices=AccountProviders.choices, default=AccountProviders.VFD_PROVIDER,
        help_text="Select an an accounts creation provider here"
    )
    bill_payment_provider = models.CharField(
        max_length=300, choices=BillPaymentProviders.choices, default=BillPaymentProviders.CORALPAY
    )
    lotto_kyc_level_choice = models.CharField(
        max_length=100, choices=LottoKYCLevel.choices, default=LottoKYCLevel.THREE,
        help_text="This is the tier that an agent must be on to access the lotto portal"
    )
    paybox_float_email = models.EmailField(blank=True, null=True)

    effect_cash_out_pricing = models.BooleanField(
        default=False,
        help_text="When this is true, hitting the save button will trigger a re-calculation of the cash out charges for people on band one."
    )
    effect_cash_out_pricing_band_two = models.BooleanField(
        default=False,
        help_text="When this is true, hitting the save button will trigger a re-calculation of the cash out charges for people on band two."
    )
    effect_cash_out_pricing_band_three = models.BooleanField(
        default=False,
        help_text="When this is true, hitting the save button will trigger a re-calculation of the cash out charges for people on band three."
    )

    request_pos_verf_fee = models.FloatField(
        default=200.00,
        help_text="Amount charged to request for POS"
    )
    woven_fee = models.FloatField(
        default=10.00,
        help_text="Woven transfer fee"
    )
    vfd_fee = models.FloatField(
        default=10.00,
        help_text="VFD transfer fee"
    )
    default_provider_fee = models.FloatField(
        default=10.00,
        help_text="Liberty Pay transfer fee only for record purpose"
    )
    sales_rep_comm_cash_out = models.FloatField(
        default=0.2,
        help_text="Commission paid to sales rep on cash out by downline"
    )
    sales_rep_comm_transfer = models.FloatField(
        default=0.2,
        help_text="Commission paid to sales rep on transfer by downline"
    )
    send_money_transfer_fee = models.FloatField(
        default=20,
        help_text="tranfser fee for agents on band one"
    )
    send_money_transfer_fee_band_two = models.FloatField(
        default=30,
        help_text="tranfser fee for agents on band two"
    )
    send_money_transfer_fee_band_three = models.FloatField(
        default=45,
        help_text="tranfser fee for agents on band three"
    )
    send_money_transfer_extra_fee = models.FloatField(
        default=5,
        help_text="extra tranfser fee to be charged"
    )

    lotto_super_agent_profit = models.FloatField(default=2)
    lotto_agent_verification_url = models.URLField(blank=True, null=True, default="http://localhost.com")

    agent_transfer_fee_value = models.FloatField(default=3)
    ro_transfer_fee_value = models.FloatField(default=3)

    stock_price = models.FloatField(default=25000.0)

    oob_max_sum = models.FloatField(
        default=100000,
        help_text="Maximum amount to be sent by admin for reversals in a day"
    )
    ussd_bills_fee = models.FloatField(
        default=7,
        help_text="charge for buying airtime via USSD"
    )
    liberty_cash_out_fee = models.FloatField(default=0.5)
    liberty_cash_out_fee_band_two = models.FloatField(default=0.6)
    liberty_cash_out_fee_band_three = models.FloatField(default=0.7)

    merchant_cash_out_percent = models.FloatField(
        default=1,
        help_text="percentage to charge merchants on cash out"
    )

    merchant_inflow_percent = models.FloatField(default=0.55, blank=True, null=True)
    merchant_inflow_capped_at = models.FloatField(default=1000, blank=True, null=True)

    merchant_cash_out_max_charge_amount = models.FloatField(default=1000, help_text="Max deductable charge on a merchant transaction.")

    nibbs_cash_out_fee = models.FloatField(default=0.22)
    interswitch_cash_out_fee_value = models.FloatField(default=15.0)
    aptics_cash_out_fee_value = models.FloatField(default=2.25)
    mbbs_cash_out_fee_value = models.FloatField(default=2.0)

    ro_cash_out_fee = models.FloatField(
        default=10,
        help_text="profit for R.O on cash out (%)"
    )
    ro_cash_out_fee_value = models.FloatField(
        default=3,
        help_text="profit for R.O on cash out"
    )
    cash_back_cash_out_fee = models.FloatField(
        default=5,
        help_text="profit for agent on cash out"
    )

    fund_bank_transfer_fee = models.FloatField(
        default=15,
        help_text="charge on inflow to accounts"
    )

    virtual_card_initialize_amount = models.FloatField(
        default=1000,
        help_text="cost of requesting virtual card"
    )
    physical_card_initialize_amount = models.FloatField(
        default=1500,
        help_text="cost of requesting physical card"
    )
    card_delivery_fee = models.FloatField(default=500)
    default_card_spending_limit = models.FloatField(default=1000)

    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    allow_many_bvn = models.BooleanField(
        default=True,
        help_text="When true, this allows multiple users to have one BVN. This should remain False"
    )
    admin_sensitive_endpoint_lock = models.BooleanField(
        default=False,
        help_text="For endpoints marked as sensitive, this toggles allows them to be accessible by admin or not"
    )
    use_task_for_transfers = models.BooleanField(
        default=False,
        help_text="when true, first leg transfers happen in the background and not on request"
    )
    use_task_for_cash_out_resolve = models.BooleanField(
        default=True,
        help_text="when true, cash out events are resolved in the background and not on request"
    )
    automatic_reversals = models.BooleanField(
        default=False,
        help_text="when true, reversals are automatic"
    )
    enquire_account_on_send_money = models.BooleanField(
        default=True,
        help_text="when true, account enquiry happens again during the actual transfer"
    )
    block_duplicate_transaction = models.BooleanField(
        default=True,
        help_text="when true, this will disallow duplicate transfers within 5 minutes"
    )
    send_money_regulator = models.BooleanField(
        default=True,
        help_text="when true, send money is enabled"
    )
    buddy_transfer_regulator = models.BooleanField(
        default=True,
        help_text="when true, buddy transfer is enabled"
    )
    fund_lotto_wall_regulator = models.BooleanField(
        default=True,
        help_text="when true, send to lotto transfer is enabled"
    )
    bills_and_payment_regulator = models.BooleanField(
        default=True,
        help_text="when true, bills payment is enabled"
    )
    airtime_pin_regulator = models.BooleanField(
        default=True,
        help_text="when true, airtime pin purchase is enabled"
    )
    card_withdraw_regulator = models.BooleanField(
        default=True,
        help_text="when true, cash out is enabled"
    )
    card_interswitch_regulator = models.BooleanField(
        default=True,
        help_text="when true, interswitch charges are taken into consideration for cash out"
    )
    card_nibbs_regulator = models.BooleanField(
        default=True,
        help_text="when true, nibss charges are taken into consideration for cash out"
    )
    sudo_card_regulator = models.BooleanField(
        default=True,
        help_text="when true, card withdrawal/purchase is enabled"
    )
    lotto_play_regulator = models.BooleanField(
        default=True,
        help_text="when true, lotto game plays can be paid for"
    )
    trans_band_regulator = models.BooleanField(
        default=False,
        help_text="when true, this decides what band an agent falls into based on their transactions"
    )
    free_cash_out = models.BooleanField(
        default=False,
        help_text="when true, no commission will be charged on cash out"
    )

    africas_talking_ips = models.JSONField(default=list, null=True, blank=True)
    sudo_card_ips = models.JSONField(default=list, null=True, blank=True)

    allow_create_other_accounts = models.BooleanField(
        default=False,
        help_text="when true, agents can create multiple accounts"
    )
    api_create_accounts = models.JSONField(
        default=list, null=True, blank=True,
        help_text="list of accounts that can create accounts via API"
    )
    temp_admin_dash_users = models.JSONField(
        default=list, null=True, blank=True,
        help_text="list of accounts that can access the agency bankimg web dashboard"
    )
    watch_balance_users = models.JSONField(
        default=list, null=True, blank=True,
        help_text="---Deprecated; list of accounts whose balance are being watched"
    )

    pos_app_latest_version = models.CharField(max_length=10, default="*******")
    stock_price = models.FloatField(default=25000.0)
    trans_monitor_thresh = models.FloatField(
        default=50000.0,
        help_text="for analytics purposes"
    )
    overdraft_bench = models.FloatField(
        default=********,
        help_text="benchmark for overdraft. O means no overdraft. and amount above 0 is the amount of overdraft we are willing to go into"
    )

    kyc_visualisable_ids = models.JSONField(default=list, null=True, blank=True)
    kyc_admin_numbers = models.JSONField(default=list, null=True, blank=True)

    def __str__(self):
        return str(self.id)

    def clean(self):
        if self.agent_transfer_fee_value + self.ro_transfer_fee_value > self.send_money_transfer_extra_fee:
            raise ValidationError('Agent and RO Transfer Fee Value cannot be grater than send_money_transfer_extra_fee')

    ##################################################################################
    # Calculate Constants

    def save(self, *args, **kwargs):

        # liberty_cash_out_fee = self.liberty_cash_out_fee
        # liberty_cash_out_fee_band_two = self.liberty_cash_out_fee_band_two
        # liberty_cash_out_fee_band_three = self.liberty_cash_out_fee_band_three
        # nibbs_cash_out_fee = self.nibbs_cash_out_fee
        # interswitch_cash_out_fee_value = self.interswitch_cash_out_fee_value
        # ro_cash_out_fee = self.ro_cash_out_fee
        # ro_cash_out_fee_value = self.ro_cash_out_fee_value
        # cash_back_cash_out_fee = self.cash_back_cash_out_fee
        #
        # for band in self.charge_band.all():
        #     if band.transaction_type == "SEND_MONEY":
        #         transaction_charge = self.send_money_transfer_fee + self.send_money_transfer_extra_fee
        #         band.transaction_charge_value = transaction_charge
        #         band.save()
        #
        # if self.effect_cash_out_pricing:
        #
        #     for charge in self.cash_out_charges.filter(band__lte=1):
        #         # Calculate Agent Cash Value
        #         # if charge.upper_limit >= 5000 and charge.upper_limit <= 8000:
        #         #     # charge.agent_charge_value = (9000/100) * liberty_cash_out_fee
        #         #     charge.agent_charge_value = float("{:.2f}".format((9000/100) * liberty_cash_out_fee))
        #
        #         if charge.lower_limit > 19000:
        #             charge.agent_charge_value = float("{:.2f}".format((20000 / 100) * liberty_cash_out_fee))
        #
        #         else:
        #             rounded_num = ((charge.upper_limit // 1000) * 1000)
        #
        #             charge.agent_charge_value = float("{:.2f}".format((rounded_num / 100) * liberty_cash_out_fee))
        #
        #         # Calculate Provider Cash Value
        #         if charge.upper_limit > 8000:
        #             charge.provider_charge_value = interswitch_cash_out_fee_value
        #         else:
        #             charge.provider_charge_value = float("{:.2f}".format((charge.upper_limit / 100) * nibbs_cash_out_fee))
        #
        #         # Calculate Other Values
        #         charge.first_liberty_profit = float("{:.2f}".format(charge.agent_charge_value - charge.provider_charge_value))
        #
        #         # Calculate RO Profit
        #         if charge.upper_limit > 4000:
        #             charge.ro_profit = ro_cash_out_fee_value
        #         else:
        #             charge.ro_profit = float("{:.2f}".format((charge.first_liberty_profit / 100) * ro_cash_out_fee))
        #
        #         # Calculate Second Liberty Profit
        #         charge.second_liberty_profit = float("{:.2f}".format(charge.first_liberty_profit - charge.ro_profit))
        #
        #         # Calculate Agent Profit
        #         charge.agent_profit = float("{:.3f}".format((charge.second_liberty_profit / 100) * cash_back_cash_out_fee))
        #
        #         # Calculate Liberty Profit
        #         charge.liberty_profit = float("{:.3f}".format(charge.second_liberty_profit - charge.agent_profit))
        #
        #         # Calculate Cash Back Up to Percent
        #         calculated_cash_back_percent = float("{:.2f}".format((charge.agent_profit / charge.agent_charge_value) * 100))
        #         charge.cash_back_up_to_percent = f"{calculated_cash_back_percent}%"
        #
        #         charge.save()
        #
        #     self.effect_cash_out_pricing = False
        #
        # if self.effect_cash_out_pricing_band_two:
        #     for charge in self.cash_out_charges.filter(band__gte=2, band__lt=3):
        #         # Calculate Agent Cash Value
        #         if charge.lower_limit > 19000:
        #             charge.agent_charge_value = float("{:.2f}".format((20000 / 100) * liberty_cash_out_fee_band_two))
        #
        #         else:
        #             rounded_num = ((charge.upper_limit // 1000) * 1000)
        #             charge.agent_charge_value = float("{:.2f}".format((rounded_num / 100) * liberty_cash_out_fee_band_two))
        #
        #         # Calculate Provider Cash Value
        #         if charge.upper_limit > 8000:
        #             charge.provider_charge_value = interswitch_cash_out_fee_value
        #         else:
        #             charge.provider_charge_value = float("{:.2f}".format((charge.upper_limit / 100) * nibbs_cash_out_fee))
        #
        #         # Calculate Other Values
        #         charge.first_liberty_profit = float("{:.2f}".format(charge.agent_charge_value - charge.provider_charge_value))
        #
        #         # Calculate RO Profit
        #         if charge.upper_limit > 4000:
        #             charge.ro_profit = ro_cash_out_fee_value
        #         else:
        #             charge.ro_profit = float("{:.2f}".format((charge.first_liberty_profit / 100) * ro_cash_out_fee))
        #
        #         # Calculate Second Liberty Profit
        #         charge.second_liberty_profit = float("{:.2f}".format(charge.first_liberty_profit - charge.ro_profit))
        #
        #         # Calculate Agent Profit
        #         charge.agent_profit = float("{:.3f}".format((charge.second_liberty_profit / 100) * cash_back_cash_out_fee))
        #
        #         # Calculate Liberty Profit
        #         charge.liberty_profit = float("{:.3f}".format(charge.second_liberty_profit - charge.agent_profit))
        #
        #         # Calculate Cash Back Up to Percent
        #         calculated_cash_back_percent = float("{:.2f}".format((charge.agent_profit / charge.agent_charge_value) * 100))
        #         charge.cash_back_up_to_percent = f"{calculated_cash_back_percent}%"
        #
        #         charge.save()
        #
        #     self.effect_cash_out_pricing_band_two = False
        #
        # if self.effect_cash_out_pricing_band_three:
        #     for charge in self.cash_out_charges.filter(band__gte=3, band__lt=4):
        #         # Calculate Agent Cash Value
        #         if charge.lower_limit > 19000:
        #             charge.agent_charge_value = float("{:.2f}".format((20000 / 100) * liberty_cash_out_fee_band_three))
        #
        #         else:
        #             rounded_num = ((charge.upper_limit // 1000) * 1000)
        #             charge.agent_charge_value = float("{:.2f}".format((rounded_num / 100) * liberty_cash_out_fee_band_three))
        #
        #         # Calculate Provider Cash Value
        #         if charge.upper_limit > 8000:
        #             charge.provider_charge_value = interswitch_cash_out_fee_value
        #         else:
        #             charge.provider_charge_value = float("{:.2f}".format((charge.upper_limit / 100) * nibbs_cash_out_fee))
        #
        #         # Calculate Other Values
        #         charge.first_liberty_profit = float("{:.2f}".format(charge.agent_charge_value - charge.provider_charge_value))
        #
        #         # Calculate RO Profit
        #         if charge.upper_limit > 4000:
        #             charge.ro_profit = ro_cash_out_fee_value
        #         else:
        #             charge.ro_profit = float("{:.2f}".format((charge.first_liberty_profit / 100) * ro_cash_out_fee))
        #
        #         # Calculate Second Liberty Profit
        #         charge.second_liberty_profit = float("{:.2f}".format(charge.first_liberty_profit - charge.ro_profit))
        #
        #         # Calculate Agent Profit
        #         charge.agent_profit = float("{:.3f}".format((charge.second_liberty_profit / 100) * cash_back_cash_out_fee))
        #
        #         # Calculate Liberty Profit
        #         charge.liberty_profit = float("{:.3f}".format(charge.second_liberty_profit - charge.agent_profit))
        #
        #         # Calculate Cash Back Up to Percent
        #         calculated_cash_back_percent = float("{:.2f}".format((charge.agent_profit / charge.agent_charge_value) * 100))
        #         charge.cash_back_up_to_percent = f"{calculated_cash_back_percent}%"
        #
        #         charge.save()
        #
        #     self.effect_cash_out_pricing_band_three = False
        #
        # else:
        #     pass
        #
        # cache_key = 'constant_table_instance_v1'
        # cache.delete(cache_key)
        super(ConstantTable, self).save(*args, **kwargs)

    @classmethod
    def get_constant_table_instance(cls):
        from django.core.cache import cache

        """ "
        This function always returns an instance of the constant table
        """
        cache_key = 'constant_table_instance_v1'  # Versioned cache key
        constant_instance = cache.get(cache_key)

        if constant_instance is None:
            get_multiplier = cls.objects.filter(is_active=True)
            if get_multiplier:
                constant_instance = get_multiplier.latest("last_updated")
            else:
                constant_instance = cls.objects.latest("last_updated")

            # Cache the result with the versioned cache key
            cache.set(cache_key, constant_instance)

        return constant_instance

        # get_multiplier = cls.objects.filter(is_active=True)
        # if get_multiplier:
        #     constant_instance = get_multiplier.latest("last_updated")
        # else:
        #     constant_instance = cls.objects.latest("last_updated")

    @staticmethod
    def default_account_provider() -> str:
        return ConstantTable.get_constant_table_instance().account_provider

    @staticmethod
    def default_bills_payment_provider() -> str:
        return ConstantTable.get_constant_table_instance().bill_payment_provider

    @staticmethod
    def get_provider_fee(from_provider_type=None):
        if from_provider_type == "WOVEN":
            return ConstantTable.get_constant_table_instance().woven_fee
        elif from_provider_type == "VFD":
            return ConstantTable.get_constant_table_instance().vfd_fee
        else:
            return ConstantTable.get_constant_table_instance().default_provider_fee

    @staticmethod
    def get_sales_rep_commission(sales_rep_user):
        response = {
            "transfer": ConstantTable.get_constant_table_instance().sales_rep_comm_transfer,
            "cash_out": ConstantTable.get_constant_table_instance().sales_rep_comm_cash_out
        }

        return response

    @staticmethod
    def calculate_send_money_transaction_charge(user: User, amount=None) -> float:
        constant_instance = ConstantTable.get_constant_table_instance()
        # charge_bands = ChargeBand.objects.filter(Q(constant=constant_instance) & Q(transaction_type="SEND_MONEY"))
        # # print(constant_instance.charge_band())
        # for inst in charge_bands:
        #     if inst.lower_limit <= amount < inst.upper_limit:
        #         transaction_charge = (amount/100) * inst.transaction_charge_percent + inst.transaction_charge_value
        #         break
        # else:
        #     transaction_charge = 30

        # return transaction_charge

        if user.type_of_user in ["AGENT", "LOTTO_AGENT", "MERCHANT"]:
            if user.trans_band <= 1:
                transaction_charge = constant_instance.send_money_transfer_fee + constant_instance.send_money_transfer_extra_fee

            elif user.trans_band == 2:
                transaction_charge = constant_instance.send_money_transfer_fee_band_two + constant_instance.send_money_transfer_extra_fee

            elif user.trans_band > 2 and user.trans_band <= 3:
                transaction_charge = constant_instance.send_money_transfer_fee_band_three + constant_instance.send_money_transfer_extra_fee

            else:
                transaction_charge = constant_instance.send_money_transfer_fee + constant_instance.send_money_transfer_extra_fee
        else:
            transaction_charge = constant_instance.send_money_transfer_fee + constant_instance.send_money_transfer_extra_fee

        return transaction_charge

    # @staticmethod
    # def calculate_card_withdraw_transaction_charge_agent(amount) -> float:
    #     constant_instance = ConstantTable.get_constant_table_instance()
    #     charge_bands = ChargeBand.objects.filter(Q(constant=constant_instance) & Q(transaction_type="CARD_WITHDRAW_AGENT"))
    #     print(charge_bands)
    #     for inst in charge_bands:
    #         if inst.lower_limit <= amount < inst.upper_limit:
    #             transaction_charge = (amount/100) * inst.transaction_charge_percent + inst.transaction_charge_value
    #     else:
    #         transaction_charge = 50

    #     return transaction_charge

    @staticmethod
    def calculate_card_withdraw_transaction_charge_agent(amount) -> float:
        transaction_charge_percent = float(settings.CARD_WITHDRAW_AGENT_CHARGE)
        # amount_div = amount/100

        # if amount_div < 1:
        #     amount_div = 1

        # if 1 <= amount < 20000:
        #     start = 0
        #     interval = 1000

        #     for _ in

        if amount == 0:
            charge = 0

        elif 1 <= amount <= 1000:
            charge = 5

        elif 1001 <= amount <= 2000:
            charge = 10

        elif 2001 <= amount <= 3000:
            charge = 15

        elif 3001 <= amount <= 4000:
            charge = 20

        elif 4001 <= amount <= 5000:
            charge = 25

        elif 5001 <= amount <= 9000:
            charge = 45

        elif 9001 <= amount <= 10000:
            charge = 50

        elif 10001 <= amount <= 11000:
            charge = 55

        elif 11001 <= amount <= 12000:
            charge = 60

        elif 12001 <= amount <= 13000:
            charge = 65

        elif 13001 <= amount <= 14000:
            charge = 70

        elif 14001 <= amount <= 15000:
            charge = 75

        elif 15001 <= amount <= 16000:
            charge = 80

        elif 16001 <= amount <= 17000:
            charge = 85

        elif 17001 <= amount <= 18000:
            charge = 90

        elif 18001 <= amount <= 19000:
            charge = 95

        elif 19001 <= amount <= 20000:
            charge = 100

        else:
            charge = 100

        # elif 1 <= amount <= 4999:
        #     charge = amount/100 * transaction_charge_percent

        # elif 1 <= amount <= 1000:
        #     charge = 1000/100 * transaction_charge_percent

        # elif 1001 <= amount <= 2000:
        #     charge = 2000/100 * transaction_charge_percent

        # elif 2001 <= amount <= 3000:
        #     charge = 3000/100 * transaction_charge_percent

        # elif 3001 <= amount <= 4000:
        #     charge = 4000/100 * transaction_charge_percent

        # elif 4001 <= amount <= 5000:
        #     charge = 5000/100 * transaction_charge_percent

        # elif 5001 <= amount <= 6000:
        #     charge = 6000/100 * transaction_charge_percent

        # elif 6001 <= amount <= 7000:
        #     charge = 7000/100 * transaction_charge_percent

        # elif 7001 <= amount <= 8000:
        #     charge = 8000/100 * transaction_charge_percent

        # elif 8001 <= amount <= 9000:
        #     charge = 9000/100 * transaction_charge_percent

        # elif 9001 <= amount <= 10000:
        #     charge = 10000/100 * transaction_charge_percent

        # elif 10001 <= amount <= 11000:
        #     charge = 11000/100 * transaction_charge_percent

        # elif 11001 <= amount <= 12000:
        #     charge = 12000/100 * transaction_charge_percent

        # elif 12001 <= amount <= 13000:
        #     charge = 13000/100 * transaction_charge_percent

        # elif 13001 <= amount <= 14000:
        #     charge = 14000/100 * transaction_charge_percent

        # elif 14001 <= amount <= 15000:
        #     charge = 15000/100 * transaction_charge_percent

        # elif 15001 <= amount <= 16000:
        #     charge = 16000/100 * transaction_charge_percent

        # elif 16001 <= amount <= 17000:
        #     charge = 17000/100 * transaction_charge_percent

        # elif 17001 <= amount <= 18000:
        #     charge = 18000/100 * transaction_charge_percent

        # elif 18001 <= amount <= 19000:
        #     charge = 19000/100 * transaction_charge_percent

        # elif 19001 <= amount <= 20000:
        #     charge = 20000/100 * transaction_charge_percent

        # elif amount > 20000:
        #     charge = 100

        # if amount_div < 1:
        #     amount_div = 1

        # if amo

        # charge = amount_div * transaction_charge_percent

        # if charge > 100:
        #     transaction_charge = 100
        # else:
        #     transaction_charge = charge

        transaction_charge = charge
        return transaction_charge

    # @staticmethod
    # def calculate_card_withdraw_transaction_charge_merchant(amount) -> float:
    #     constant_instance = ConstantTable.get_constant_table_instance()
    #     charge_bands = ChargeBand.objects.filter(Q(constant=constant_instance) & Q(transaction_type="CARD_WITHDRAW_MERCHANT"))
    #     print(charge_bands)
    #     for inst in charge_bands:
    #         if inst.lower_limit <= amount < inst.upper_limit:
    #             charge = (amount/100) * inst.transaction_charge_percent + inst.transaction_charge_value
    #             if charge > 1000:
    #                 transaction_charge = 1000
    #             else:
    #                 transaction_charge = charge
    #     else:
    #         transaction_charge = 50

    #     return transaction_charge

    @staticmethod
    def calculate_card_withdraw_transaction_charge_merchant(amount) -> float:
        # transaction_charge_percent = float(settings.CARD_WITHDRAW_MERCHANT_CHARGE)
        transaction_charge_percent = ConstantTable.get_constant_table_instance().merchant_cash_out_percent
        amount_div = amount / 100

        if amount_div < 1:
            amount_div = 1

        charge = amount_div * transaction_charge_percent
        if charge > 1000:
            transaction_charge = 1000
        else:
            transaction_charge = charge

        return transaction_charge

    @classmethod
    def calculate_fund_bank_transfer_fees(cls, user: User, amount=float, exlude_other_charges: bool = False):
        fund_bank_transfer_fee = 0
        if exlude_other_charges is False:
            if user.type_of_user in ["AGENT"]:
                fund_bank_transfer_fee += ConstantTable.get_constant_table_instance().fund_bank_transfer_fee

        # Exclude staff_agents and dmo_agents from stamp duty
        # if amount >= 10000 and user.type_of_user not in ["STAFF_AGENT", "DMO_AGENT"]:
        #     fund_bank_transfer_fee += 50  # Electronic Money Transfer Levy

        return fund_bank_transfer_fee

    @staticmethod
    def calculate_transfer_limit(kyc_level, user) -> float:
        if kyc_level == 1:
            trans_limit = (
                ConstantTable.get_constant_table_instance().kyc_one_transfer_limit
            )
        elif kyc_level == 2:
            trans_limit = (
                    ConstantTable.get_constant_table_instance().kyc_two_transfer_limit + user.added_trans_limit
            )
        elif kyc_level == 3:
            trans_limit = (
                    ConstantTable.get_constant_table_instance().kyc_three_transfer_limit + user.added_trans_limit
            )
        else:
            trans_limit = (
                    ConstantTable.get_constant_table_instance().kyc_zero_transfer_limit + user.added_trans_limit
            )
        return trans_limit

    # def save(self, *args, **kwargs):

    #     if self.collection_account_provider == self.WOVEN_PROVIDER:
    #         AccountSystem.fetch_collection_account(self.WOVEN_PROVIDER)

    # if self.account_provider == self.PROVIDUS_PROVIDER:
    #     choose_account_provider_on_save(self, self.providus_account.all())

    # else:
    #     self.accountNumber = None
    #     self.accountName = None
    #     self.bankName = None

    # super(User, self).save(*args, **kwargs)


class AvailableBalance(models.Model):
    float_balance = models.FloatField()
    commission_balance = models.FloatField()
    whisper_balance = models.FloatField()
    is_active = models.BooleanField(default=True)
    start_index = models.PositiveIntegerField(default=0)
    batch_size = models.PositiveIntegerField(default=70)
    use_batch = models.BooleanField(default=False)
    comm_use_batch = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class ChargeBand(models.Model):
    SEND_MONEY = "SEND_MONEY"
    CARD_WITHDRAW_AGENT = "CARD_WITHDRAW_AGENT"
    CARD_WITHDRAW_MERCHANT = "CARD_WITHDRAW_MERCHANT"

    TRANSACTION_TYPE = [
        (SEND_MONEY, "SEND_MONEY"),
        (CARD_WITHDRAW_AGENT, "CARD_WITHDRAW_AGENT"),
        (CARD_WITHDRAW_MERCHANT, "CARD_WITHDRAW_MERCHANT")
    ]

    constant = models.ForeignKey(
        ConstantTable, related_name="charge_band", on_delete=models.CASCADE
    )
    transaction_type = models.CharField(max_length=200, choices=TRANSACTION_TYPE)
    lower_limit = models.FloatField()
    upper_limit = models.FloatField()
    transaction_charge_percent = models.FloatField()
    transaction_charge_value = models.FloatField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    # def save(self, *args, **kwargs):

    #     if self.transaction_type == "SEND_MONEY":
    #         constant_instance = ConstantTable.get_constant_table_instance()

    #         transaction_charge = constant_instance.send_money_transfer_fee + constant_instance.send_money_transfer_extra_fee

    #         self.transaction_charge_value = transaction_charge

    #     super(ChargeBand, self).save(*args, **kwargs)


##########################################################################################
# KYC


# class KYC(models.Model):
#     user = models.OneToOneField(
#         User, related_name="check_kycc", on_delete=models.CASCADE
#     )
#     is_kyc_level_one = models.BooleanField(default=False)
#     is_kyc_level_two = models.BooleanField(default=False)

#     @staticmethod
#     def check_user_kyc_level(user) -> int:
#         """
#         Check user KYC level
#         """

#         if user.check_kyc.is_kyc_level_one and not user.check_kyc.is_kyc_level_two:
#             # KYC LEVEL 1
#             return 1
#         elif user.check_kyc.is_kyc_level_two:
#             # KYC LEVEL 2
#             return 2
#         else:
#             # No KYC
#             return 0

#     def save(self, *args, **kwargs):
#         if self.is_kyc_level_two == True:
#             print(self.user.send_money_status)
#             self.user.send_money_status = True
#             self.user.save()
#         elif self.is_kyc_level_one == False and self.is_kyc_level_two == False:
#             self.user.send_money_status = False
#             self.user.save()
#         super(KYC, self).save(*args, **kwargs)

#     # @staticmethod
#     # def check_user_kyc_level_two(user):
#     #     """
#     #     Check if user has completed KYC level two
#     #     """
#     #     return user.check_kyc.nin_rel.is_complete or user.check_kyc.drivers_rel.is_complete


# # FINAL KYC METAMAP
# # class FaceMatch(models.Model):

# #     bvn_number = models.CharField(max_length=50, unique=True)
# #     bvnLastName = models.CharField(max_length=250, null=True, blank=True)
# #     bvnFirstName = models.CharField(max_length=250, null=True, blank=True)
# #     nameMatch = models.BooleanField(default=False)
# #     email = models.EmailField(null=True, blank=True)
# #     bvn_complete = models.BooleanField(default=False)


# # class KYCLEVELTWO(models.Model):
# #     NO_ID = 'NULL'
# #     DRIVERS_LICENSE = 'DRIVERS'
# #     NIN = 'NIN'
# #     VOTERS_CARD = 'VOTERS'
# #     ID_TYPES = [
# #         (NO_ID, 'NULL'),
# #         (DRIVERS_LICENSE, 'PROVIDUS'),
# #         (NIN, 'WOVEN')
# #         (VOTERS_CARD, 'WOVEN')
# #     ]
# #     user = models.ForeignKey(
# #         User, related_name="chech_kyc_two", on_delete=models.DO_NOTHING
# #     )

# #     document_id = models.CharField(max_length=50, unique=True)
# #     bvnLastName = models.CharField(max_length=250, null=True, blank=True)
# #     bvnFirstName = models.CharField(max_length=250, null=True, blank=True)
#     nameMatch = models.BooleanField(default=False)
#     email = models.EmailField(null=True, blank=True)
#     bvn_complete = models.BooleanField(default=False)
#     date_completed = models.DateTimeField(auto_now_add=True)


#########################################################################################


class OldEmailDump(models.Model):
    user = models.ForeignKey(User, related_name="old_emails", on_delete=models.CASCADE)
    last_email = models.EmailField()
    date_added = models.DateTimeField(auto_now_add=True)


class UnregisteredPhoneNumber(models.Model):
    phone_number = models.CharField(max_length=50, unique=True)
    date_added = models.DateTimeField(auto_now_add=True)


class RawResponses(models.Model):
    tag = models.CharField(max_length=100)
    data = models.TextField()
    extras = models.CharField(max_length=200)


# class HorizonPayTable(models.Model):
#     payload = models.TextField()
#     date = models.DateTimeField(auto_now_add=True)


# WHITELISTS
class Whitelist(models.Model):
    service_name = models.CharField(max_length=100, default="LIBERTY")
    host = models.CharField(max_length=100)
    list_of_hosts = models.JSONField(default=list)
    is_active = models.BooleanField(default=True)
    date_added = models.DateTimeField(auto_now_add=True)


# SECURITY QUESTIONS
class ResetPinStorage(models.Model):
    LOGIN_PIN_RESET = "LOGIN_PIN_RESET"

    RESET_PIN_TYPE = [
        (LOGIN_PIN_RESET, "LOGIN_PIN_RESET"),
    ]

    user = models.ForeignKey(User, related_name="hashed_codes", on_delete=models.CASCADE)
    type_of_reset = models.CharField(max_length=200, choices=RESET_PIN_TYPE)
    hashed_pin = models.CharField(max_length=700)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class SMSRecord(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    receiver = models.CharField(max_length=100)
    message = models.TextField(null=True, blank=True)
    is_sent = models.BooleanField(default=False)
    payload = models.TextField(null=True, blank=True)
    response_payload = models.TextField(null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return str(self.receiver)


class UnsentSMS(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    wallet_type = models.CharField(max_length=100, null=True, blank=True)
    charge = models.FloatField(null=True, blank=True)
    fail_reason = models.CharField(max_length=200, null=True, blank=True)
    sms_unsent_raw_data = models.TextField(null=True, blank=True)
    is_resent = models.BooleanField(default=False)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class OtherServiceDetail(models.Model):
    service_name = models.CharField(max_length=150)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)
    wallet_id = models.CharField(max_length=150, null=True, blank=True)
    can_gen_super_token = models.BooleanField(default=False)
    ip_addresses = models.JSONField(default=list)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return str(self.service_name
                   )


class TrackUserClick(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    view_or_screen = models.CharField(max_length=150)
    no_of_clicks = models.PositiveBigIntegerField(default=0)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.user.email} - {self.view_or_screen}"

    # def save(self, *args, **kwargs):

    #     self.no_of_clicks += 1

    #     super(TrackUserClick, self).save(*args, **kwargs)

    @classmethod
    def track_clicks_accross(cls, user, view):
        track_click, created = cls.objects.get_or_create(user=user, view_or_screen=view)
        track_click.no_of_clicks += 1
        track_click.save()


class CallbackSystem(models.Model):
    TRANSACTION_TYPE_CHOICES = settings.TRANSACTION_TYPE_CHOICES

    EXTRA_TRANSACTION_TYPE_CHOICES = (
        ("ALL", "ALL"),
        ("NONE", "NONE"),
    )

    ALL_TRANSACTION_TYPE_CHOICES = EXTRA_TRANSACTION_TYPE_CHOICES + TRANSACTION_TYPE_CHOICES

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    transaction_type = models.CharField(max_length=150, choices=ALL_TRANSACTION_TYPE_CHOICES)
    other_transaction_type = models.CharField(max_length=150, null=True, blank=True)
    url = models.CharField(max_length=500)
    headers = models.TextField(default={
        "Content-Type": "application/json", "Authorization": "Bearer "
    })
    last_response = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        if self.transaction_type == "NONE":
            if self.other_transaction_type is None:
                trans_type = "NONE"
            else:
                trans_type = self.other_transaction_type

            return f"{self.user.email} - {trans_type}"
        else:
            return f"{self.user.email} - {self.transaction_type}"

    def clean(self):
        if self.transaction_type == "NONE" and self.other_transaction_type is None:
            raise ValidationError('Transaction Type and Other Transaction Type Cannot be None')

        if self.transaction_type != "NONE" and self.other_transaction_type is not None:
            raise ValidationError('Either Transaction Type is NONE and Other Transaction type is filled')

    def save(self, *args, **kwargs):
        if self.headers:
            if self.headers.startswith('{'):
                from horizon_pay.helpers.helper_function import encrypt_trans_pin
                json_form = json.dumps(self.headers)
                get_encrypted_header = encrypt_trans_pin(json_form)
                self.headers = get_encrypted_header
            else:
                pass
        else:
            pass

        super(CallbackSystem, self).save(*args, **kwargs)

    @classmethod
    def send_callback(cls, user, transaction_type, payload, transaction_instance=None):

        get_callback_obj = cls.objects.filter(user=user) \
            .filter(Q(transaction_type=transaction_type) | Q(other_transaction_type=transaction_type)) \
            .last()

        log_info("entered here too")

        # payload = eval(payload)

        # payload["transaction_type"] = transaction_type

        if get_callback_obj:
            push_out = trigger_callback_send_out(
                url=get_callback_obj.url,
                payload=payload,
                headers=get_callback_obj.headers
            )

            get_callback_obj.last_response = push_out
            get_callback_obj.save()

            if transaction_instance:
                transaction_instance.callback_response = push_out
                if push_out["status"] == "sent":
                    transaction_instance.callback_sent = True
                else:
                    pass
                transaction_instance.save()

        else:
            push_out = None

        return push_out


class DeliveryAddressData(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    state = models.CharField(max_length=400, blank=True, null=True)
    lga = models.CharField(max_length=400, blank=True, null=True)
    nearest_landmark = models.CharField(max_length=400, blank=True, null=True)
    street = models.CharField(max_length=400, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    is_primary = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @property
    def full_address(self):
        return f"{self.street} {self.nearest_landmark} {self.lga} {self.state}"

    def clean(self):
        if self.is_primary == True and DeliveryAddressData.objects.filter(is_primary=True).count() > 0:
            raise ValidationError('cannot add more than one primary address')


class UserFlag(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    suspended_or_unsuspended_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name="userflag_suspender", null=True, blank=True)
    alert_message = models.TextField(blank=True, null=True)
    lotto_suspended = models.BooleanField(default=False)
    lotto_unsuspended = models.BooleanField(default=False)
    suspended = models.BooleanField(default=False)
    unsuspended = models.BooleanField(default=False)
    system_task = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def create_suspension_instance(cls, user, reason, is_suspended=None, suspended_by=None):
        system_task = False

        if suspended_by is None:
            system_task = True

        if is_suspended == True:
            suspended = True
            unsuspended = False


        elif is_suspended == False:
            unsuspended = True
            suspended = False

        else:
            suspended = False
            unsuspended = False

        user_flag = cls.objects.create(
            user=user,
            alert_message=reason,
            suspended=suspended,
            unsuspended=unsuspended,
            suspended_or_unsuspended_by=suspended_by,
            system_task=system_task
        )

        return True

    @staticmethod
    def unsuspend_user(user, reason, request):
        """
        This Method is used to remove suspension from users
        """
        user.is_suspended = False
        user.suspension_reason = reason
        user.save(custom_request=request)

        user_flag = UserFlag.create_suspension_instance(
            user=user,
            reason=reason,
            is_suspended=False,
            suspended_by=request.user
        )

        return True


class Blacklist(models.Model):
    LIST_TYPE_CHOICES = (
        ("ACCOUNT_NUMBERS", "ACCOUNT_NUMBERS"),
        # ("ACCOUNT_NUMBERS", "ACCOUNT_NUMBERS"),
    )

    list_type = models.CharField(max_length=200, choices=LIST_TYPE_CHOICES)
    content = models.JSONField(default=list)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def get_blacklist(cls, list_type):
        cache_key = f"blacklist_{list_type}"
        blacklist = cache.get(cache_key)
        if blacklist is None:
            blacklist = cls.objects.filter(list_type=list_type).last()
            cache.set(cache_key, blacklist)

        return blacklist


class UserWhitelist(models.Model):
    LIST_TYPE_CHOICES = (
        ("INFLOW_COUNT", "INFLOW_COUNT"),
    )

    list_type = models.CharField(max_length=200, choices=LIST_TYPE_CHOICES)
    content = models.JSONField(default=list)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class DeliverySystem(models.Model):
    DELIVERY_OBJECT_TYPE_CHOICES = (
        ("CARD", "CARD"),
        ("TERMINAL", "CARD"),
    )

    DELIVERY_STATUS_CHOICES = (
        ("NOT_PACKAGED", "NOT_PACKAGED"),
        ("PACKAGED", "PACKAGED"),
        ("SENT_OUT", "SENT_OUT"),
        ("DELIVERED", "DELIVERED"),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    delivery_address = models.ForeignKey(DeliveryAddressData, on_delete=models.CASCADE)
    delivery_object = models.CharField(max_length=200, choices=DELIVERY_OBJECT_TYPE_CHOICES)
    delivery_status = models.CharField(max_length=200, choices=DELIVERY_STATUS_CHOICES, default="NOT_PACKAGED")
    date_sent_out = models.DateTimeField(null=True, blank=True)
    date_delivered = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def create_delivery_instance(cls, user, delivery_address_id, delivery_object):
        try:
            delivery_address_id = int(delivery_address_id)
            find_address = DeliveryAddressData.objects.filter(id=delivery_address_id).last()
            if find_address:

                cls.objects.create(
                    user=user,
                    delivery_address=find_address,
                    delivery_object=delivery_object
                )
            else:
                log_info("address not found")
                return False

            return True

        except Exception as err:
            log_info(str(err))
            return False


class AgentSupervisor(models.Model):
    supervisor = models.ForeignKey(User, on_delete=models.CASCADE)
    agents = models.ManyToManyField(User, related_name='gentsundersupervisor')
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class UserOtherAccount(models.Model):
    owner = models.ForeignKey(User, on_delete=models.CASCADE)
    other_account = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_other_account')
    added_num = models.PositiveIntegerField(validators=[MinValueValidator(0)])
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def get_new_email(cls, user: User):
        num_to_add = 1
        while True:
            log_debug("-----------------------------------------------------------")
            log_debug("-----------------------------------------------------------")
            log_debug("-----------------------------------------------------------")
            log_info("num_to_add: ", num_to_add)
            log_info("num_to_add: ", num_to_add)
            log_debug("-----------------------------------------------------------")
            log_debug("-----------------------------------------------------------")
            log_debug("-----------------------------------------------------------")
            num_to_add = user.num_of_other_accounts + num_to_add
            email_username, domain = user.email.split('@')
            new_username = f"{email_username}+{num_to_add}"
            new_email = f"{new_username}@{domain}"

            log_info("new_email: ", new_email)
            if not User.objects.filter(email=new_email).exists():
                return new_email, new_username, num_to_add

            num_to_add += 1

    @classmethod
    def generate_random_password(cls):
        import random

        random_number = random.randint(100000, 999999)
        random_string = str(random_number).zfill(6)

        log_info(str(random_string))
        return random_string

    @classmethod
    def create_other_account(cls, user: User, phone_number=None, password=None, start_num="2341"):
        try:
            # if cls.objects.filter(other_account=user).exists():
            #     return {
            #         "status": False,
            #         "message": "This user is a subaccount and is not allowed to have multiple accounts"
            #     }, None, None

            new_email, new_username, num_added = cls.get_new_email(user=user)

            if phone_number is None:
                new_phone_number = generate_random_phone_number(start_num=start_num)
                # new_phone_number = f"{user.phone_number}+{num_added}"
            else:
                new_phone_number = User.format_number_from_back_add_234(phone_number=phone_number)
                if new_phone_number is None:
                    return {
                               "status": False,
                               "message": "Entered Phone number is incorrect"
                           }, None, None

            if password is None:
                new_password = cls.generate_random_password()
            else:
                new_password = password

            # Check if user bvn is not watchlisted
            if suspend_watchlisted_bvn_user(user=user):
                return {
                    "status": False,
                    "message": "BVN is watchlisted, and account suspended. Please contact admin"
                }, None, None

            new_user = User.objects.create(
                username=new_username,
                phone_number=new_phone_number,
                email=new_email,
                first_name=user.first_name,
                last_name=user.last_name,
                state=user.state,
                lga=user.lga,
                nearest_landmark=user.nearest_landmark,
                street=user.street,
                type_of_user=user.type_of_user,
                business_name=user.business_name,
                marital_status=user.marital_status,
                gender=user.gender,
                has_login_pin=True,
                master_bvn=False,
                registration_email_otp=user.registration_email_otp,
                first_security_question=user.first_security_question,
                first_security_answer=user.first_security_answer,
                second_security_question=user.second_security_question,
                second_security_answer=user.second_security_answer,
                registration_email_verified=user.registration_email_verified,
                sms_subscription=False,
                mobile_suspended=True if user.type_of_user == "LIBERTY_RETAIL" else False
            )

            new_user.set_password(new_password)
            new_user.save()

            user.num_of_other_accounts += 1
            user.save()

            cls.objects.create(
                owner=user,
                other_account=new_user,
                added_num=num_added
            )

            # Perform image verification for sub account
            from kyc_app.models import UserImage
            user_images = UserImage.objects.filter(user=user)
            if user_images.exists():
                user_image = user_images.last()
                UserImage.objects.create(
                    user=new_user,
                    bvn_number=user_image.bvn_number,
                    snapshot=user_image.snapshot,
                    bvn_photo=user_image.bvn_photo,
                    app_photo=user_image.app_photo,
                    bvn_payload=user_image.bvn_payload,
                    verification_response=user_image.verification_response,
                    is_match=user_image.is_match
                )

            if user.type_of_user == "LOTTO_AGENT":
                SuperAgentProfile.objects.create(agent=new_user)

            bvn_rel = user.check_kyc.bvn_rel
            docs_rel = user.check_kyc.docsface_rel

            new_user_bvn_rel = new_user.check_kyc.bvn_rel
            new_user_docs_rel = new_user.check_kyc.docsface_rel

            new_user_bvn_rel.bvn_number = bvn_rel.bvn_number
            new_user_bvn_rel.bvn_phone_number = bvn_rel.bvn_phone_number
            new_user_bvn_rel.bvn_phone_number_2 = bvn_rel.bvn_phone_number_2
            new_user_bvn_rel.bvn_first_name = bvn_rel.bvn_first_name
            new_user_bvn_rel.bvn_middle_name = bvn_rel.bvn_middle_name
            new_user_bvn_rel.bvn_last_name = bvn_rel.bvn_last_name
            new_user_bvn_rel.bvn_birthdate = bvn_rel.bvn_birthdate
            new_user_bvn_rel.date_added = datetime.now()
            new_user_bvn_rel.payload = bvn_rel.payload
            new_user_bvn_rel.verified_by = bvn_rel.verified_by
            new_user_bvn_rel.is_verified = bvn_rel.is_verified
            new_user_bvn_rel.channel = bvn_rel.channel

            new_user_bvn_rel.save()

            new_user_docs_rel.is_verified = docs_rel.is_verified
            new_user_docs_rel.resource_id = docs_rel.resource_id
            new_user_docs_rel.resource_url = docs_rel.resource_url
            new_user_docs_rel.channel = docs_rel.channel
            new_user_docs_rel.id_type = docs_rel.id_type
            new_user_docs_rel.id_sub_type = docs_rel.id_sub_type
            new_user_docs_rel.front_view_url = docs_rel.front_view_url
            new_user_docs_rel.back_view_url = docs_rel.back_view_url
            new_user_docs_rel.selfie_url = docs_rel.selfie_url
            new_user_docs_rel.sprite_url = docs_rel.sprite_url
            new_user_docs_rel.video_url = docs_rel.video_url
            new_user_docs_rel.payload = docs_rel.payload
            new_user_docs_rel.date_added = datetime.now()

            new_user_docs_rel.save()

            return {
                       "status": True,
                       "new_email": new_email,
                       "new_phone_number": new_phone_number,
                       "new_username": new_username,
                       "new_password": new_password,
                   }, new_user_bvn_rel, new_user

        except Exception as err:
            return {
                       "status": False,
                       "message": f"Other Error: {err}"
                   }, None, None


class CorporateAccount(models.Model):
    onboarded_by = models.ForeignKey(User, related_name="corporate_onboard_by", on_delete=models.CASCADE)
    user = models.ForeignKey(User, related_name="corporate_user", on_delete=models.CASCADE)
    other_users = models.ManyToManyField(User, related_name='related_entities', blank=True)
    entity_type = models.CharField(max_length=100)
    rc_number = models.CharField(max_length=100)
    company_name = models.CharField(max_length=100)
    incorp_date = models.CharField(max_length=100)
    bvn = models.CharField(max_length=100)
    corporate_id = models.CharField(max_length=100, default=create_corporate_id, editable=False)
    added_acc_num = models.PositiveSmallIntegerField(default=0)
    raw_incorp_date = models.DateField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.email


class UserTempToken(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    pin_type = models.CharField(max_length=150)
    token = models.CharField(max_length=250)
    exp = models.DateTimeField()
    last_success_req = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


# class RegisteredDevice(models.Model):

#     DEVICE_STATUS_CHOICES = (
#         ("PRIMARY", "PRIMARY"),
#         ("SECONDARY", "SECONDARY"),
#     )
#     user = models.ForeignKey(User, on_delete=models.CASCADE)
#     device_status = models.CharField(max_length=150, choices=DEVICE_STATUS_CHOICES, default="PRIMARY")
#     device_type = models.CharField(max_length=150, choices=DEVICE_TYPE_CHOICES)
#     device_token = models.CharField(max_length=300)
#     last_gen_pin = models.CharField(max_length=500, null=True, blank=True)
#     date_created = models.DateTimeField(auto_now_add=True)
#     last_updated = models.DateTimeField(auto_now=True)


#     # def save(self, *args, **kwargs):

#     #     if self.dev == self.WOVEN_PROVIDER:
#     #         AccountSystem.fetch_collection_account(self.WOVEN_PROVIDER)

#     #     if self.account_provider == self.PROVIDUS_PROVIDER:
#     #         choose_account_provider_on_save(self, self.providus_account.all())

#     #     else:
#     #         self.accountNumber = None
#     #         self.accountName = None
#     #         self.bankName = None

#     #     super(User, self).save(*args, **kwargs)


class DisplayBanner(models.Model):
    title = models.CharField(max_length=100)
    image = models.ImageField(upload_to="banners/", storage=CustomMediaStorage())
    seen_by = models.JSONField(default=list, blank=True, null=True)
    display_count = models.IntegerField(default=1)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    date_created = models.DateTimeField(auto_now_add=True)


class UserDataDeletionRequest(models.Model):
    user_email = models.EmailField(max_length=300)
    # reason_for_deletion = models.CharField(max_length=500, blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class AjoAgentForm(models.Model):
    gender_choices = [
        ("male", "Male"),
        ("female", "Female")
    ]
    ajo_agent_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=100)
    email = models.EmailField(blank=True, null=True)
    gender = models.CharField(max_length=100, choices=gender_choices)
    above_eighteen = models.BooleanField(default=True)
    building_no = models.CharField(max_length=100)
    street = models.TextField()
    nearest_landmark = models.CharField(max_length=100)
    city = models.CharField(max_length=100)
    lga = models.CharField(max_length=150)
    state = models.CharField(max_length=100)
    bvn = models.CharField(max_length=100, blank=True, null=True)
    nin = models.CharField(max_length=100, blank=True, null=True)
    id_payload = models.TextField(blank=True, null=True)
    id_verified = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.phone_number


class TerminalRetrievalRequest(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    phone_number = models.CharField(max_length=100)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.phone_number


class Region(models.Model):
    name = models.CharField(max_length=100)
    regional_head = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    active = models.BooleanField(default=True)

    def __str__(self):
        return str(self.name)


class NewLocationList(models.Model):
    location = models.CharField(max_length=300)
    region = models.ForeignKey(Region, on_delete=models.SET_NULL, blank=True, null=True)
    supervisor = models.ForeignKey(User, on_delete=models.SET_NULL, related_name="new_location_supervisor", blank=True, null=True)
    other_supervisors = models.ManyToManyField(User, through='BranchTeam', related_name="branch_team")
    sub_location = models.CharField(max_length=300, null=True, blank=True)
    state = models.CharField(max_length=150)
    sub_location_num = models.PositiveSmallIntegerField(default=0)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.location} - {'None' if not self.sub_location else self.sub_location} - {self.state}"

    @classmethod
    def handle_liberty_location_creation(cls, location: str, ):
        get_former_numbers = cls.objects.filter(location=location.upper())

        if get_former_numbers:
            new_count = get_former_numbers.count() + 1

            while get_former_numbers.filter(sub_location_num=new_count).exists():
                new_count += 1
        else:
            new_count = 1

        return new_count

    def save(self, custom_request=None, *args, **kwargs):
        self.location = self.location.upper()
        self.sub_location = self.sub_location.upper() if self.sub_location else self.sub_location

        if not self.pk:
            self.created_by = custom_request.user if custom_request is not None else None

            # self.sub_location_num += 1

        # self.sub_location_num = self.__class__.handle_liberty_location_creation(location=self.location)

        super(NewLocationList, self).save(*args, **kwargs)


class BranchTeam(models.Model):
    branch = models.ForeignKey(NewLocationList, on_delete=models.CASCADE)
    supervisor = models.ForeignKey(User, related_name="branch_other_supervisor", on_delete=models.CASCADE)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('branch', 'supervisor')

    def __str__(self) -> str:
        return f"Supervisor - ID - {self.supervisor.id} | {self.supervisor.email} | {self.branch.location} | {self.branch.sub_location if self.branch.sub_location else 'No Sub Location'}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        user_team, created = UserTeam.objects.get_or_create(team=self)


class UserTeam(models.Model):
    team = models.OneToOneField(BranchTeam, on_delete=models.CASCADE, unique=True)
    users = models.ManyToManyField(User, related_name="user_team")
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    # class Meta:
    #     unique_together = ('team', 'users')

    def __str__(self) -> str:
        return f"{self.team.branch.location} - {self.team.supervisor.email}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        if self.users:
            for user in self.users.all():
                user: User
                user.user_branch = self.team.branch
                user.save()


# class TransactionTime(models.Model):

#     transaction = models.ForeignKey(User, on_delete=models.SET_NULL, related_name="new_location_supervisor", blank=True, null=True)
#     transaction_type = models.CharField(max_length=300)
#     sub_location_num = models.PositiveSmallIntegerField(default=0)
#     created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
#     date_created = models.DateTimeField(auto_now_add=True)
#     last_updated = models.DateTimeField(auto_now=True)

#     def __str__(self) -> str:
#         return self.location


class IssueLog(models.Model):
    """
    Model to track issues and their resolutions related to users
    """
    user = models.ForeignKey(
        'User',
        on_delete=models.CASCADE,
        related_name='issue_logs',
        help_text="User who the issue is about"
    )

    description = models.TextField(
        help_text="Description of the issue and its resolution"
    )
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Issue Log - {self.user.email} - {self.date_created}"

    class Meta:

        ordering = ['-date_created']


class MerchantAcquisitionOfficer(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    merchants = models.ManyToManyField(User, related_name="aquisition_merchant")
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.email


class ApplicationBanner(models.Model):
    name = models.CharField(max_length=200)
    is_active = models.BooleanField(default=False)
    image_url = models.TextField(blank=True, null=True)
    location = models.CharField(max_length=100, blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name
    

class BVNWatchlist(models.Model):

    CATEGORY_CHOICES = [
        ('1', 'Breach in Monetary Value'),
        ('2', 'Double Listed'),
        ('3', 'Money Laundering'),
        ('4', 'Terrorism Financing'),
        ('5', 'Identity Theft'),
        ('99', 'Fraud'),
    ]

    bvn = models.CharField(max_length=11, unique=True, db_index=True)
    first_name = models.CharField(max_length=100)
    other_name = models.CharField(max_length=100, blank=True, null=True)
    surname = models.CharField(max_length=100)
    category = models.CharField(max_length=50, choices=CATEGORY_CHOICES, default='1')
    date_watchlisted = models.DateField(blank=True, null=True)
    requesting_bank = models.CharField(max_length=200, blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "BVN Watchlist"
        verbose_name_plural = "BVN Watchlists"
        ordering = ['-date_watchlisted', '-date_created']
        indexes = [
            models.Index(fields=['bvn']),
            models.Index(fields=['date_watchlisted']),
            models.Index(fields=['category']),
            models.Index(fields=['first_name', 'surname']),
        ]

    def __str__(self):
        return f"{self.bvn} - {self.first_name} {self.surname}"

    @property
    def full_name(self):
        names = [self.first_name]
        if self.other_name:
            names.append(self.other_name)
        names.append(self.surname)
        return ' '.join(names)

    @classmethod
    def is_bvn_watchlisted(cls, bvn):
        return cls.objects.filter(bvn=bvn).exists()

    @classmethod
    def get_watchlist_info(cls, bvn):
        try:
            return cls.objects.get(bvn=bvn)
        except cls.DoesNotExist:
            return None


class UserVersionTracker(models.Model):
    """
    Model to track version information sent by users during login
    against the current backend version
    """
    email = models.EmailField()
    user_version = models.CharField(max_length=20, help_text="Version sent by user in login payload")
    backend_version = models.CharField(max_length=20, help_text="Current backend version from ConstantTable")
    device_type = models.CharField(max_length=20, blank=True, null=True, help_text="Device type from login payload")
    serial_no = models.CharField(max_length=350, blank=True, null=True, help_text="Serial number from login payload")
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "User Version Tracker"
        verbose_name_plural = "User Version Trackers"
        ordering = ['-date_created']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['date_created']),
            models.Index(fields=['user_version']),
            models.Index(fields=['backend_version']),
        ]

    def __str__(self):
        return f"{self.email} - User: {self.user_version} | Backend: {self.backend_version}"

    @property
    def version_match(self):
        """Check if user version matches backend version"""
        return self.user_version == self.backend_version

    @property
    def is_outdated(self):
        """Check if user version is older than backend version"""
        try:
            from packaging import version as pkg_version
            return pkg_version.parse(self.user_version) < pkg_version.parse(self.backend_version)
        except:
            return False


class ApplicationBannerClick(models.Model):
    """
        Banner Clicks.
        We want to record number of ApplicationBanner Click per user
    """
    banner = models.ForeignKey(ApplicationBanner, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    click_count = models.PositiveIntegerField(default=0)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.banner.name) + " clicked by " + str(self.user.email)


class AddedTransactionLimitLog(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    amount_added = models.FloatField(default=0.00)
    old_limit = models.FloatField(default=0.00)
    new_limit = models.FloatField(default=0.00)
    created_at = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.email} - {self.amount}"



