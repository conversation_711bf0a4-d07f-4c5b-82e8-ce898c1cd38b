"""
Utility functions for merchant reward system integration.
These functions handle the integration between the reward system and transaction processing.
"""

from decimal import Decimal
from django.utils import timezone
from main.models import MerchantRewardSystem, MerchantDailyReward
from main.helper.logging_utils import log_info, log_warning, log_error


def check_and_use_free_transaction(merchant_user, transaction_amount=None):
    """
    Check if a merchant has available free transactions and use one if available.
    
    Args:
        merchant_user: User object with type_of_user='MERCHANT'
        transaction_amount: Optional transaction amount for validation
    
    Returns:
        dict: {
            'free_transaction_used': bool,
            'remaining_free_transactions': int,
            'reward_system_active': bool,
            'message': str
        }
    """
    try:
        # Check if merchant has an active reward system
        try:
            reward_system = merchant_user.reward_system
        except MerchantRewardSystem.DoesNotExist:
            return {
                'free_transaction_used': False,
                'remaining_free_transactions': 0,
                'reward_system_active': False,
                'message': 'No reward system configured for this merchant'
            }
        
        if not reward_system.is_active:
            return {
                'free_transaction_used': False,
                'remaining_free_transactions': 0,
                'reward_system_active': False,
                'message': 'Reward system is not active for this merchant'
            }
        
        # Check if transaction meets minimum amount requirement
        if transaction_amount and transaction_amount < reward_system.minimum_transaction_amount:
            available_free = reward_system.available_free_transactions
            return {
                'free_transaction_used': False,
                'remaining_free_transactions': available_free,
                'reward_system_active': True,
                'message': f'Transaction amount below minimum threshold of {reward_system.minimum_transaction_amount}'
            }
        
        # Try to use a free transaction
        free_transaction_used = reward_system.use_free_transaction()
        remaining_free = reward_system.available_free_transactions
        
        if free_transaction_used:
            log_info(
                f"Free transaction used for merchant {merchant_user.email}",
                "MERCHANT_FREE_TRANSACTION_USED",
                {
                    'merchant_email': merchant_user.email,
                    'transaction_amount': str(transaction_amount) if transaction_amount else 'N/A',
                    'remaining_free_transactions': remaining_free
                }
            )
            
            return {
                'free_transaction_used': True,
                'remaining_free_transactions': remaining_free,
                'reward_system_active': True,
                'message': f'Free transaction used. {remaining_free} remaining.'
            }
        else:
            return {
                'free_transaction_used': False,
                'remaining_free_transactions': remaining_free,
                'reward_system_active': True,
                'message': 'No free transactions available'
            }
    
    except Exception as e:
        log_error(
            f"Error checking free transactions for merchant {merchant_user.email}: {str(e)}",
            "MERCHANT_FREE_TRANSACTION_ERROR",
            {
                'merchant_email': merchant_user.email,
                'transaction_amount': str(transaction_amount) if transaction_amount else 'N/A'
            },
            include_traceback=True
        )
        
        return {
            'free_transaction_used': False,
            'remaining_free_transactions': 0,
            'reward_system_active': False,
            'message': f'Error checking free transactions: {str(e)}'
        }


def get_merchant_reward_status(merchant_user):
    """
    Get comprehensive reward status for a merchant.
    
    Args:
        merchant_user: User object with type_of_user='MERCHANT'
    
    Returns:
        dict: Comprehensive reward status information
    """
    try:
        # Check if merchant has a reward system
        try:
            reward_system = merchant_user.reward_system
        except MerchantRewardSystem.DoesNotExist:
            return {
                'has_reward_system': False,
                'is_active': False,
                'message': 'No reward system configured'
            }
        
        # Get current performance
        current_performance = reward_system.current_performance
        
        # Get available free transactions
        available_free = reward_system.available_free_transactions
        
        # Get recent reward history (last 7 days)
        recent_rewards = MerchantDailyReward.objects.filter(
            merchant=merchant_user,
            evaluation_date__gte=timezone.now().date() - timezone.timedelta(days=7)
        ).order_by('-evaluation_date')
        
        # Calculate recent performance stats
        recent_stats = {
            'total_days': recent_rewards.count(),
            'targets_met': recent_rewards.filter(target_met=True).count(),
            'total_free_transactions_earned': sum(r.free_transactions_awarded for r in recent_rewards),
            'success_rate': 0
        }
        
        if recent_stats['total_days'] > 0:
            recent_stats['success_rate'] = (recent_stats['targets_met'] / recent_stats['total_days']) * 100
        
        return {
            'has_reward_system': True,
            'is_active': reward_system.is_active,
            'target_transactions_per_day': reward_system.target_transactions_per_day,
            'reward_free_transactions': reward_system.reward_free_transactions,
            'minimum_transaction_amount': reward_system.minimum_transaction_amount,
            'reward_expiry_days': reward_system.reward_expiry_days,
            'available_free_transactions': available_free,
            'current_performance': current_performance,
            'recent_stats': recent_stats,
            'recent_rewards': [
                {
                    'date': r.evaluation_date,
                    'transactions_completed': r.transactions_completed,
                    'target_met': r.target_met,
                    'free_transactions_awarded': r.free_transactions_awarded,
                    'free_transactions_remaining': r.free_transactions_remaining
                }
                for r in recent_rewards
            ]
        }
    
    except Exception as e:
        log_error(
            f"Error getting reward status for merchant {merchant_user.email}: {str(e)}",
            "MERCHANT_REWARD_STATUS_ERROR",
            {'merchant_email': merchant_user.email},
            include_traceback=True
        )
        
        return {
            'has_reward_system': False,
            'is_active': False,
            'error': str(e)
        }


def create_or_update_merchant_reward_system(merchant_user, target_transactions=10, 
                                          reward_free_transactions=5, 
                                          minimum_amount=100.00,
                                          expiry_days=30,
                                          is_active=True,
                                          created_by=None):
    """
    Create or update a merchant reward system.
    
    Args:
        merchant_user: User object with type_of_user='MERCHANT'
        target_transactions: Daily transaction target
        reward_free_transactions: Number of free transactions to award
        minimum_amount: Minimum transaction amount
        expiry_days: Days before free transactions expire
        is_active: Whether the system is active
        created_by: User who created/updated the system
    
    Returns:
        MerchantRewardSystem: The created or updated reward system
    """
    try:
        reward_system, created = MerchantRewardSystem.objects.get_or_create(
            merchant=merchant_user,
            defaults={
                'target_transactions_per_day': target_transactions,
                'reward_free_transactions': reward_free_transactions,
                'minimum_transaction_amount': Decimal(str(minimum_amount)),
                'reward_expiry_days': expiry_days,
                'is_active': is_active,
                'created_by': created_by
            }
        )
        
        if not created:
            # Update existing system
            reward_system.target_transactions_per_day = target_transactions
            reward_system.reward_free_transactions = reward_free_transactions
            reward_system.minimum_transaction_amount = Decimal(str(minimum_amount))
            reward_system.reward_expiry_days = expiry_days
            reward_system.is_active = is_active
            reward_system.save()
        
        action = "created" if created else "updated"
        log_info(
            f"Merchant reward system {action} for {merchant_user.email}",
            f"MERCHANT_REWARD_SYSTEM_{action.upper()}",
            {
                'merchant_email': merchant_user.email,
                'target_transactions': target_transactions,
                'reward_free_transactions': reward_free_transactions,
                'minimum_amount': str(minimum_amount),
                'is_active': is_active,
                'created_by': created_by.email if created_by else None
            }
        )
        
        return reward_system
    
    except Exception as e:
        log_error(
            f"Error creating/updating reward system for merchant {merchant_user.email}: {str(e)}",
            "MERCHANT_REWARD_SYSTEM_ERROR",
            {
                'merchant_email': merchant_user.email,
                'target_transactions': target_transactions,
                'reward_free_transactions': reward_free_transactions
            },
            include_traceback=True
        )
        raise


def bulk_create_merchant_reward_systems(merchant_users, target_transactions=10,
                                       reward_free_transactions=5,
                                       minimum_amount=100.00,
                                       expiry_days=30,
                                       is_active=True,
                                       created_by=None):
    """
    Bulk create reward systems for multiple merchants.
    
    Args:
        merchant_users: List of User objects with type_of_user='MERCHANT'
        target_transactions: Daily transaction target
        reward_free_transactions: Number of free transactions to award
        minimum_amount: Minimum transaction amount
        expiry_days: Days before free transactions expire
        is_active: Whether the systems are active
        created_by: User who created the systems
    
    Returns:
        dict: Summary of creation results
    """
    results = {
        'created': 0,
        'updated': 0,
        'errors': 0,
        'error_details': []
    }
    
    for merchant_user in merchant_users:
        try:
            reward_system = create_or_update_merchant_reward_system(
                merchant_user=merchant_user,
                target_transactions=target_transactions,
                reward_free_transactions=reward_free_transactions,
                minimum_amount=minimum_amount,
                expiry_days=expiry_days,
                is_active=is_active,
                created_by=created_by
            )
            
            # Check if it was created or updated
            if hasattr(reward_system, '_created'):
                results['created'] += 1
            else:
                results['updated'] += 1
                
        except Exception as e:
            results['errors'] += 1
            results['error_details'].append({
                'merchant_email': merchant_user.email,
                'error': str(e)
            })
    
    log_info(
        f"Bulk merchant reward system creation completed",
        "MERCHANT_REWARD_BULK_CREATE",
        {
            'total_merchants': len(merchant_users),
            'created': results['created'],
            'updated': results['updated'],
            'errors': results['errors'],
            'created_by': created_by.email if created_by else None
        }
    )
    
    return results


def calculate_commission_with_rewards(merchant_user, transaction_amount, base_commission_rate):
    """
    Calculate commission for a transaction, considering free transaction rewards.
    
    Args:
        merchant_user: User object with type_of_user='MERCHANT'
        transaction_amount: Transaction amount
        base_commission_rate: Base commission rate (as decimal, e.g., 0.015 for 1.5%)
    
    Returns:
        dict: {
            'commission_amount': Decimal,
            'free_transaction_used': bool,
            'original_commission': Decimal,
            'commission_saved': Decimal,
            'remaining_free_transactions': int
        }
    """
    original_commission = Decimal(str(transaction_amount)) * Decimal(str(base_commission_rate))
    
    # Check for free transaction
    free_transaction_result = check_and_use_free_transaction(merchant_user, transaction_amount)
    
    if free_transaction_result['free_transaction_used']:
        return {
            'commission_amount': Decimal('0.00'),
            'free_transaction_used': True,
            'original_commission': original_commission,
            'commission_saved': original_commission,
            'remaining_free_transactions': free_transaction_result['remaining_free_transactions']
        }
    else:
        return {
            'commission_amount': original_commission,
            'free_transaction_used': False,
            'original_commission': original_commission,
            'commission_saved': Decimal('0.00'),
            'remaining_free_transactions': free_transaction_result['remaining_free_transactions']
        }
