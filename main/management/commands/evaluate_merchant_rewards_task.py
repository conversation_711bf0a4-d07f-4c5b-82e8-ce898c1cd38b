"""
Django management command to evaluate merchant daily rewards.
This command should be run daily at 12:01 AM via cron to evaluate
the previous day's merchant performance and award free transactions.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from django.db.models import Sum, Count, Avg
from decimal import Decimal

from main.models import MerchantRewardSystem, MerchantDailyReward, User
from accounts.models import Transaction
from main.helper.logging_utils import log_info, log_error, log_warning


class Command(BaseCommand):
    help = 'Evaluate merchant daily rewards and award free transactions'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--date',
            type=str,
            help='Specific date to evaluate (YYYY-MM-DD format). Defaults to yesterday.',
        )
        parser.add_argument(
            '--merchant-email',
            type=str,
            help='Evaluate rewards for a specific merchant only',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force re-evaluation even if already processed',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.force = options['force']
        
        # Determine evaluation date
        if options['date']:
            try:
                evaluation_date = timezone.datetime.strptime(options['date'], '%Y-%m-%d').date()
            except ValueError:
                self.stdout.write(
                    self.style.ERROR('Invalid date format. Use YYYY-MM-DD.')
                )
                return
        else:
            # Default to yesterday
            evaluation_date = timezone.now().date() - timezone.timedelta(days=1)
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('Running in DRY-RUN mode - no changes will be made'))
        
        self.stdout.write(f'Evaluating merchant rewards for date: {evaluation_date}')
        
        try:
            # Get merchants to evaluate
            if options['merchant_email']:
                merchants = self._get_specific_merchant(options['merchant_email'])
            else:
                merchants = self._get_active_merchants()
            
            if not merchants:
                self.stdout.write(self.style.WARNING('No merchants found for evaluation'))
                return
            
            self.stdout.write(f'Found {len(merchants)} merchants to evaluate')
            
            # Process each merchant
            results = {
                'processed': 0,
                'targets_met': 0,
                'rewards_awarded': 0,
                'total_free_transactions': 0,
                'errors': 0,
                'skipped': 0
            }
            
            for merchant in merchants:
                try:
                    result = self._evaluate_merchant(merchant, evaluation_date)
                    results['processed'] += 1
                    
                    if result['target_met']:
                        results['targets_met'] += 1
                        results['rewards_awarded'] += 1
                        results['total_free_transactions'] += result['free_transactions_awarded']
                    
                    if result['skipped']:
                        results['skipped'] += 1
                    
                    # Display progress
                    status = "✓ TARGET MET" if result['target_met'] else "✗ Target not met"
                    if result['skipped']:
                        status = "⚠ SKIPPED"
                    
                    self.stdout.write(
                        f"  {merchant.email:30s} | "
                        f"Transactions: {result['transactions_completed']:3d}/{result['target']:3d} | "
                        f"Free awarded: {result['free_transactions_awarded']:2d} | "
                        f"{status}"
                    )
                    
                except Exception as e:
                    results['errors'] += 1
                    self.stdout.write(
                        self.style.ERROR(f"Error processing {merchant.email}: {str(e)}")
                    )
                    log_error(
                        f"Error evaluating merchant rewards: {str(e)}",
                        "MERCHANT_REWARD_EVALUATION_ERROR",
                        {
                            'merchant_email': merchant.email,
                            'evaluation_date': str(evaluation_date)
                        },
                        include_traceback=True
                    )
            
            # Display summary
            self._display_summary(results, evaluation_date)
            
            # Log overall results
            log_info(
                f"Merchant reward evaluation completed for {evaluation_date}",
                "MERCHANT_REWARD_EVALUATION_COMPLETE",
                {
                    'evaluation_date': str(evaluation_date),
                    'merchants_processed': results['processed'],
                    'targets_met': results['targets_met'],
                    'total_free_transactions_awarded': results['total_free_transactions'],
                    'errors': results['errors']
                }
            )
            
        except Exception as e:
            log_error(
                f"Critical error in merchant reward evaluation: {str(e)}",
                "MERCHANT_REWARD_EVALUATION_CRITICAL_ERROR",
                {'evaluation_date': str(evaluation_date)},
                include_traceback=True
            )
            raise

    def _get_active_merchants(self):
        """Get all merchants with active reward systems."""
        return User.objects.filter(
            type_of_user='MERCHANT',
            is_active=True,
            reward_system__is_active=True
        ).select_related('reward_system')

    def _get_specific_merchant(self, email):
        """Get a specific merchant by email."""
        try:
            merchant = User.objects.select_related('reward_system').get(
                email=email,
                type_of_user='MERCHANT'
            )
            return [merchant]
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Merchant with email {email} not found')
            )
            return []

    def _evaluate_merchant(self, merchant, evaluation_date):
        """Evaluate a single merchant's performance and award rewards."""
        
        # Check if already evaluated (unless force is specified)
        if not self.force:
            existing_reward = MerchantDailyReward.objects.filter(
                merchant=merchant,
                evaluation_date=evaluation_date
            ).first()
            
            if existing_reward:
                return {
                    'skipped': True,
                    'target_met': existing_reward.target_met,
                    'transactions_completed': existing_reward.transactions_completed,
                    'target': existing_reward.target_transactions,
                    'free_transactions_awarded': 0  # Already awarded
                }
        
        reward_system = merchant.reward_system
        
        # Get transaction statistics for the evaluation date
        transaction_stats = self._get_transaction_stats(merchant, evaluation_date, reward_system)
        
        # Determine if target was met
        target_met = transaction_stats['count'] >= reward_system.target_transactions_per_day
        
        # Calculate free transactions to award
        free_transactions_awarded = 0
        if target_met:
            free_transactions_awarded = reward_system.reward_free_transactions
        
        # Create or update the daily reward record
        if not self.dry_run:
            with transaction.atomic():
                daily_reward, created = MerchantDailyReward.objects.update_or_create(
                    merchant=merchant,
                    evaluation_date=evaluation_date,
                    defaults={
                        'transactions_completed': transaction_stats['count'],
                        'target_transactions': reward_system.target_transactions_per_day,
                        'minimum_amount': reward_system.minimum_transaction_amount,
                        'target_met': target_met,
                        'free_transactions_awarded': free_transactions_awarded,
                        'free_transactions_remaining': free_transactions_awarded,
                        'total_transaction_amount': transaction_stats['total_amount'],
                        'average_transaction_amount': transaction_stats['avg_amount'],
                        'evaluation_completed_at': timezone.now(),
                        'notes': f"Evaluated on {timezone.now().date()}"
                    }
                )
                
                if target_met:
                    log_info(
                        f"Merchant {merchant.email} met target: {transaction_stats['count']}/{reward_system.target_transactions_per_day}",
                        "MERCHANT_REWARD_TARGET_MET",
                        {
                            'merchant_email': merchant.email,
                            'evaluation_date': str(evaluation_date),
                            'transactions_completed': transaction_stats['count'],
                            'target': reward_system.target_transactions_per_day,
                            'free_transactions_awarded': free_transactions_awarded
                        }
                    )
        
        return {
            'skipped': False,
            'target_met': target_met,
            'transactions_completed': transaction_stats['count'],
            'target': reward_system.target_transactions_per_day,
            'free_transactions_awarded': free_transactions_awarded
        }

    def _get_transaction_stats(self, merchant, evaluation_date, reward_system):
        """Get transaction statistics for a merchant on a specific date."""
        
        # Query successful transactions for the evaluation date
        transactions = Transaction.objects.filter(
            user=merchant,
            date_created__date=evaluation_date,
            transaction_status='SUCCESSFUL',
            amount__gte=reward_system.minimum_transaction_amount
        )
        
        # Calculate statistics
        stats = transactions.aggregate(
            count=Count('id'),
            total_amount=Sum('amount'),
            avg_amount=Avg('amount')
        )
        
        return {
            'count': stats['count'] or 0,
            'total_amount': stats['total_amount'] or Decimal('0.00'),
            'avg_amount': stats['avg_amount'] or Decimal('0.00')
        }

    def _display_summary(self, results, evaluation_date):
        """Display evaluation summary."""
        self.stdout.write('\n' + '=' * 80)
        self.stdout.write('MERCHANT REWARD EVALUATION SUMMARY')
        self.stdout.write('=' * 80)
        self.stdout.write(f'Evaluation Date: {evaluation_date}')
        self.stdout.write(f'Merchants Processed: {results["processed"]}')
        self.stdout.write(f'Targets Met: {results["targets_met"]}')
        self.stdout.write(f'Rewards Awarded: {results["rewards_awarded"]}')
        self.stdout.write(f'Total Free Transactions: {results["total_free_transactions"]}')
        self.stdout.write(f'Errors: {results["errors"]}')
        self.stdout.write(f'Skipped (already processed): {results["skipped"]}')
        
        if results['processed'] > 0:
            success_rate = (results['targets_met'] / results['processed']) * 100
            self.stdout.write(f'Success Rate: {success_rate:.1f}%')
        
        self.stdout.write('=' * 80)
