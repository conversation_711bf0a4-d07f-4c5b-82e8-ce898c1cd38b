# Merchant Daily Reward System Documentation

## Overview

The Merchant Daily Reward System is a comprehensive solution that tracks merchant transaction performance and provides commission-free transaction rewards based on daily targets. The system runs automated daily evaluations and integrates seamlessly with the existing transaction processing infrastructure.

## System Components

### 1. Database Models

#### MerchantRewardSystem
- **Purpose**: Stores reward system configuration for individual merchants
- **Key Fields**:
  - `merchant`: OneToOne relationship with User (MERCHANT type)
  - `target_transactions_per_day`: Daily transaction threshold
  - `reward_free_transactions`: Number of free transactions awarded
  - `minimum_transaction_amount`: Minimum amount to count towards target
  - `reward_expiry_days`: Days before unused rewards expire
  - `is_active`: Enable/disable reward system

#### MerchantDailyReward
- **Purpose**: Tracks daily performance evaluations and reward awards
- **Key Fields**:
  - `merchant`: ForeignKey to User
  - `evaluation_date`: Date being evaluated (previous day)
  - `transactions_completed`: Actual transactions completed
  - `target_met`: Whether target was achieved
  - `free_transactions_awarded`: Free transactions granted
  - `free_transactions_remaining`: Unused free transactions

### 2. Management Commands

#### evaluate_merchant_rewards_task
**Purpose**: Daily evaluation of merchant performance and reward distribution

**Usage**:
```bash
# Evaluate yesterday's performance (default)
python manage.py evaluate_merchant_rewards_task

# Evaluate specific date
python manage.py evaluate_merchant_rewards_task --date 2024-01-15

# Evaluate specific merchant
python manage.py evaluate_merchant_rewards_task --merchant-email <EMAIL>

# Dry run (no changes)
python manage.py evaluate_merchant_rewards_task --dry-run

# Force re-evaluation
python manage.py evaluate_merchant_rewards_task --force
```

**Cron Configuration**:
```bash
# Add to crontab for daily execution at 12:01 AM
1 0 * * * /path/to/venv/bin/python /path/to/project/manage.py evaluate_merchant_rewards_task >> /var/log/merchant_rewards.log 2>&1
```

### 3. Celery Tasks

#### evaluate_merchant_rewards_daily_task
- **Purpose**: Automated daily evaluation via Celery
- **Schedule**: Daily at 12:01 AM
- **Features**: Retry logic, comprehensive logging, error handling

#### expire_merchant_free_transactions_task
- **Purpose**: Clean up expired free transactions
- **Schedule**: Daily
- **Features**: Automatic expiry based on reward_expiry_days

### 4. API Endpoints

#### GET /merchant/reward-status/
- **Purpose**: Get current reward status for authenticated merchant
- **Authentication**: Required (merchant only)
- **Response**: Reward configuration, current performance, available free transactions

#### GET /merchant/reward-history/
- **Purpose**: Get detailed reward history
- **Parameters**: `days`, `page`, `page_size`
- **Response**: Paginated reward history with summary statistics

#### GET /merchant/current-performance/
- **Purpose**: Real-time performance data for current day
- **Response**: Today's progress, time analysis, performance messages

## Business Logic

### Daily Evaluation Process

1. **Merchant Selection**: Query all active merchants with enabled reward systems
2. **Transaction Counting**: Count successful transactions from previous day above minimum amount
3. **Target Comparison**: Compare actual count against individual target threshold
4. **Reward Distribution**: Award free transactions if target is met or exceeded
5. **Record Keeping**: Create MerchantDailyReward record with detailed metrics

### Transaction Processing Integration

1. **Pre-Transaction Check**: Check if merchant has available free transactions
2. **Commission Calculation**: Skip commission if free transaction is used
3. **Balance Update**: Deduct one free transaction from merchant's balance
4. **Audit Logging**: Record usage for reporting and compliance

### Free Transaction Management

1. **Expiry Handling**: Automatic expiry based on reward_expiry_days setting
2. **FIFO Usage**: Oldest rewards are used first
3. **Balance Tracking**: Real-time balance updates and availability checks

## Admin Interface Features

### MerchantRewardSystem Admin
- **List View**: Shows targets, performance, and reward status
- **Bulk Actions**: Enable/disable rewards, update targets, reset balances
- **Form Validation**: Ensures proper configuration values
- **Performance Display**: Real-time current day performance

### MerchantDailyReward Admin
- **History View**: Complete reward evaluation history
- **Filtering**: By date, target status, merchant
- **Read-Only**: Prevents manual modification for audit integrity
- **Expiry Actions**: Manual expiry of unused transactions

## Integration Guide

### Transaction Processing Integration

```python
from main.helper.merchant_reward_utils import calculate_commission_with_rewards

# In your transaction processing code
commission_result = calculate_commission_with_rewards(
    merchant_user=merchant,
    transaction_amount=amount,
    base_commission_rate=0.015  # 1.5%
)

if commission_result['free_transaction_used']:
    # Skip commission charge
    commission_amount = Decimal('0.00')
    # Log the free transaction usage
else:
    # Apply normal commission
    commission_amount = commission_result['commission_amount']
```

### Reward Status Check

```python
from main.helper.merchant_reward_utils import get_merchant_reward_status

# Get comprehensive reward status
status = get_merchant_reward_status(merchant_user)

if status['has_reward_system'] and status['is_active']:
    available_free = status['available_free_transactions']
    current_performance = status['current_performance']
    # Use this data in your application
```

## Configuration

### Environment Variables
```bash
# Celery configuration for reward tasks
CELERY_BEAT_SCHEDULE_MERCHANT_REWARDS=True

# Logging configuration
MERCHANT_REWARD_LOG_LEVEL=INFO
```

### Django Settings
```python
# Add to INSTALLED_APPS if not already present
INSTALLED_APPS = [
    # ... other apps
    'main',
]

# Celery beat schedule
CELERY_BEAT_SCHEDULE = {
    'evaluate-merchant-rewards-daily': {
        'task': 'main.tasks.evaluate_merchant_rewards_daily_task',
        'schedule': crontab(hour=0, minute=1),  # 12:01 AM daily
    },
    'expire-merchant-free-transactions': {
        'task': 'main.tasks.expire_merchant_free_transactions_task',
        'schedule': crontab(hour=2, minute=0),  # 2:00 AM daily
    },
}
```

## Monitoring and Logging

### Log Categories
- **MERCHANT_REWARD_EVALUATION_START**: Daily evaluation begins
- **MERCHANT_REWARD_TARGET_MET**: Merchant meets daily target
- **MERCHANT_REWARD_EVALUATION_COMPLETE**: Daily evaluation completes
- **MERCHANT_FREE_TRANSACTION_USED**: Free transaction is used
- **MERCHANT_REWARD_EXPIRED**: Free transactions expire

### Monitoring Queries
```sql
-- Check daily evaluation status
SELECT evaluation_date, COUNT(*) as merchants_evaluated, 
       SUM(CASE WHEN target_met THEN 1 ELSE 0 END) as targets_met
FROM main_merchantdailyreward 
WHERE evaluation_date >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY evaluation_date
ORDER BY evaluation_date DESC;

-- Check free transaction usage
SELECT merchant_id, SUM(free_transactions_awarded) as total_awarded,
       SUM(free_transactions_remaining) as total_remaining
FROM main_merchantdailyreward
WHERE evaluation_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY merchant_id;
```

## Performance Considerations

### Database Optimization
- **Indexes**: Optimized indexes on frequently queried fields
- **Batch Processing**: Efficient bulk operations for large merchant sets
- **Query Optimization**: Select_related and prefetch_related usage

### Caching Strategy
```python
# Cache merchant reward status for frequently accessed data
from django.core.cache import cache

def get_cached_merchant_status(merchant_id):
    cache_key = f"merchant_reward_status_{merchant_id}"
    status = cache.get(cache_key)
    
    if status is None:
        status = get_merchant_reward_status(merchant)
        cache.set(cache_key, status, timeout=300)  # 5 minutes
    
    return status
```

## Troubleshooting

### Common Issues

1. **Daily Evaluation Not Running**
   - Check cron job configuration
   - Verify Celery worker status
   - Review task logs for errors

2. **Free Transactions Not Working**
   - Verify reward system is active
   - Check transaction amount meets minimum
   - Confirm free transactions are available

3. **Performance Issues**
   - Monitor database query performance
   - Check for missing indexes
   - Review transaction volume patterns

### Debug Commands
```bash
# Test specific merchant evaluation
python manage.py evaluate_merchant_rewards_task --merchant-email <EMAIL> --dry-run

# Check merchant reward status
python manage.py shell -c "
from main.helper.merchant_reward_utils import get_merchant_reward_status
from main.models import User
merchant = User.objects.get(email='<EMAIL>')
print(get_merchant_reward_status(merchant))
"
```

## Security Considerations

### Access Control
- **API Endpoints**: Merchant-only access with proper authentication
- **Admin Interface**: Admin-only access to reward configuration
- **Audit Trail**: Complete logging of all reward-related activities

### Data Protection
- **Sensitive Data**: Proper handling of merchant financial information
- **Audit Logging**: Comprehensive logging for compliance requirements
- **Data Retention**: Configurable retention policies for reward history

## Deployment Checklist

1. **Database Migration**: Run migrations for new models
2. **Cron Job Setup**: Configure daily evaluation cron job
3. **Celery Configuration**: Set up beat schedule for automated tasks
4. **Admin Access**: Ensure admin users can access reward management
5. **API Testing**: Verify merchant API endpoints work correctly
6. **Monitoring Setup**: Configure logging and monitoring alerts
7. **Performance Testing**: Test with expected merchant volumes

## Support and Maintenance

### Regular Maintenance Tasks
- **Weekly**: Review reward system performance and usage
- **Monthly**: Analyze merchant engagement and target achievement rates
- **Quarterly**: Review and optimize reward parameters based on business goals

### Backup and Recovery
- **Database Backups**: Include reward system tables in regular backups
- **Configuration Backup**: Backup reward system configurations
- **Recovery Testing**: Regular testing of backup and recovery procedures
