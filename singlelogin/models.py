from django.db import models
from django.core.exceptions import ValidationError
from django.conf import settings
from datetime import datetime, timedelta
from hashlib import pbkdf2_hmac
from accounts.helpers.helper_func import notify_admin_group
from django.contrib.auth import get_user_model

from main.models import UserFlag

User = get_user_model()



class BlackListedJWT(models.Model):
    date_created = models.DateTimeField(auto_now_add=True)
    user_id = models.PositiveIntegerField(db_index=True)
    blacklisted = models.BooleanField(default=False, db_index=True)
    login_type = models.CharField(max_length=150, null=True, blank=True)
    ip_addr = models.CharField(max_length=150, null=True, blank=True)
    token = models.CharField(max_length=1000, db_index=True)
    user_agent = models.TextField(null=True, blank=True)
    email = models.CharField(max_length=1000, null=True, blank=True)
    serial_no = models.Char<PERSON><PERSON>(max_length=1000, null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)


    @classmethod
    def hash_jwt(cls, init_token):
        salt = settings.SECRET_KEY
        iterations = 5000  # Application specific, read above.
        dk = pbkdf2_hmac('sha256', init_token.encode(), salt.encode()*2, iterations)
        return dk.hex()


    @classmethod
    def create_hashed_jwt(cls, user_id, init_token, ip_addr, user_agent, email=None, serial_no=None):
        list_of_exluded_user = BlacklistConstant.get_constant_table_instance()

        # get the current datetime
        now = datetime.now()

        # calculate the start and end of the current minute
        start = now.replace(second=0, microsecond=0)
        end = start + timedelta(seconds=30)

        login_num_of_times = 6
        login_num_of_times_exempted_users = 10

        user = User.objects.get(id=user_id)
        # Check if type_of_user is MERCHANT; and extent login number to 10
        if user.type_of_user == "MERCHANT":
            login_num_of_times = 10

        if cls.objects.exclude(user_id__in=list_of_exluded_user).filter(user_id=user_id, date_created__gte=start, date_created__lte=end).count() > login_num_of_times \
            or cls.objects.filter(user_id__in=list_of_exluded_user).filter(user_id=user_id, date_created__gte=start, date_created__lte=end).count() > login_num_of_times_exempted_users:

            if user_id in list_of_exluded_user:
                user_num_times = login_num_of_times_exempted_users
            else:
                user_num_times = login_num_of_times

            details = f"ALERTTTTTTTTTTT!!\n\nALERTTTTTTTTTTT!!\nUser with user ID: {user_id} tried to log in more than {user_num_times} times in 1 minute. He has now been suspended"
            try:
                suspend = False
                if user.type_of_user != "MERCHANT":
                    suspend = True

                    user = User.objects.get(id=int(user_id))
                    user.is_suspended = True
                    user.suspension_reason = details
                    user.save()

                UserFlag.create_suspension_instance(
                    user=user,
                    reason=details,
                    is_suspended=suspend
                )

            except:
                pass

            notify_admin_group(user=None, details=details, user_id=user_id, suspend=True)

            return False


        else:

            token = cls.hash_jwt(init_token=init_token)

            cls.objects.exclude(user_id__in=list_of_exluded_user).filter(user_id=user_id).update(
                blacklisted = True
            )

            login_date = datetime.now()
            login_type = "Logged in"

            new = cls.objects.create(
                user_id = user_id,
                token = token,
                login_type = login_type,
                ip_addr = ip_addr,
                user_agent = user_agent,
                email = email,
                serial_no = serial_no
            )


            return True



class BlacklistConstant(models.Model):
    whitelisted_ids = models.JSONField(default=list)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def clean(self):
        for i in self.whitelisted_ids:
            try:
                int(i)
            except:
                raise ValidationError('JSON elements must be integers')


    @classmethod
    def get_constant_table_instance(cls):
        """ "
        This function always returns an instance of the constant table
        """
        from django.core.cache import cache

        cache_key = 'blacklist_constant_table_instance_v1'  # Versioned cache key
        constant_instance = cache.get(cache_key)

        if constant_instance is None:
            get_multiplier = cls.objects.filter(is_active=True)
            if get_multiplier:
                constant_instance = get_multiplier.latest("last_updated").whitelisted_ids
            else:
                constant_instance = cls.objects.create().whitelisted_ids

            # Cache the result with the versioned cache key
            cache.set(cache_key, constant_instance)

        return constant_instance



