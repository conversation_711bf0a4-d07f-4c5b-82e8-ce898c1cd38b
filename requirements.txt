amqp==5.1.1
appdirs==1.4.4
asgiref==3.5.0
asttokens==3.0.0
async-timeout==4.0.2
atomicwrites==1.4.0
attrs==21.4.0
autopep8==2.0.2
billiard==3.6.4.0
boto3==1.24.75
botocore==1.27.75
CacheControl==0.12.11
cachetools==5.2.0
cattrs==22.1.0
celery==5.2.6
certifi==2021.10.8
cffi==1.15.0
cfgv==3.3.1
charset-normalizer==2.0.12
click==8.1.2
click-didyoumean==0.3.0
click-plugins==1.1.1
click-repl==0.2.0
colorama==0.4.4
contourpy==1.0.7
coreapi==2.3.3
coreschema==0.0.4
cryptocode==0.1
cryptography==36.0.1
cycler==0.11.0
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.13
diff-match-patch==20200713
distlib==0.3.4
Django==4.0.3
django-admin-rangefilter==0.13.2
django-celery-beat==2.3.0
django-celery-results==2.3.1
django-cors-headers==3.13.0
django-data-browser==4.2.10
django-debug-toolbar==4.1.0
django-extensions==3.2.3
django-filter==22.1
django-import-export==2.7.1
django-model-utils==4.2.0
django-redisboard==8.4.0
django-silk==5.0.3
django-storages==1.13.1
django-templated-mail==1.1.1
django-timezone-field==5.0
django-user-agents==0.4.0
djangorestframework==3.13.1
djangorestframework-simplejwt==4.8.0
djoser==2.1.0
docopt==0.6.2
drf-yasg==1.20.0
environs==9.5.0
et-xmlfile==1.1.0
exceptiongroup==1.0.0rc8
executing==2.2.0
facebook-sdk==3.1.0
Faker==12.0.1
filelock==3.7.0
firebase-admin==5.2.0
Flask==2.1.1
fonttools==4.39.2
future==0.18.2
fuzzywuzzy==0.18.0
gevent==23.7.0
google-api-core==2.8.2
google-api-python-client==2.54.0
google-auth==2.27.0
google-auth-httplib2==0.1.0
google-auth-oauthlib==1.2.0
google-cloud-core==2.3.2
google-cloud-firestore==2.6.0
google-cloud-storage==2.4.0
google-crc32c==1.3.0
google-resumable-media==2.3.3
googleapis-common-protos==1.56.4
gprof2dot==2022.7.29
greenlet==2.0.2
grequests==0.7.0
grpcio==1.47.0
grpcio-status==1.47.0
gspread==6.0.0
httplib2==0.20.4
hyperlink==21.0.0
identify==2.5.1
idna==3.3
importlib-metadata==4.11.3
importlib_resources==6.4.5
inflection==0.5.1
iniconfig==1.1.1
ipython==8.18.1
itsdangerous==2.1.2
itypes==1.2.0
jedi==0.19.2
Jinja2==3.0.3
jmespath==1.0.1
kiwisolver==1.4.4
kombu==5.2.4
MarkupPy==1.14
MarkupSafe==2.1.0
marshmallow==3.14.1
matplotlib==3.7.1
matplotlib-inline==0.1.7
mixer==7.2.2
mock==4.0.3
msgpack==1.0.4
nodeenv==1.6.0
num2words==0.5.12
numpy==1.23.2
oauthlib==3.2.0
odfpy==1.4.1
openpyxl==3.0.10
packaging==21.3
pandas==1.4.4
parso==0.8.4
pexpect==4.9.0
Pillow==9.1.1
pipenv==2022.5.2
platformdirs==2.5.2
pluggy==1.0.0
pre-commit==2.19.0
prompt_toolkit==3.0.50
proto-plus==1.20.6
protobuf==3.20.1
psycopg2-binary==2.9.3
ptyprocess==0.7.0
pure_eval==0.2.3
py==1.11.0
py-jwt-validator==0.6.0
pyasn1==0.4.8
pyasn1-modules==0.2.8
pycodestyle==2.10.0
pycparser==2.21
pycryptodome==3.15.0
pycryptodomex==3.15.0
Pygments==2.19.1
PyJWT==2.3.0
pyotp==2.9.0
pyparsing==3.0.7
pytest==7.1.2
pytest-django==4.5.2
pytest-watch==4.2.0
python-crontab==2.6.0
python-dateutil==2.8.2
python-decouple==3.6
python-dotenv==0.19.2
python-http-client==3.3.7
python-twitter==3.5
python3-openid==3.2.0
pytz==2021.3
pyxlsb==1.0.10
PyYAML==6.0
redis==4.3.3
requests==2.27.1
requests-cache==0.9.5
requests-oauthlib==1.3.1
rsa==4.9
ruamel.yaml==0.17.21
ruamel.yaml.clib==0.2.6
s3transfer==0.6.0
sendgrid==6.9.7
sentry-sdk==1.29.2
six==1.16.0
social-auth-app-django==4.0.0
social-auth-core==4.2.0
sqlparse==0.4.2
stack-data==0.6.3
starkbank-ecdsa==2.0.3
StrEnum==0.4.15
tablib==3.2.1
toml==0.10.2
tomli==2.0.1
traitlets==5.14.3
typing_extensions==4.12.2
tzdata==2022.1
ua-parser==0.16.1
uritemplate==4.1.1
url-normalize==1.4.3
urllib3==1.26.12
user-agents==2.2.0
vine==5.0.0
virtualenv==20.14.1
virtualenv-clone==0.5.7
watchdog==2.1.8
wcwidth==0.2.5
Werkzeug==2.1.1
wrapt==1.14.1
xlrd==2.0.1
xlwt==1.3.0
zipp==3.8.0
zope.event==5.0
zope.interface==6.0
